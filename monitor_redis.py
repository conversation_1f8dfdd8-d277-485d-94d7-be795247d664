#!/usr/bin/env python3
"""
Redis连接监控脚本
实时监控Redis服务器状态和连接情况
"""

import redis
import time
import json
from datetime import datetime
from config.settings import settings

def get_redis_client():
    """获取Redis客户端"""
    return redis.Redis(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        password=settings.REDIS_PASSWORD,
        db=settings.REDIS_DB,
        socket_connect_timeout=30,
        socket_timeout=60,
        socket_keepalive=True,
        decode_responses=True
    )

def monitor_redis_status():
    """监控Redis状态"""
    client = get_redis_client()
    
    print("=== Redis状态监控 ===")
    print("按 Ctrl+C 停止监控\n")
    
    try:
        while True:
            try:
                # 获取服务器信息
                info = client.info()
                
                # 提取关键指标
                stats = {
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'connected_clients': info.get('connected_clients', 0),
                    'blocked_clients': info.get('blocked_clients', 0),
                    'used_memory_human': info.get('used_memory_human', '0B'),
                    'used_memory_peak_human': info.get('used_memory_peak_human', '0B'),
                    'total_commands_processed': info.get('total_commands_processed', 0),
                    'instantaneous_ops_per_sec': info.get('instantaneous_ops_per_sec', 0),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0),
                    'uptime_in_seconds': info.get('uptime_in_seconds', 0),
                }
                
                # 计算命中率
                hits = stats['keyspace_hits']
                misses = stats['keyspace_misses']
                hit_rate = (hits / (hits + misses) * 100) if (hits + misses) > 0 else 0
                
                # 显示状态
                print(f"[{stats['timestamp']}]")
                print(f"  连接数: {stats['connected_clients']} | 阻塞: {stats['blocked_clients']}")
                print(f"  内存: {stats['used_memory_human']} (峰值: {stats['used_memory_peak_human']})")
                print(f"  QPS: {stats['instantaneous_ops_per_sec']} | 总命令: {stats['total_commands_processed']}")
                print(f"  命中率: {hit_rate:.2f}% | 运行时间: {stats['uptime_in_seconds']}秒")
                
                # 检查Celery任务
                celery_keys = client.keys('celery-task-meta-*')
                print(f"  Celery任务数: {len(celery_keys)}")
                
                # 检查队列状态
                queue_length = client.llen('excel_import_queue')
                print(f"  队列长度: {queue_length}")
                
                print("-" * 60)
                
                time.sleep(5)  # 每5秒更新一次
                
            except redis.ConnectionError as e:
                print(f"❌ Redis连接错误: {e}")
                time.sleep(10)
            except redis.TimeoutError as e:
                print(f"⚠️  Redis超时: {e}")
                time.sleep(5)
            except Exception as e:
                print(f"❌ 未知错误: {e}")
                time.sleep(5)
                
    except KeyboardInterrupt:
        print("\n监控已停止")

def check_celery_tasks():
    """检查Celery任务状态"""
    client = get_redis_client()
    
    print("=== Celery任务状态检查 ===")
    
    try:
        # 获取所有任务
        task_keys = client.keys('celery-task-meta-*')
        print(f"总任务数: {len(task_keys)}")
        
        if not task_keys:
            print("没有找到任务")
            return
        
        # 统计任务状态
        status_count = {}
        recent_tasks = []
        
        for key in task_keys:
            try:
                task_data = client.get(key)
                if task_data:
                    task_info = json.loads(task_data)
                    status = task_info.get('status', 'UNKNOWN')
                    status_count[status] = status_count.get(status, 0) + 1
                    
                    # 收集最近的任务
                    if len(recent_tasks) < 5:
                        task_id = key.replace('celery-task-meta-', '')
                        recent_tasks.append({
                            'task_id': task_id,
                            'status': status,
                            'date_done': task_info.get('date_done', 'N/A')
                        })
            except Exception as e:
                print(f"解析任务 {key} 失败: {e}")
        
        # 显示统计
        print("\n任务状态统计:")
        for status, count in status_count.items():
            print(f"  {status}: {count}")
        
        print("\n最近的任务:")
        for task in recent_tasks:
            print(f"  {task['task_id']}: {task['status']} ({task['date_done']})")
            
    except Exception as e:
        print(f"检查任务失败: {e}")

def clean_old_tasks():
    """清理旧任务"""
    client = get_redis_client()
    
    print("=== 清理旧任务 ===")
    
    try:
        task_keys = client.keys('celery-task-meta-*')
        print(f"找到 {len(task_keys)} 个任务")
        
        if not task_keys:
            print("没有任务需要清理")
            return
        
        # 询问是否清理
        response = input("是否清理所有旧任务? (y/N): ")
        if response.lower() != 'y':
            print("取消清理")
            return
        
        # 删除任务
        deleted_count = 0
        for key in task_keys:
            try:
                client.delete(key)
                deleted_count += 1
            except Exception as e:
                print(f"删除任务 {key} 失败: {e}")
        
        print(f"已清理 {deleted_count} 个任务")
        
    except Exception as e:
        print(f"清理任务失败: {e}")

def main():
    """主函数"""
    print("Redis监控工具")
    print("1. 实时监控Redis状态")
    print("2. 检查Celery任务状态")
    print("3. 清理旧任务")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请选择操作 (1-4): ").strip()
            
            if choice == '1':
                monitor_redis_status()
            elif choice == '2':
                check_celery_tasks()
            elif choice == '3':
                clean_old_tasks()
            elif choice == '4':
                print("退出")
                break
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n退出")
            break
        except Exception as e:
            print(f"操作失败: {e}")

if __name__ == "__main__":
    main()
