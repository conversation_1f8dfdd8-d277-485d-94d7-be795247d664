import logging
import os
import sys
from urllib.parse import urlparse, unquote
import requests
from flask import Flask, request, jsonify

from llama_parse import LlamaParse
from llama_parse.utils import Language

from configurer.yy_nacos import Nacos
from configurer.config_reader import get_lamma_index_config

logger = logging.getLogger(name=__name__)

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

app = Flask(__name__)

os.environ["LLAMA_CLOUD_API_KEY"] = 'llx-x76NZdc5MqH9zM2bWBSmKkTigfLrKlK47GFeZ5BvLr91eYgf'


def get_pdf_filename_from_url(url):
    """从URL中提取PDF文件名"""
    parsed_url = urlparse(url)  # 解析URL
    path = parsed_url.path  # 获取路径部分
    filename = path.split("/")[-1]  # 提取最后一部分
    return unquote(filename)  # 解码URL编码（如%转义）


def download_file(file_url, save_dir="temp_files"):
    """下载文件到本地临时目录"""
    os.makedirs(save_dir, exist_ok=True)  # 确保临时目录存在
    file_name = os.path.join(save_dir, get_pdf_filename_from_url(file_url))
    response = requests.get(file_url, stream=True)
    if response.status_code == 200:
        with open(file_name, "wb") as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)
        return file_name
    else:
        logger.error(f"Failed to download file from URL: {file_url}")
        raise Exception(f"Failed to download file. HTTP Status: {response.status_code}")


def parse_file_with_llamaparser(file_path):
    """调用 LlamaParser Python SDK 解析文件"""
    config = get_lamma_index_config()

    documents = LlamaParse(
        result_type="markdown",
        premium_mode=True if config['premium'] else False,
        language=Language.SIMPLIFIED_CHINESE,
        api_key=config['api_key'],
    ).load_data(file_path)

    # 将 documents[i].text 属性聚合，并以 \n 分割
    result = "\n".join([doc.text for doc in documents])

    return result


@app.route('/parse-pdf', methods=['POST'])
def parse_pdf():

    try:
        # Step 1: 获取文件 URL
        file_url = request.json.get("file_url")
        logger.info(f"received request: {file_url}")
        if not file_url:
            return jsonify({"error": "file_url is required"}), 400

        # Step 2: 下载文件到本地
        file_path = download_file(file_url)

        # Step 3: 调用 LlamaParser SDK 解析文件
        parsed_content = parse_file_with_llamaparser(file_path)

        # 清理临时文件
        os.remove(file_path)

        # Step 4: 返回解析结果
        return jsonify({
            'result': parsed_content,
            'error': None,
            'success': True
        })

    except Exception as e:
        return jsonify({"error": str(e), "success": False}), 500


if __name__ == '__main__':
    Nacos()
    app.run(host="0.0.0.0", port=8000, threaded=True)
