import json


class Quote:
    def __init__(self, docId: str, pageNo: int, content: str):
        self.docId = docId  # 文档ID
        self.pageNo = pageNo  # 页码
        self.content = content  # 引用内容


def main(arg1: str) -> dict:
    """
    解析dify RAG的结果，实现：

    1. 获取全部的parent chunk的content，作为后续llm抽取指标最终结果的
    2. 获取所有命中的child chunk的content，作为quote返回
    3. 组装一个map，可以定位到所有的doc

    """
    hit_docs = []
    hit_quotes = []
    seg_page_dict = {}
    records = json.loads(arg1)['records']
    for item in records:
        child_chunks = item['child_chunks']
        ## 排除知识库创建的第一条placeholder命中的case
        if len(child_chunks) > 0 and child_chunks[0]['content'] == 'placeholder':
            continue

        parent_chunk = item['segment']
        doc_id = parent_chunk['document']['name']
        hit_page_no = parent_chunk['position']
        hit_parent_content = parent_chunk['content']  # 修改变量名，避免与 hit_docs 混淆

        # 将 hit_docs 直接作为对象添加到列表中
        hit_docs.append({
            "doc_id": doc_id,
            "hit_snippets": hit_parent_content
        })

        seg_page_dict[doc_id] = hit_page_no
        for c in child_chunks:
            hit_quotes.append({
                'content': c['content'],
                'doc_id': doc_id,
                'page_no': hit_page_no
            })

    return {
        "hit_docs": hit_docs,  # 直接返回 Python 对象，而不是 JSON 字符串
        "quotes": hit_quotes,
        "seg_page_dict": seg_page_dict
    }


if __name__ == '__main__':
    test_input = json.dumps({
        "records": [
            {
                "segment": {
                    "document": {"name": "doc_001"},
                    "position": 3,
                    "content": "This is the parent chunk content."
                },
                "child_chunks": [
                    {"content": "This is child chunk A."},
                    {"content": "This is child chunk B."}
                ]
            },
            {
                "segment": {
                    "document": {"name": "doc_002"},
                    "position": 5,
                    "content": "Another parent chunk."
                },
                "child_chunks": [
                    {"content": "This is child chunk C."}
                ]
            },
            {
                "segment": {
                    "document": {"name": "doc_003"},
                    "position": 1,
                    "content": "Placeholder parent chunk."
                },
                "child_chunks": [
                    {"content": "placeholder"}
                ]
            }
        ]
    })

    result = main(test_input)
    print("== result ==")
    print(result)
    print(type(result))
    print(type(result['hit_docs']))
    print(result['hit_docs'])
    print(result['quotes'])