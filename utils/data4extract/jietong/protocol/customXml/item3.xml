<?xml version='1.0' encoding='utf-8'?>
<ct:contentTypeSchema ma:contentTypeVersion="17" xmlns:ct="http://schemas.microsoft.com/office/2006/metadata/contentType" ma:contentTypeName="文档" ma:contentTypeID="0x0101002C8927EDEB1E6E4693F4A29C283F9750" ma:versionID="6b9ec9c095f143e6bf3c92431334a34a" xmlns:ma="http://schemas.microsoft.com/office/2006/metadata/properties/metaAttributes" ct:_="" ma:contentTypeDescription="新建文档。" ma:_="" ma:contentTypeScope="">
 <xsd:schema xmlns:ns3="4bbd090b-74dd-4ea9-8424-0e161cb613dd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ma:fieldsID="b9c5ae756f4cb8c6e98bf040beaba6cc" xmlns:p="http://schemas.microsoft.com/office/2006/metadata/properties" ns2:_="" xmlns:xs="http://www.w3.org/2001/XMLSchema" ma:root="true" targetNamespace="http://schemas.microsoft.com/office/2006/metadata/properties" ns3:_="" xmlns:ns2="2872d83f-b338-4e21-a28a-5708592b420d">
  <xsd:import namespace="2872d83f-b338-4e21-a28a-5708592b420d"/>
  <xsd:import namespace="4bbd090b-74dd-4ea9-8424-0e161cb613dd"/>
  <xsd:element name="properties">
   <xsd:complexType>
    <xsd:sequence>
     <xsd:element name="documentManagement">
      <xsd:complexType>
       <xsd:all>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceMetadata"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceFastMetadata"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceDateTaken"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceAutoTags"/>
        <xsd:element minOccurs="0" ref="ns2:MediaLengthInSeconds"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceGenerationTime"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceEventHashCode"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceAutoKeyPoints"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceKeyPoints"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceOCR"/>
        <xsd:element minOccurs="0" ref="ns2:lcf76f155ced4ddcb4097134ff3c332f"/>
        <xsd:element minOccurs="0" ref="ns3:TaxCatchAll"/>
        <xsd:element minOccurs="0" ref="ns3:SharedWithUsers"/>
        <xsd:element minOccurs="0" ref="ns3:SharedWithDetails"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceObjectDetectorVersions"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceSearchProperties"/>
       </xsd:all>
      </xsd:complexType>
     </xsd:element>
    </xsd:sequence>
   </xsd:complexType>
  </xsd:element>
 </xsd:schema>
 <xsd:schema elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:dms="http://schemas.microsoft.com/office/2006/documentManagement/types" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" targetNamespace="2872d83f-b338-4e21-a28a-5708592b420d">
  <xsd:import namespace="http://schemas.microsoft.com/office/2006/documentManagement/types"/>
  <xsd:import namespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls"/>
  <xsd:element ma:hidden="true" name="MediaServiceMetadata" ma:readOnly="true" ma:displayName="MediaServiceMetadata" ma:internalName="MediaServiceMetadata" nillable="true" ma:index="8">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element ma:hidden="true" name="MediaServiceFastMetadata" ma:readOnly="true" ma:displayName="MediaServiceFastMetadata" ma:internalName="MediaServiceFastMetadata" nillable="true" ma:index="9">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element ma:hidden="true" name="MediaServiceDateTaken" ma:readOnly="true" ma:displayName="MediaServiceDateTaken" ma:internalName="MediaServiceDateTaken" nillable="true" ma:index="10">
   <xsd:simpleType>
    <xsd:restriction base="dms:Text"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element name="MediaServiceAutoTags" ma:readOnly="true" ma:displayName="Tags" ma:internalName="MediaServiceAutoTags" nillable="true" ma:index="11">
   <xsd:simpleType>
    <xsd:restriction base="dms:Text"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element ma:hidden="true" name="MediaLengthInSeconds" ma:readOnly="true" ma:displayName="MediaLengthInSeconds" ma:internalName="MediaLengthInSeconds" nillable="true" ma:index="12">
   <xsd:simpleType>
    <xsd:restriction base="dms:Unknown"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element ma:hidden="true" name="MediaServiceGenerationTime" ma:readOnly="true" ma:displayName="MediaServiceGenerationTime" ma:internalName="MediaServiceGenerationTime" nillable="true" ma:index="13">
   <xsd:simpleType>
    <xsd:restriction base="dms:Text"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element ma:hidden="true" name="MediaServiceEventHashCode" ma:readOnly="true" ma:displayName="MediaServiceEventHashCode" ma:internalName="MediaServiceEventHashCode" nillable="true" ma:index="14">
   <xsd:simpleType>
    <xsd:restriction base="dms:Text"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element ma:hidden="true" name="MediaServiceAutoKeyPoints" ma:readOnly="true" ma:displayName="MediaServiceAutoKeyPoints" ma:internalName="MediaServiceAutoKeyPoints" nillable="true" ma:index="15">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element name="MediaServiceKeyPoints" ma:readOnly="true" ma:displayName="KeyPoints" ma:internalName="MediaServiceKeyPoints" nillable="true" ma:index="16">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note">
     <xsd:maxLength value="255"/>
    </xsd:restriction>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element name="MediaServiceOCR" ma:readOnly="true" ma:displayName="Extracted Text" ma:internalName="MediaServiceOCR" nillable="true" ma:index="17">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note">
     <xsd:maxLength value="255"/>
    </xsd:restriction>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element ma:anchorId="fba54fb3-c3e1-fe81-a776-ca4b69148c4d" ma:open="true" name="lcf76f155ced4ddcb4097134ff3c332f" ma:readOnly="false" ma:displayName="图像标记" ma:fieldId="{5cf76f15-5ced-4ddc-b409-7134ff3c332f}" ma:isKeyword="false" ma:taxonomy="true" ma:taxonomyMulti="true" ma:internalName="lcf76f155ced4ddcb4097134ff3c332f" ma:termSetId="09814cd3-568e-fe90-9814-8d621ff8fb84" nillable="true" ma:taxonomyFieldName="MediaServiceImageTags" ma:sspId="64ac1d50-88aa-4bfe-bce7-e0113eca5772" ma:index="19">
   <xsd:complexType>
    <xsd:sequence>
     <xsd:element maxOccurs="1" minOccurs="0" ref="pc:Terms"/>
    </xsd:sequence>
   </xsd:complexType>
  </xsd:element>
  <xsd:element ma:hidden="true" name="MediaServiceObjectDetectorVersions" ma:readOnly="true" ma:displayName="MediaServiceObjectDetectorVersions" ma:indexed="true" ma:internalName="MediaServiceObjectDetectorVersions" nillable="true" ma:index="23">
   <xsd:simpleType>
    <xsd:restriction base="dms:Text"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element ma:hidden="true" name="MediaServiceSearchProperties" ma:readOnly="true" ma:displayName="MediaServiceSearchProperties" ma:internalName="MediaServiceSearchProperties" nillable="true" ma:index="24">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note"/>
   </xsd:simpleType>
  </xsd:element>
 </xsd:schema>
 <xsd:schema elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:dms="http://schemas.microsoft.com/office/2006/documentManagement/types" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" targetNamespace="4bbd090b-74dd-4ea9-8424-0e161cb613dd">
  <xsd:import namespace="http://schemas.microsoft.com/office/2006/documentManagement/types"/>
  <xsd:import namespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls"/>
  <xsd:element ma:hidden="true" ma:list="{4f2e7558-5bd2-4949-857a-5e8b53f5ef37}" ma:web="4bbd090b-74dd-4ea9-8424-0e161cb613dd" name="TaxCatchAll" ma:showField="CatchAllData" ma:displayName="Taxonomy Catch All Column" ma:internalName="TaxCatchAll" nillable="true" ma:index="20">
   <xsd:complexType>
    <xsd:complexContent>
     <xsd:extension base="dms:MultiChoiceLookup">
      <xsd:sequence>
       <xsd:element name="Value" maxOccurs="unbounded" minOccurs="0" nillable="true" type="dms:Lookup"/>
      </xsd:sequence>
     </xsd:extension>
    </xsd:complexContent>
   </xsd:complexType>
  </xsd:element>
  <xsd:element name="SharedWithUsers" ma:readOnly="true" ma:displayName="共享对象:" ma:internalName="SharedWithUsers" nillable="true" ma:index="21">
   <xsd:complexType>
    <xsd:complexContent>
     <xsd:extension base="dms:UserMulti">
      <xsd:sequence>
       <xsd:element name="UserInfo" maxOccurs="unbounded" minOccurs="0">
        <xsd:complexType>
         <xsd:sequence>
          <xsd:element name="DisplayName" minOccurs="0" type="xsd:string"/>
          <xsd:element name="AccountId" minOccurs="0" nillable="true" type="dms:UserId"/>
          <xsd:element name="AccountType" minOccurs="0" type="xsd:string"/>
         </xsd:sequence>
        </xsd:complexType>
       </xsd:element>
      </xsd:sequence>
     </xsd:extension>
    </xsd:complexContent>
   </xsd:complexType>
  </xsd:element>
  <xsd:element name="SharedWithDetails" ma:readOnly="true" ma:displayName="共享对象详细信息" ma:internalName="SharedWithDetails" nillable="true" ma:index="22">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note">
     <xsd:maxLength value="255"/>
    </xsd:restriction>
   </xsd:simpleType>
  </xsd:element>
 </xsd:schema>
 <xsd:schema elementFormDefault="qualified" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" blockDefault="#all" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:odoc="http://schemas.microsoft.com/internal/obd" targetNamespace="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:dc="http://purl.org/dc/elements/1.1/">
  <xsd:import schemaLocation="http://dublincore.org/schemas/xmls/qdc/2003/04/02/dc.xsd" namespace="http://purl.org/dc/elements/1.1/"/>
  <xsd:import schemaLocation="http://dublincore.org/schemas/xmls/qdc/2003/04/02/dcterms.xsd" namespace="http://purl.org/dc/terms/"/>
  <xsd:element name="coreProperties" type="CT_coreProperties"/>
  <xsd:complexType name="CT_coreProperties">
   <xsd:all>
    <xsd:element maxOccurs="1" minOccurs="0" ref="dc:creator"/>
    <xsd:element maxOccurs="1" minOccurs="0" ref="dcterms:created"/>
    <xsd:element maxOccurs="1" minOccurs="0" ref="dc:identifier"/>
    <xsd:element name="contentType" maxOccurs="1" ma:displayName="内容类型" minOccurs="0" type="xsd:string" ma:index="0"/>
    <xsd:element maxOccurs="1" ma:displayName="标题" minOccurs="0" ref="dc:title" ma:index="4"/>
    <xsd:element maxOccurs="1" minOccurs="0" ref="dc:subject"/>
    <xsd:element maxOccurs="1" minOccurs="0" ref="dc:description"/>
    <xsd:element name="keywords" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    <xsd:element maxOccurs="1" minOccurs="0" ref="dc:language"/>
    <xsd:element name="category" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    <xsd:element name="version" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    <xsd:element name="revision" maxOccurs="1" minOccurs="0" type="xsd:string">
     <xsd:annotation>
      <xsd:documentation>&#xd;
                        This value indicates the number of saves or revisions. The application is responsible for updating this value after each revision.&#xd;
                    </xsd:documentation>
     </xsd:annotation>
    </xsd:element>
    <xsd:element name="lastModifiedBy" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    <xsd:element maxOccurs="1" minOccurs="0" ref="dcterms:modified"/>
    <xsd:element name="contentStatus" maxOccurs="1" minOccurs="0" type="xsd:string"/>
   </xsd:all>
  </xsd:complexType>
 </xsd:schema>
 <xs:schema elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" targetNamespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls">
  <xs:element name="Person">
   <xs:complexType>
    <xs:sequence>
     <xs:element minOccurs="0" ref="pc:DisplayName"/>
     <xs:element minOccurs="0" ref="pc:AccountId"/>
     <xs:element minOccurs="0" ref="pc:AccountType"/>
    </xs:sequence>
   </xs:complexType>
  </xs:element>
  <xs:element name="DisplayName" type="xs:string"/>
  <xs:element name="AccountId" type="xs:string"/>
  <xs:element name="AccountType" type="xs:string"/>
  <xs:element name="BDCAssociatedEntity">
   <xs:complexType>
    <xs:sequence>
     <xs:element maxOccurs="unbounded" minOccurs="0" ref="pc:BDCEntity"/>
    </xs:sequence>
    <xs:attribute ref="pc:EntityNamespace"/>
    <xs:attribute ref="pc:EntityName"/>
    <xs:attribute ref="pc:SystemInstanceName"/>
    <xs:attribute ref="pc:AssociationName"/>
   </xs:complexType>
  </xs:element>
  <xs:attribute name="EntityNamespace" type="xs:string"/>
  <xs:attribute name="EntityName" type="xs:string"/>
  <xs:attribute name="SystemInstanceName" type="xs:string"/>
  <xs:attribute name="AssociationName" type="xs:string"/>
  <xs:element name="BDCEntity">
   <xs:complexType>
    <xs:sequence>
     <xs:element minOccurs="0" ref="pc:EntityDisplayName"/>
     <xs:element minOccurs="0" ref="pc:EntityInstanceReference"/>
     <xs:element minOccurs="0" ref="pc:EntityId1"/>
     <xs:element minOccurs="0" ref="pc:EntityId2"/>
     <xs:element minOccurs="0" ref="pc:EntityId3"/>
     <xs:element minOccurs="0" ref="pc:EntityId4"/>
     <xs:element minOccurs="0" ref="pc:EntityId5"/>
    </xs:sequence>
   </xs:complexType>
  </xs:element>
  <xs:element name="EntityDisplayName" type="xs:string"/>
  <xs:element name="EntityInstanceReference" type="xs:string"/>
  <xs:element name="EntityId1" type="xs:string"/>
  <xs:element name="EntityId2" type="xs:string"/>
  <xs:element name="EntityId3" type="xs:string"/>
  <xs:element name="EntityId4" type="xs:string"/>
  <xs:element name="EntityId5" type="xs:string"/>
  <xs:element name="Terms">
   <xs:complexType>
    <xs:sequence>
     <xs:element maxOccurs="unbounded" minOccurs="0" ref="pc:TermInfo"/>
    </xs:sequence>
   </xs:complexType>
  </xs:element>
  <xs:element name="TermInfo">
   <xs:complexType>
    <xs:sequence>
     <xs:element minOccurs="0" ref="pc:TermName"/>
     <xs:element minOccurs="0" ref="pc:TermId"/>
    </xs:sequence>
   </xs:complexType>
  </xs:element>
  <xs:element name="TermName" type="xs:string"/>
  <xs:element name="TermId" type="xs:string"/>
 </xs:schema>
</ct:contentTypeSchema>
