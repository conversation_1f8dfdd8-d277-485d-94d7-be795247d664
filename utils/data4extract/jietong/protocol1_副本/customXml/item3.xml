<?xml version='1.0' encoding='utf-8'?>
<ct:contentTypeSchema ma:contentTypeScope="" ma:contentTypeID="0x0101002C8927EDEB1E6E4693F4A29C283F9750" ma:contentTypeDescription="新建文档。" xmlns:ct="http://schemas.microsoft.com/office/2006/metadata/contentType" xmlns:ma="http://schemas.microsoft.com/office/2006/metadata/properties/metaAttributes" ma:contentTypeVersion="17" ma:contentTypeName="文档" ma:versionID="6b9ec9c095f143e6bf3c92431334a34a" ct:_="" ma:_="">
 <xsd:schema ns2:_="" targetNamespace="http://schemas.microsoft.com/office/2006/metadata/properties" xmlns:p="http://schemas.microsoft.com/office/2006/metadata/properties" ma:root="true" xmlns:ns2="2872d83f-b338-4e21-a28a-5708592b420d" ns3:_="" xmlns:ns3="4bbd090b-74dd-4ea9-8424-0e161cb613dd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ma:fieldsID="b9c5ae756f4cb8c6e98bf040beaba6cc">
  <xsd:import namespace="2872d83f-b338-4e21-a28a-5708592b420d"/>
  <xsd:import namespace="4bbd090b-74dd-4ea9-8424-0e161cb613dd"/>
  <xsd:element name="properties">
   <xsd:complexType>
    <xsd:sequence>
     <xsd:element name="documentManagement">
      <xsd:complexType>
       <xsd:all>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceMetadata"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceFastMetadata"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceDateTaken"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceAutoTags"/>
        <xsd:element minOccurs="0" ref="ns2:MediaLengthInSeconds"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceGenerationTime"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceEventHashCode"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceAutoKeyPoints"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceKeyPoints"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceOCR"/>
        <xsd:element minOccurs="0" ref="ns2:lcf76f155ced4ddcb4097134ff3c332f"/>
        <xsd:element minOccurs="0" ref="ns3:TaxCatchAll"/>
        <xsd:element minOccurs="0" ref="ns3:SharedWithUsers"/>
        <xsd:element minOccurs="0" ref="ns3:SharedWithDetails"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceObjectDetectorVersions"/>
        <xsd:element minOccurs="0" ref="ns2:MediaServiceSearchProperties"/>
       </xsd:all>
      </xsd:complexType>
     </xsd:element>
    </xsd:sequence>
   </xsd:complexType>
  </xsd:element>
 </xsd:schema>
 <xsd:schema xmlns:dms="http://schemas.microsoft.com/office/2006/documentManagement/types" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" targetNamespace="2872d83f-b338-4e21-a28a-5708592b420d" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
  <xsd:import namespace="http://schemas.microsoft.com/office/2006/documentManagement/types"/>
  <xsd:import namespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls"/>
  <xsd:element nillable="true" ma:index="8" ma:displayName="MediaServiceMetadata" ma:hidden="true" name="MediaServiceMetadata" ma:readOnly="true" ma:internalName="MediaServiceMetadata">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="9" ma:displayName="MediaServiceFastMetadata" ma:hidden="true" name="MediaServiceFastMetadata" ma:readOnly="true" ma:internalName="MediaServiceFastMetadata">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="10" ma:displayName="MediaServiceDateTaken" ma:hidden="true" name="MediaServiceDateTaken" ma:readOnly="true" ma:internalName="MediaServiceDateTaken">
   <xsd:simpleType>
    <xsd:restriction base="dms:Text"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="11" ma:displayName="Tags" name="MediaServiceAutoTags" ma:readOnly="true" ma:internalName="MediaServiceAutoTags">
   <xsd:simpleType>
    <xsd:restriction base="dms:Text"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="12" ma:displayName="MediaLengthInSeconds" ma:hidden="true" name="MediaLengthInSeconds" ma:readOnly="true" ma:internalName="MediaLengthInSeconds">
   <xsd:simpleType>
    <xsd:restriction base="dms:Unknown"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="13" ma:displayName="MediaServiceGenerationTime" ma:hidden="true" name="MediaServiceGenerationTime" ma:readOnly="true" ma:internalName="MediaServiceGenerationTime">
   <xsd:simpleType>
    <xsd:restriction base="dms:Text"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="14" ma:displayName="MediaServiceEventHashCode" ma:hidden="true" name="MediaServiceEventHashCode" ma:readOnly="true" ma:internalName="MediaServiceEventHashCode">
   <xsd:simpleType>
    <xsd:restriction base="dms:Text"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="15" ma:displayName="MediaServiceAutoKeyPoints" ma:hidden="true" name="MediaServiceAutoKeyPoints" ma:readOnly="true" ma:internalName="MediaServiceAutoKeyPoints">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="16" ma:displayName="KeyPoints" name="MediaServiceKeyPoints" ma:readOnly="true" ma:internalName="MediaServiceKeyPoints">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note">
     <xsd:maxLength value="255"/>
    </xsd:restriction>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="17" ma:displayName="Extracted Text" name="MediaServiceOCR" ma:readOnly="true" ma:internalName="MediaServiceOCR">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note">
     <xsd:maxLength value="255"/>
    </xsd:restriction>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element nillable="true" ma:isKeyword="false" ma:sspId="64ac1d50-88aa-4bfe-bce7-e0113eca5772" ma:open="true" ma:taxonomy="true" ma:termSetId="09814cd3-568e-fe90-9814-8d621ff8fb84" ma:fieldId="{5cf76f15-5ced-4ddc-b409-7134ff3c332f}" ma:index="19" ma:taxonomyFieldName="MediaServiceImageTags" ma:displayName="图像标记" ma:taxonomyMulti="true" name="lcf76f155ced4ddcb4097134ff3c332f" ma:anchorId="fba54fb3-c3e1-fe81-a776-ca4b69148c4d" ma:readOnly="false" ma:internalName="lcf76f155ced4ddcb4097134ff3c332f">
   <xsd:complexType>
    <xsd:sequence>
     <xsd:element minOccurs="0" ref="pc:Terms" maxOccurs="1"/>
    </xsd:sequence>
   </xsd:complexType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="23" ma:indexed="true" ma:displayName="MediaServiceObjectDetectorVersions" ma:hidden="true" name="MediaServiceObjectDetectorVersions" ma:readOnly="true" ma:internalName="MediaServiceObjectDetectorVersions">
   <xsd:simpleType>
    <xsd:restriction base="dms:Text"/>
   </xsd:simpleType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="24" ma:displayName="MediaServiceSearchProperties" ma:hidden="true" name="MediaServiceSearchProperties" ma:readOnly="true" ma:internalName="MediaServiceSearchProperties">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note"/>
   </xsd:simpleType>
  </xsd:element>
 </xsd:schema>
 <xsd:schema xmlns:dms="http://schemas.microsoft.com/office/2006/documentManagement/types" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" targetNamespace="4bbd090b-74dd-4ea9-8424-0e161cb613dd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
  <xsd:import namespace="http://schemas.microsoft.com/office/2006/documentManagement/types"/>
  <xsd:import namespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls"/>
  <xsd:element nillable="true" ma:list="{4f2e7558-5bd2-4949-857a-5e8b53f5ef37}" ma:index="20" ma:displayName="Taxonomy Catch All Column" ma:hidden="true" ma:web="4bbd090b-74dd-4ea9-8424-0e161cb613dd" name="TaxCatchAll" ma:showField="CatchAllData" ma:internalName="TaxCatchAll">
   <xsd:complexType>
    <xsd:complexContent>
     <xsd:extension base="dms:MultiChoiceLookup">
      <xsd:sequence>
       <xsd:element minOccurs="0" nillable="true" type="dms:Lookup" name="Value" maxOccurs="unbounded"/>
      </xsd:sequence>
     </xsd:extension>
    </xsd:complexContent>
   </xsd:complexType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="21" ma:displayName="共享对象:" name="SharedWithUsers" ma:readOnly="true" ma:internalName="SharedWithUsers">
   <xsd:complexType>
    <xsd:complexContent>
     <xsd:extension base="dms:UserMulti">
      <xsd:sequence>
       <xsd:element minOccurs="0" name="UserInfo" maxOccurs="unbounded">
        <xsd:complexType>
         <xsd:sequence>
          <xsd:element minOccurs="0" type="xsd:string" name="DisplayName"/>
          <xsd:element minOccurs="0" nillable="true" type="dms:UserId" name="AccountId"/>
          <xsd:element minOccurs="0" type="xsd:string" name="AccountType"/>
         </xsd:sequence>
        </xsd:complexType>
       </xsd:element>
      </xsd:sequence>
     </xsd:extension>
    </xsd:complexContent>
   </xsd:complexType>
  </xsd:element>
  <xsd:element nillable="true" ma:index="22" ma:displayName="共享对象详细信息" name="SharedWithDetails" ma:readOnly="true" ma:internalName="SharedWithDetails">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note">
     <xsd:maxLength value="255"/>
    </xsd:restriction>
   </xsd:simpleType>
  </xsd:element>
 </xsd:schema>
 <xsd:schema attributeFormDefault="unqualified" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" blockDefault="#all" xmlns:dcterms="http://purl.org/dc/terms/" targetNamespace="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:odoc="http://schemas.microsoft.com/internal/obd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <xsd:import schemaLocation="http://dublincore.org/schemas/xmls/qdc/2003/04/02/dc.xsd" namespace="http://purl.org/dc/elements/1.1/"/>
  <xsd:import schemaLocation="http://dublincore.org/schemas/xmls/qdc/2003/04/02/dcterms.xsd" namespace="http://purl.org/dc/terms/"/>
  <xsd:element type="CT_coreProperties" name="coreProperties"/>
  <xsd:complexType name="CT_coreProperties">
   <xsd:all>
    <xsd:element minOccurs="0" ref="dc:creator" maxOccurs="1"/>
    <xsd:element minOccurs="0" ref="dcterms:created" maxOccurs="1"/>
    <xsd:element minOccurs="0" ref="dc:identifier" maxOccurs="1"/>
    <xsd:element minOccurs="0" ma:index="0" type="xsd:string" ma:displayName="内容类型" name="contentType" maxOccurs="1"/>
    <xsd:element minOccurs="0" ma:index="4" ma:displayName="标题" ref="dc:title" maxOccurs="1"/>
    <xsd:element minOccurs="0" ref="dc:subject" maxOccurs="1"/>
    <xsd:element minOccurs="0" ref="dc:description" maxOccurs="1"/>
    <xsd:element minOccurs="0" type="xsd:string" name="keywords" maxOccurs="1"/>
    <xsd:element minOccurs="0" ref="dc:language" maxOccurs="1"/>
    <xsd:element minOccurs="0" type="xsd:string" name="category" maxOccurs="1"/>
    <xsd:element minOccurs="0" type="xsd:string" name="version" maxOccurs="1"/>
    <xsd:element minOccurs="0" type="xsd:string" name="revision" maxOccurs="1">
     <xsd:annotation>
      <xsd:documentation>&#xd;
                        This value indicates the number of saves or revisions. The application is responsible for updating this value after each revision.&#xd;
                    </xsd:documentation>
     </xsd:annotation>
    </xsd:element>
    <xsd:element minOccurs="0" type="xsd:string" name="lastModifiedBy" maxOccurs="1"/>
    <xsd:element minOccurs="0" ref="dcterms:modified" maxOccurs="1"/>
    <xsd:element minOccurs="0" type="xsd:string" name="contentStatus" maxOccurs="1"/>
   </xsd:all>
  </xsd:complexType>
 </xsd:schema>
 <xs:schema attributeFormDefault="unqualified" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" targetNamespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
  <xs:element name="Person">
   <xs:complexType>
    <xs:sequence>
     <xs:element minOccurs="0" ref="pc:DisplayName"/>
     <xs:element minOccurs="0" ref="pc:AccountId"/>
     <xs:element minOccurs="0" ref="pc:AccountType"/>
    </xs:sequence>
   </xs:complexType>
  </xs:element>
  <xs:element type="xs:string" name="DisplayName"/>
  <xs:element type="xs:string" name="AccountId"/>
  <xs:element type="xs:string" name="AccountType"/>
  <xs:element name="BDCAssociatedEntity">
   <xs:complexType>
    <xs:sequence>
     <xs:element minOccurs="0" ref="pc:BDCEntity" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute ref="pc:EntityNamespace"/>
    <xs:attribute ref="pc:EntityName"/>
    <xs:attribute ref="pc:SystemInstanceName"/>
    <xs:attribute ref="pc:AssociationName"/>
   </xs:complexType>
  </xs:element>
  <xs:attribute type="xs:string" name="EntityNamespace"/>
  <xs:attribute type="xs:string" name="EntityName"/>
  <xs:attribute type="xs:string" name="SystemInstanceName"/>
  <xs:attribute type="xs:string" name="AssociationName"/>
  <xs:element name="BDCEntity">
   <xs:complexType>
    <xs:sequence>
     <xs:element minOccurs="0" ref="pc:EntityDisplayName"/>
     <xs:element minOccurs="0" ref="pc:EntityInstanceReference"/>
     <xs:element minOccurs="0" ref="pc:EntityId1"/>
     <xs:element minOccurs="0" ref="pc:EntityId2"/>
     <xs:element minOccurs="0" ref="pc:EntityId3"/>
     <xs:element minOccurs="0" ref="pc:EntityId4"/>
     <xs:element minOccurs="0" ref="pc:EntityId5"/>
    </xs:sequence>
   </xs:complexType>
  </xs:element>
  <xs:element type="xs:string" name="EntityDisplayName"/>
  <xs:element type="xs:string" name="EntityInstanceReference"/>
  <xs:element type="xs:string" name="EntityId1"/>
  <xs:element type="xs:string" name="EntityId2"/>
  <xs:element type="xs:string" name="EntityId3"/>
  <xs:element type="xs:string" name="EntityId4"/>
  <xs:element type="xs:string" name="EntityId5"/>
  <xs:element name="Terms">
   <xs:complexType>
    <xs:sequence>
     <xs:element minOccurs="0" ref="pc:TermInfo" maxOccurs="unbounded"/>
    </xs:sequence>
   </xs:complexType>
  </xs:element>
  <xs:element name="TermInfo">
   <xs:complexType>
    <xs:sequence>
     <xs:element minOccurs="0" ref="pc:TermName"/>
     <xs:element minOccurs="0" ref="pc:TermId"/>
    </xs:sequence>
   </xs:complexType>
  </xs:element>
  <xs:element type="xs:string" name="TermName"/>
  <xs:element type="xs:string" name="TermId"/>
 </xs:schema>
</ct:contentTypeSchema>
