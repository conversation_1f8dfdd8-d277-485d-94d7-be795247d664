from typing import List

from pdf2image import convert_from_path

from models.file_info import FileInfo


def pdf_to_images(pdf_path, output_folder, dpi=200) -> List[FileInfo]:
    """
    将 pdf_path 指定的 PDF 文件拆分为多张图片并保存在 output_folder 文件夹中。
    返回一个包含所有生成图片路径的列表。
    """
    # 将 PDF 转为一系列 PIL.Image.Image 对象
    images = convert_from_path(pdf_path, dpi=dpi)

    # 存放生成的图片路径
    img_files = []

    # 遍历每页生成的 PIL 图片对象，并另存为 PNG 文件
    for i, page_img in enumerate(images, start=1):
        # 构造输出文件名，如 page_1.png, page_2.png ...
        file_name = f"page_{i}.png"
        image_path = f"{output_folder}/page_{i}.png"
        page_img.save(image_path, "PNG")
        img_files.append(FileInfo(file_name=file_name, file_path=image_path))

    return img_files


if __name__ == "__main__":
    pdf_path = "../test/OCR识别病历1_纯图版（5-6）.pdf"  # 你的 PDF 文件路径
    output_folder = "../uploads"  # 你希望保存的文件夹，需要确保它已存在（或自行先创建）

    # 调用函数
    file_infos = pdf_to_images(pdf_path, output_folder)

    # 输出每张图片的路径
    print("生成的图片路径：")
    for p in file_infos:
        print(p)
