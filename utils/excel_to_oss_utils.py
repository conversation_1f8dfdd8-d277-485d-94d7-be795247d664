# utils/excel_to_oss_utils.py

import os
import pandas as pd
from datetime import datetime
from typing import Dict, List
from sqlalchemy import create_engine, text, inspect

from logger.logger import app_logger
from servers.file_server import upload_local_file, get_file_url
from config.settings import settings


def generate_excel_from_mysql(mysql_config: Dict, task_id: str) -> str:
    """
    从 MySQL 数据库生成 Excel 文件
    
    Args:
        mysql_config: MySQL 数据库配置
        task_id: 任务ID，用于生成唯一的文件名
        
    Returns:
        str: 生成的 Excel 文件的本地路径
    """
    app_logger.info(f"开始从数据库 '{mysql_config['database']}' 生成 Excel 文件")
    
    # 创建数据库连接
    engine = create_engine(
        f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}@{mysql_config['host']}/{mysql_config['database']}?charset=utf8mb4",
        pool_pre_ping=True
    )
    
    try:
        # 获取数据库中的所有表
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        if not tables:
            raise ValueError(f"数据库 '{mysql_config['database']}' 中没有找到任何表")
        
        app_logger.info(f"找到 {len(tables)} 个表: {tables}")
        
        # 生成唯一的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"excel_export_{task_id}_{timestamp}.xlsx"
        
        # 确保输出目录存在
        output_dir = settings.EXCEL_IMPORT_FOLDER
        os.makedirs(output_dir, exist_ok=True)
        
        excel_file_path = os.path.join(output_dir, filename)
        
        # 创建 Excel writer
        with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
            for table_name in tables:
                try:
                    app_logger.info(f"正在导出表: {table_name}")
                    
                    # 从表中读取数据
                    query = f"SELECT * FROM `{table_name}`"
                    df = pd.read_sql(query, engine)
                    
                    # 将数据写入 Excel 的一个 sheet
                    # 使用原始表名作为 sheet 名称，但需要处理 Excel 的限制
                    sheet_name = clean_sheet_name(table_name)
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                    
                    app_logger.info(f"表 '{table_name}' 导出完成，共 {len(df)} 行数据")
                    
                except Exception as e:
                    app_logger.error(f"导出表 '{table_name}' 时出错: {e}")
                    # 继续处理其他表，不中断整个过程
                    continue
        
        app_logger.info(f"Excel 文件生成成功: {excel_file_path}")
        return excel_file_path
        
    except Exception as e:
        app_logger.error(f"从数据库生成 Excel 文件失败: {e}", exc_info=True)
        raise
    finally:
        engine.dispose()


def clean_sheet_name(name: str) -> str:
    """
    清理 sheet 名称，使其符合 Excel 的要求
    Excel sheet 名称限制：
    - 最大长度 31 字符
    - 不能包含: \ / ? * [ ] :
    """
    # 替换不允许的字符
    invalid_chars = ['\\', '/', '?', '*', '[', ']', ':']
    cleaned_name = name
    for char in invalid_chars:
        cleaned_name = cleaned_name.replace(char, '_')
    
    # 限制长度
    if len(cleaned_name) > 31:
        cleaned_name = cleaned_name[:31]
    
    # 确保不为空
    if not cleaned_name:
        cleaned_name = "Sheet1"
    
    return cleaned_name


def upload_excel_to_oss(excel_file_path: str, task_id: str) -> str:
    """
    将 Excel 文件上传到 OSS 并返回访问链接
    
    Args:
        excel_file_path: Excel 文件的本地路径
        task_id: 任务ID，用于生成 OSS 文件名
        
    Returns:
        str: OSS 文件的访问链接
    """
    app_logger.info(f"开始上传 Excel 文件到 OSS: {excel_file_path}")
    
    try:
        # 生成 OSS 文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        original_filename = os.path.basename(excel_file_path)
        oss_filename = f"excel_exports/{task_id}_{timestamp}_{original_filename}"
        
        # 上传文件到 OSS
        file_key = upload_local_file(excel_file_path, oss_filename)
        
        # 获取文件的访问链接
        file_url = get_file_url(file_key)
        
        app_logger.info(f"Excel 文件上传到 OSS 成功，文件键: {file_key}")
        app_logger.info(f"OSS 访问链接: {file_url}")
        
        return file_url
        
    except Exception as e:
        app_logger.error(f"上传 Excel 文件到 OSS 失败: {e}", exc_info=True)
        raise


def get_database_summary(mysql_config: Dict) -> Dict:
    """
    获取数据库的摘要信息
    
    Args:
        mysql_config: MySQL 数据库配置
        
    Returns:
        Dict: 包含表信息的摘要
    """
    engine = create_engine(
        f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}@{mysql_config['host']}/{mysql_config['database']}?charset=utf8mb4",
        pool_pre_ping=True
    )
    
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        table_info = []
        total_rows = 0
        
        for table_name in tables:
            try:
                # 获取表的行数
                with engine.connect() as conn:
                    result = conn.execute(text(f"SELECT COUNT(*) FROM `{table_name}`"))
                    row_count = result.scalar()
                    total_rows += row_count
                    
                    table_info.append({
                        "table_name": table_name,
                        "row_count": row_count
                    })
            except Exception as e:
                app_logger.warning(f"获取表 '{table_name}' 信息时出错: {e}")
                table_info.append({
                    "table_name": table_name,
                    "row_count": 0,
                    "error": str(e)
                })
        
        return {
            "database_name": mysql_config['database'],
            "total_tables": len(tables),
            "total_rows": total_rows,
            "tables": table_info
        }
        
    except Exception as e:
        app_logger.error(f"获取数据库摘要失败: {e}")
        return {
            "database_name": mysql_config['database'],
            "error": str(e)
        }
    finally:
        engine.dispose()
