import logging
import os
from datetime import datetime

from starlette.requests import Request

from api.doc_diff_api import UPLOAD_FOLDER
from models.file_info import FileInfo
from servers.file_server import build_oss_key, get_file_url
from utils.oss_utils import write_file


def get_filename_from_request(request: Request):
    content_disposition = request.headers.get("content-disposition")
    if content_disposition:
        # 提取文件名
        parts = content_disposition.split(";")
        for part in parts:
            if "filename" in part:
                filename = part.split("=")[1].strip().strip('"')
                return filename

    return None


async def upload(request: Request) -> FileInfo | None:
    now = datetime.now()
    formatted_time = now.strftime("%Y%m%d%H%M%S")
    filename = f"{formatted_time}.txt"

    try:
        filename2 = get_filename_from_request(request)
        if filename2:
            filename = filename2

        current_file_path = os.path.dirname(__file__)
        file_path = os.path.join(current_file_path, '..', UPLOAD_FOLDER, filename)

        content = await request.body()

        # 保存上传的文件
        with open(file_path, "wb") as f:
            f.write(content)

        # 上传文件到OSS
        key = build_oss_key(filename)
        write_file(file_path, key)

        # ASR
        file_url = get_file_url(key)
        res = FileInfo(file_key=key, file_name=filename, file_url=file_url, file_path=file_path)

        # 清理临时文件
        # os.remove(file_path)
        return res
    except Exception as e:
        logging.error("File Upload Failed: ", e)
        return None
