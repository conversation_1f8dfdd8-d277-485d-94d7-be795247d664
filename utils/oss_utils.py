# -*- coding: utf-8 -*-
# utils/oss_utils.py

import oss2
import os
import io
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Optional, Callable
from oss2.models import GetObjectResult

LASER = "/data/LASER_yiya"

# 从配置文件中获取Access Key ID和Access Key Secret
access_key_id = "LTAI5tSMtzYQ5GVPCm8njDYp"
access_key_secret = "******************************"

# 对于500MB的文件，可以设置每片10MB到50MB
PART_SIZE = 20 * 1024 * 1024  # 10MB

# 使用获取的RAM用户的访问密钥配置访问凭证
auth = oss2.AuthV4(access_key_id, access_key_secret)

# 使用环境变量中获取的RAM用户访问密钥配置访问凭证
bucket_name = 'yiya-dev'  # 将 bucket 名称存为变量
bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', bucket_name, region="cn-hangzhou")


def write_large_file_from_stream(stream: io.BytesIO, key: str,
                                 progress_callback: Optional[Callable[[int, int], None]] = None):
    """
    从内存流中上传文件到OSS。
    该方法会自动判断文件大小并决定是否使用分片上传，是官方推荐的方式。
    """
    stream.seek(0)
    bucket.put_object(key, stream, progress_callback=progress_callback)


def write_large_file_from_stream_parallel(stream: io.BytesIO, key: str, part_size: int = 20 * 1024 * 1024,
                                          max_workers: int = 2,
                                          progress_callback: Optional[Callable[[int, int], None]] = None):
    """
    优化的并行分片上传大文件到OSS，解决内存占用和网络瓶颈问题。

    Args:
        stream: 内存流对象
        key: OSS文件key
        part_size: 分片大小(字节), 默认5MB（更小分片=更高并发）
        max_workers: 最大并发线程数, 默认4
        progress_callback: 进度回调函数, 参数为(bytes_uploaded, total_bytes)

    Returns:
        上传结果
    """
    import time

    stream.seek(0, io.SEEK_END)
    total_size = stream.tell()
    stream.seek(0)

    if total_size <= 10 * 1024 * 1024:  # 小于10MB直接上传
        return bucket.put_object(key, stream, progress_callback=progress_callback)

    # 计算最优分片大小和并发数
    optimal_part_size = max(part_size, min(8 * 1024 * 1024, total_size // 50))  # 至少50个分片
    part_count = (total_size + optimal_part_size - 1) // optimal_part_size

    # 基于CPU核心数限制并发
    actual_max_workers = min(max_workers, (os.cpu_count() or 4) * 2)

    upload_id = bucket.init_multipart_upload(key).upload_id

    try:
        # 预计算所有分片信息（不加载数据到内存）
        parts_info = []
        for part_number in range(1, part_count + 1):
            start = (part_number - 1) * optimal_part_size
            end = min(start + optimal_part_size, total_size)
            parts_info.append((part_number, start, end - start))

        uploaded_parts = []
        uploaded_bytes = 0

        # 使用线程池和内存映射优化
        with ThreadPoolExecutor(max_workers=actual_max_workers) as executor:
            future_to_part = {
                executor.submit(_upload_part_from_stream, stream, key, upload_id, part_num, start, size): part_num
                for part_num, start, size in parts_info
            }

            for future in as_completed(future_to_part):
                part_num = future_to_part[future]
                try:
                    result = future.result()
                    uploaded_parts.append(result)
                    uploaded_bytes += parts_info[part_num - 1][2]

                    # 进度回调
                    if progress_callback:
                        progress_callback(uploaded_bytes, total_size)

                except Exception as e:
                    bucket.abort_multipart_upload(key, upload_id)
                    raise e

        # 完成分片上传
        uploaded_parts.sort(key=lambda x: x.part_number)
        return bucket.complete_multipart_upload(key, upload_id, uploaded_parts)

    except Exception as e:
        try:
            bucket.abort_multipart_upload(key, upload_id)
        except:
            pass
        raise e


def _upload_part_from_stream(stream: io.BytesIO, key: str, upload_id: str, part_number: int, start: int, size: int):
    """从流中读取指定位置的数据并上传分片"""
    stream.seek(start)
    data = stream.read(size)
    result = bucket.upload_part(key, upload_id, part_number, data)
    return oss2.models.PartInfo(part_number, result.etag)


def _upload_part(key: str, upload_id: str, part_number: int, data: bytes):
    """上传单个分片"""
    result = bucket.upload_part(key, upload_id, part_number, data)
    return oss2.models.PartInfo(part_number, result.etag)


def download_file(key, save_path):
    """将文件从OSS下载到本地路径。"""
    folder = os.path.dirname(save_path)
    if not os.path.exists(folder):
        os.makedirs(folder)
    bucket.get_object_to_file(key, save_path)


def write_file(local_path, key):
    """从本地文件路径上传文件到OSS。"""
    bucket.put_object_from_file(key, local_path)


def get_file_url(key: str):
    """获取文件的签名URL。"""
    expiration = 24 * 60 * 60
    return bucket.sign_url('GET', key, expiration)


def progress_callback(bytes_uploaded, total_bytes):
    """默认进度回调函数"""
    progress = (bytes_uploaded / total_bytes) * 100
    print(f"\r上传进度: {progress:.2f}% ({bytes_uploaded}/{total_bytes} bytes)", end="", flush=True)
    if bytes_uploaded == total_bytes:
        print("\n上传完成!")


def download_file_to_stream(key: str) -> GetObjectResult:
    """
    从OSS下载文件，并以流的形式返回。
    返回的对象本身就是一个可读的流 (file-like object)。
    """
    return bucket.get_object(key)


def write_from_stream(stream: io.BytesIO, key: str):
    """
    从内存中的流（比如 io.BytesIO）上传文件到OSS。
    """
    # put_object 方法可以直接处理流对象
    bucket.put_object(key, stream)


def test_upload_speed(file_size_mb=50, optimized=True):
    """测试上传速度并诊断瓶颈"""
    import time
    import io

    # 创建测试数据
    test_data = b"x" * (file_size_mb * 1024 * 1024)
    stream = io.BytesIO(test_data)
    key = f"speed_test_{file_size_mb}mb.dat"

    print(f"=== 上传速度测试 ({file_size_mb}MB) ===")

    # 测试简单上传
    start = time.time()
    write_large_file_from_stream(stream, key)
    simple_time = time.time() - start
    print(f"简单上传: {simple_time:.2f}s ({file_size_mb / simple_time:.2f} MB/s)")

    # 测试并行上传（优化参数）
    stream.seek(0)
    start = time.time()
    if optimized:
        write_large_file_from_stream_parallel(stream, key + "_optimized",
                                              part_size=20 * 1024 * 1024, max_workers=2)
    else:
        write_large_file_from_stream_parallel(stream, key + "_parallel")

    parallel_time = time.time() - start
    print(f"并行上传: {parallel_time:.2f}s ({file_size_mb / parallel_time:.2f} MB/s)")

    # 带宽分析
    simple_bandwidth = file_size_mb / simple_time
    parallel_bandwidth = file_size_mb / parallel_time

    print(f"\n=== 网络分析 ===")
    print(f"简单上传带宽: {simple_bandwidth:.1f} MB/s ({simple_bandwidth * 8:.0f} Mbps)")
    print(f"并行上传带宽: {parallel_bandwidth:.1f} MB/s ({parallel_bandwidth * 8:.0f} Mbps)")

    if simple_bandwidth > 10:
        print("网络带宽充足")
    else:
        print("网络带宽可能成为瓶颈")

    # 优化建议
    print(f"\n=== 优化建议 ===")
    if parallel_time > simple_time * 1.1:
        print("建议使用大分片+少线程：part_size=20MB, max_workers=2")
        print("或直接使用简单上传")
    elif parallel_time > simple_time:
        print("建议使用中等分片：part_size=10MB, max_workers=2")
    else:
        print("当前配置合理，可继续使用并行上传")

    # 清理测试文件
    try:
        bucket.delete_object(key)
        bucket.delete_object(key + "_parallel")
    except:
        pass


if __name__ == '__main__':
    test_upload_speed(50, optimized=True)  # 测试优化后的并行上传
