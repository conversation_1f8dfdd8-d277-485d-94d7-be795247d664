import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import Border, Side, Alignment
from datetime import datetime
from typing import Dict, List, Any
from logger.logger import app_logger


def process_rule1_data(rule1_data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """处理规则1数据"""
    processed_data = []
    for item in rule1_data:
        processed_data.append({
            '规则': '规则1',
            '描述': f"ae表里的受试者为{item['受试者编号']}的不良事件{item['不良事件名称_aeterm']}的开始日期{item['开始日期_aestdat']}在lab表，ecg表，pe表，vs表均找不到当天有记录",
            '受试者编号': item['受试者编号'],
            '不良事件名称': item['不良事件名称_aeterm'],
            '开始日期': str(item['开始日期_aestdat']).strip(),
            'd1用药日期': '',
            '研究结束日期': ''
        })
    return processed_data


def process_rule2_data(rule2_data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """处理规则2数据"""
    processed_data = []
    for item in rule2_data:
        # 获取开始日期
        start_date = str(item.get('开始日期_aestdat', '未知日期')).strip()
        if not start_date or start_date == 'nan':
            start_date = '未知日期'

        processed_data.append({
            '规则': '规则2',
            '描述': f"受试者编号为{item['受试者编号']}的开始日期为{start_date}的不良事件{item['不良事件名称_aeterm']}在ae表中存在多条记录",
            '受试者编号': item['受试者编号'],
            '不良事件名称': item['不良事件名称_aeterm'],
            '开始日期': start_date,
            'd1用药日期': '',
            '研究结束日期': ''
        })
    return processed_data


def process_rule3_data(rule3_data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """处理规则3数据"""
    processed_data = []
    for item in rule3_data:
        processed_data.append({
            '规则': '规则3',
            '描述': f"ae表里的受试者为{item['受试者编号']}的不良事件{item['不良事件名称_aeterm']}的开始日期{item['开始日期_aestdat']}不在d1用药日期{item['d1用药日期']}和研究结束日期{item['研究结束日期']}之间",
            '受试者编号': item['受试者编号'],
            '不良事件名称': str(item['不良事件名称_aeterm']).strip(),
            '开始日期': str(item['开始日期_aestdat']).strip(),
            'd1用药日期': str(item['d1用药日期']).strip(),
            '研究结束日期': str(item['研究结束日期']).strip()
        })
    return processed_data


def process_rule5_data(rule5_data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """处理规则5数据 - AEOUT是否规范"""
    processed_data = []
    for item in rule5_data:
        processed_data.append({
            '规则': '规则5',
            '描述': f"受试者编号为{item['受试者编号']}的开始日期为{str(item['开始日期_aestdat']).strip()}的不良事件{item['不良事件名称_aeterm']}的AEOUT不规范：\n\n{item['判断理由']}",
            '受试者编号': item['受试者编号'],
            '不良事件名称': str(item['不良事件名称_aeterm']).strip(),
            '开始日期': str(item['开始日期_aestdat']).strip(),
            'd1用药日期': '',
            '研究结束日期': ''
        })
    return processed_data


def process_rule7_data(rule7_data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """处理规则7数据 - 治疗手段是否合理"""
    processed_data = []
    for item in rule7_data:
        # 处理治疗手段信息
        treatment_info = str(item.get('治疗手段', '')).strip()
        ctcae_grade = str(item.get('ctcae分级_aectcae', '')).strip()

        # 获取不良事件开始日期和治疗开始日期
        ae_start_date = ''  # 不良事件开始日期
        treatment_start_date = ''  # 治疗开始日期

        # 获取不良事件开始日期
        if item.get('开始日期_aestdat'):
            ae_start_date = str(item['开始日期_aestdat']).strip()

        # 从治疗手段中提取治疗开始时间
        if '用药开始时间：' in treatment_info:
            import re
            match = re.search(r'用药开始时间：(\d{4}-\d{2}-\d{2})', treatment_info)
            if match:
                treatment_start_date = match.group(1)
        elif '治疗开始时间：' in treatment_info:
            # 兼容原有的治疗开始时间格式
            import re
            match = re.search(r'治疗开始时间：(\d{4}-\d{2}-\d{2})', treatment_info)
            if match:
                treatment_start_date = match.group(1)

        # 确定要在描述中使用的开始日期
        # 优先使用不良事件开始日期，如果没有则使用治疗开始日期
        display_start_date = ae_start_date if ae_start_date else treatment_start_date

        # 构建描述，始终包含开始日期信息
        if display_start_date:
            # 如果有不良事件开始日期，使用它；否则使用治疗开始日期并标注
            if ae_start_date:
                description = f"受试者编号为{item['受试者编号']}的不良事件{item['不良事件名称_aeterm']}（开始日期：{ae_start_date}，{ctcae_grade}）的治疗手段不合理：\n\n{treatment_info}。\n\n判断依据：{item['判断依据']}"
            else:
                description = f"受试者编号为{item['受试者编号']}的不良事件{item['不良事件名称_aeterm']}（{ctcae_grade}）的治疗手段不合理：\n\n{treatment_info}。\n\n判断依据：{item['判断依据']}"
        else:
            # 如果没有找到任何日期信息，显示"未知日期"
            description = f"受试者编号为{item['受试者编号']}的不良事件{item['不良事件名称_aeterm']}（{ctcae_grade}）的治疗手段不合理：\n\n{treatment_info}。\n\n判断依据：{item['判断依据']}"
            display_start_date = '未知日期'

        processed_data.append({
            '规则': '规则7',
            '描述': description,
            '受试者编号': item['受试者编号'],
            '不良事件名称': str(item['不良事件名称_aeterm']).strip(),
            '开始日期': display_start_date,
            'd1用药日期': '',
            '研究结束日期': ''
        })
    return processed_data


def process_rule8_data(rule8_data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """处理规则8数据 - SAE是否有效"""
    processed_data = []
    for item in rule8_data:
        start_date = str(item.get('开始日期_aestdat', '未知日期')).strip()
        if not start_date or start_date == 'nan':
            start_date = '未知日期'

        # 构建描述，始终包含开始日期信息
        description = f"受试者编号为{item['受试者编号']}的开始日期为{start_date}的不良事件{item['不良事件名称_aeterm']}标记为严重不良事件但可能不是SAE"

        processed_data.append({
            '规则': '规则8',
            '描述': description,
            '受试者编号': item['受试者编号'],
            '不良事件名称': str(item['不良事件名称_aeterm']).strip(),
            '开始日期': start_date,
            'd1用药日期': '',
            '研究结束日期': ''
        })
    return processed_data


def fill_medical_report_excel(report_data: Dict[str, List[Dict[str, Any]]],
                              template_path: str,
                              output_path: str) -> Dict[str, Any]:
    """
    将医学报告数据填充到Excel模板

    Args:
        report_data: 包含各规则数据的字典
        template_path: Excel模板文件路径
        output_path: 输出文件路径

    Returns:
        包含处理结果信息的字典
    """

    try:
        # 处理各规则数据
        all_data = []
        all_data.extend(process_rule1_data(report_data.get('rule1_no_matching_data', [])))
        all_data.extend(process_rule2_data(report_data.get('rule2_duplicate_records', [])))
        all_data.extend(process_rule3_data(report_data.get('rule3_invalid_dates', [])))
        all_data.extend(process_rule5_data(report_data.get('rule5_non_compliant_aeout', [])))
        all_data.extend(process_rule7_data(report_data.get('rule7_unreasonable_treatments', [])))
        all_data.extend(process_rule8_data(report_data.get('rule8_invalid_sae', [])))

        if not all_data:
            raise ValueError("没有找到需要填充的数据")

        # 创建DataFrame用于统计
        df = pd.DataFrame(all_data)

        # 加载Excel模板
        wb = load_workbook(template_path)

        # 检查是否存在指定的工作表
        target_sheet_name = None
        for sheet_name in wb.sheetnames:
            if 'Medical Data Review Report' in str(sheet_name) or 'Medical Review Report' in str(sheet_name):
                target_sheet_name = sheet_name
                break

        if not target_sheet_name:
            raise ValueError(f"未找到Medical Data Review Report工作表。可用的工作表: {wb.sheetnames}")

        ws = wb[target_sheet_name]

        # 清除现有数据（从第3行开始）
        max_row = ws.max_row if ws.max_row > 2 else 3
        for row in ws.iter_rows(min_row=3, max_row=max_row):
            for cell in row:
                cell.value = None

        # 定义边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # 定义自动换行对齐样式
        wrap_alignment = Alignment(
            wrap_text=True,  # 自动换行
            vertical='top',  # 垂直对齐到顶部
            horizontal='left'  # 水平左对齐
        )

        # 填充数据到工作表（从第3行开始）
        for row_idx, row_data in enumerate(all_data, 3):
            # 表单名称/Listing Name
            cell = ws.cell(row=row_idx, column=1, value="不良事件表 (AE)")
            cell.border = thin_border

            # 受试者编号/Subject No.
            cell = ws.cell(row=row_idx, column=2, value=str(row_data['受试者编号']))
            cell.border = thin_border

            # 访视/Visit - 留空或根据数据填充
            cell = ws.cell(row=row_idx, column=3, value="")
            cell.border = thin_border

            # 页面/Page - 留空或根据数据填充
            cell = ws.cell(row=row_idx, column=4, value="")
            cell.border = thin_border

            # 数据编号/序号/Record Number/Sequence
            cell = ws.cell(row=row_idx, column=5, value=str(row_idx - 2))
            cell.border = thin_border

            # 医学质疑(TMM)/Medical Query Raised by TMM
            cell = ws.cell(row=row_idx, column=6, value=f"{row_data['不良事件名称']}")
            cell.border = thin_border

            # 数据信息/Data Information
            cell = ws.cell(row=row_idx, column=7, value=row_data['描述'])
            cell.border = thin_border
            cell.alignment = wrap_alignment  # 设置自动换行

            # 数据管理员建议/DM Comments - 留空
            cell = ws.cell(row=row_idx, column=8, value="")
            cell.border = thin_border

            # 申办方建议/Sponsor's Comments - 留空
            cell = ws.cell(row=row_idx, column=9, value="")
            cell.border = thin_border

        # 添加表头边框（第2行）
        if ws.max_row >= 2:
            for col in range(1, 10):  # 1到9列
                cell = ws.cell(row=2, column=col)
                cell.border = thin_border

        # 保存文件
        wb.save(output_path)

        # 统计信息
        rule_counts = df['规则'].value_counts().to_dict()

        result_info = {
            'total_records': len(all_data),
            'rule_statistics': rule_counts,
            'generated_at': datetime.now().isoformat(),
            'output_path': output_path
        }

        app_logger.info(f"医学报告Excel填充完成: {output_path}")
        app_logger.info(f"共填充了 {len(all_data)} 条记录")
        app_logger.info(f"各规则统计: {rule_counts}")

        return result_info

    except Exception as e:
        app_logger.error(f"填充医学报告Excel时发生错误: {str(e)}", exc_info=True)
        raise
