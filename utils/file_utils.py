import mimetypes
import os
import zipfile


def unzip_file(zip_path, extract_to):
    # 确保目标目录存在
    if not os.path.exists(extract_to):
        os.makedirs(extract_to)

    # 初始化文件路径列表
    extracted_files = []

    # 打开 ZIP 文件
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        # 解压所有文件到指定目录
        zip_ref.extractall(extract_to)

        # 获取解压后的文件路径列表
        for file_name in zip_ref.namelist():
            extracted_path = os.path.join(extract_to, file_name)
            extracted_files.append(extracted_path)

    return extracted_files


def detect_file_type(filepath: str):
    """
    判断文件类型：若为 image/* 则返回 'image'；若为 application/pdf 则返回 'pdf'。
    """
    mime_type, _ = mimetypes.guess_type(filepath)
    if mime_type:
        if mime_type.startswith('image/'):
            return 'image'
        elif mime_type == 'application/pdf':
            return 'pdf'
    return 'unknown'
