#!/usr/bin/env python3
# -*- coding: utf-8 -*-

'''

这段代码的核心目标是解析 .docx 文件，并将其内容转换成一个结构化的 Python 字典。这个结构化的数据主要服务于两个目的：
1.  **内容层次结构（sections）**: 将文档按照标题（如 "标题1", "标题2"）拆分成一个树状的层级结构。
    每个章节（section）包含了它的标题、级别、以及内容。内容本身又被拆分成了 "ooxml" 和 "image" 两种类型的数据块。
2.  **关键信息提取（key_information）**: 从文档的特定部分（如封面、方案摘要、签字页）提取出关键的键值对信息。

其中，一个非常关键且有些“tricky”的处理是关于 OOXML 的。
当通过 Office.js 或类似技术将 OOXML 块插入到文档中时，插入点后面的段落（即 Word 控件所在的段落）的样式有时会"污染"刚刚插入内容的最后一个段落。
为了解决这个问题，一个非常有效的方法就是 OOXML 内容末尾主动添加一个空的、没有意义的段落（<w:p/>）。
这样，被"污染"的将是这个我们额外添加的空段落，而实际内容的最后一段将保持其原有的、正确的格式。
这个“修复”逻辑在 `_package_complete_ooxml` 函数中实现。

代码中大量使用了 BeautifulSoup 来解析 XML，并利用 zipfile 库直接读取 .docx 文件（它本质上是一个 zip 压缩包）。
'''
from logger.logger import app_logger

import re
import json
import base64
from zipfile import ZipFile
from typing import List, Dict, Optional, Any, Tuple
from bs4 import BeautifulSoup, Tag


class DocxParser:
    """
    一个强大的 .docx 文件解析器。

    本类将一个 .docx 文件（本质上是一个包含多个 XML 文件的 ZIP 压缩包）
    解析为一个结构化的 Python 对象。它能够识别文档的层级结构（基于标题样式），
    并提取内容，将其区分为 OOXML 文本块和 Base64 编码的图片。

    核心功能是将文档内容切片，并将每个切片（无论是标题还是普通内容）
    都包装成一个独立的、完整的、可被 Office.js 直接使用的 OOXML 包。
    """

    def __init__(self, docx_path: str):
        """
        初始化 DocxParser 实例。

        在构造函数中，会立即执行以下预加载操作：
        1. 以 ZIP 格式打开 .docx 文件。
        2. 读取核心的 XML 文件（document.xml, styles.xml, numbering.xml）到内存中。
        3. 解析 XML 的命名空间（namespaces），这对于后续的 XML 处理至关重要。
        4. 解析关系（_rels）、样式（styles）和编号（numbering）信息，供后续函数调用。

        参数:
            docx_path (str): 指向 .docx 文件的本地路径。
        """
        self.docx_path = docx_path
        self.zip_file = ZipFile(docx_path)
        self.table_styles: Dict[str, Any] = {}
        self.default_para_style_id: Optional[str] = None

        # 预加载所有需要的 XML 内容和命名空间，避免重复IO操作
        self.doc_xml_content = self._read_xml("word/document.xml")
        self.styles_xml_content = self._read_xml("word/styles.xml")
        self.numbering_xml_content = self._read_xml("word/numbering.xml")
        self.namespaces = self._get_document_namespaces()

        # 预解析文档的关键组成部分
        self.rels = self._parse_rels()
        self.numbering = self._parse_numbering(self.numbering_xml_content)
        self.styles = self._parse_styles(self.styles_xml_content)

    def _get_document_namespaces(self) -> Dict[str, str]:
        """
        从 document.xml 的根元素中提取所有命名空间（namespace）声明。

        OOXML 严重依赖 XML 命名空间（如 'w:', 'r:', 'a:' 等）。
        为了能在我们生成的 OOXML 包中正确地使用这些前缀，
        我们必须首先从原始文档中提取这些声明，并在生成时原样复制。

        返回:
            Dict[str, str]: 一个字典，键是命名空间前缀（如 'xmlns:w'），值是其 URI。
                            如果失败，则返回空字典。
        """
        if not self.doc_xml_content:
            return {}
        try:
            # 使用 BeautifulSoup 解析 XML
            soup = BeautifulSoup(self.doc_xml_content, "xml")
            # 找到 Word 文档的根标签 <w:document>
            doc_element = soup.find("w:document")
            if doc_element and hasattr(doc_element, 'attrs'):
                # 从标签属性中过滤出所有 'xmlns:' 开头的命名空间声明
                return {k.replace("xmlns:", "xmlns:"): v for k, v in doc_element.attrs.items() if
                        k.startswith("xmlns:")}
            app_logger.warning("无法在 document.xml 中找到 <w:document> 标签。")
            return {}
        except Exception as e:
            app_logger.error(f"提取命名空间时出错: {e}", exc_info=True)
            return {}

    def _generate_content_types(self) -> str:
        """
        生成一个标准的 [Content_Types].xml 文件内容。

        这是任何 OOXML 包（.docx, .pptx, .xlsx）都必须包含的“清单”文件。
        它告诉 Office 应用程序包里每个文件（Part）的类型是什么。
        这里我们只定义了我们需要的最小集合。

        返回:
            str: [Content_Types].xml 的完整 XML 字符串。
        """
        return """<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
<Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
<Default Extension="xml" ContentType="application/xml"/>
<Override PartName="/word/document.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"/>
<Override PartName="/word/styles.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml"/>
<Override PartName="/word/numbering.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml"/>
</Types>"""

    def _package_complete_ooxml(self, xml_fragments: List[str], styles_xml: Optional[bytes],
                                numbering_xml: Optional[bytes], add_empty_paragraph: bool = True) -> str:
        """
        [★★ 核心函数 - 将 OOXML 片段包装成可用的完整 OOXML 包 ★★]

        此函数接收一个或多个原始的 OOXML 片段（例如 `<w:p>...</w:p>` 或 `<w:tbl>...</w:tbl>`)，
        并将它们与样式、编号等依赖项一起，包装成一个符合 Office Open XML Package 规范的、
        可以被 Office.js 的 `insertOoxml` API 直接使用的完整 XML 字符串。

        这个“包”是一个扁平化的 XML 结构，使用 `<pkg:part>` 来模拟 ZIP 包中的文件和目录结构。

        参数:
            xml_fragments (List[str]): 一个包含原始 OOXML 元素（如段落、表格）字符串的列表。
            styles_xml (Optional[bytes]): 整个 `word/styles.xml` 文件的字节内容。
            numbering_xml (Optional[bytes]): 整个 `word/numbering.xml` 文件的字节内容。
            add_empty_paragraph (bool): 是否在内容末尾添加一个空的 `<w:p/>` 段落。
                                        这对于修复 Office.js 插入内容时，末尾段落样式被“污染”的问题至关重要。
                                        对于正文内容块，应为 True；对于独立的标题，应为 False。

        返回:
            str: 一个包含了所有必要部分（rels, content_types, styles, numbering, document）的完整 OOXML 包字符串。
                 该字符串经过了压缩，移除了所有换行符。
        """
        # 1. 准备命名空间声明字符串，供 <w:document> 标签使用
        ns_declarations = ' '.join([f'{k}="{v}"' for k, v in self.namespaces.items()])

        # 2. 将所有传入的 XML 片段合并成一个单一的内容字符串
        combined_content = "".join(xml_fragments)

        # 3. 【★★★ 关键修复逻辑 ★★★】
        # 根据参数决定是否在合并后的内容末尾添加一个空段落
        if add_empty_paragraph:
            combined_content += "<w:p/>"

        # 4. 生成或准备包的各个部分 (pkg:part)
        # 4.1. [Content_Types].xml: 定义包内各部分的内容类型
        content_types = self._generate_content_types()

        # 4.2. styles.xml: 如果提供了样式数据，则创建样式部分
        styles_part = ""
        if styles_xml:
            styles_data = styles_xml.decode('utf-8').replace('<?xml version="1.0" encoding="UTF-8" standalone="yes"?>',
                                                             '')
            styles_part = f"""<pkg:part pkg:name="/word/styles.xml" pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml"><pkg:xmlData>{styles_data}</pkg:xmlData></pkg:part>"""

        # 4.3. numbering.xml: 如果提供了编号数据，则创建编号部分
        numbering_part = ""
        if numbering_xml:
            numbering_data = numbering_xml.decode('utf-8').replace(
                '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>', '')
            numbering_part = f"""<pkg:part pkg:name="/word/numbering.xml" pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml"><pkg:xmlData>{numbering_data}</pkg:xmlData></pkg:part>"""

        # 4.4. document.xml.rels: 定义 document.xml 与其他部分（如 styles, numbering）的关系
        relationships_for_doc = f"""<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rIdStyles" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles" Target="styles.xml"/><Relationship Id="rIdNumbering" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering" Target="numbering.xml"/></Relationships>"""

        # 4.5. _rels/.rels: 定义包的顶层关系，主要是指向主文档 (document.xml)
        top_level_relationships = f"""<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml"/></Relationships>"""

        # 5. 组装最终的 OOXML 包
        #    这个结构模仿了真实的 .docx 压缩包，但表现为单个 XML 文件。
        ooxml_package = f"""<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?mso-application progid="Word.Document"?>
<pkg:package xmlns:pkg="http://schemas.microsoft.com/office/2006/xmlPackage">
<pkg:part pkg:name="/_rels/.rels" pkg:contentType="application/vnd.openxmlformats-package.relationships+xml"><pkg:xmlData>{top_level_relationships}</pkg:xmlData></pkg:part>
<pkg:part pkg:name="/word/_rels/document.xml.rels" pkg:contentType="application/vnd.openxmlformats-package.relationships+xml"><pkg:xmlData>{relationships_for_doc}</pkg:xmlData></pkg:part>
<pkg:part pkg:name="/word/document.xml" pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"><pkg:xmlData><w:document {ns_declarations}><w:body>{combined_content}</w:body></w:document></pkg:xmlData></pkg:part>
{styles_part}
{numbering_part}
</pkg:package>
"""
        # 移除所有换行和首尾空格，得到单行字符串，便于API调用
        return ''.join(line.strip() for line in ooxml_package.split('\n'))

    def _read_xml(self, path: str) -> Optional[bytes]:
        """
        从 .docx 压缩包中安全地读取一个指定的 XML 文件。

        参数:
            path (str): 要读取的文件在压缩包内的路径 (例如 "word/document.xml")。

        返回:
            Optional[bytes]: 文件的字节内容。如果文件不存在，则返回 None 并记录警告。
        """
        try:
            with self.zip_file.open(path) as f:
                return f.read()
        except KeyError:
            app_logger.warning(f"XML 文件未找到: {path}")
            return None

    def _parse_rels(self) -> Dict[str, str]:
        """
        解析文档的关系文件 (word/_rels/document.xml.rels)。

        这个文件定义了主文档与其他资源（如图片、超链接、页眉页脚）之间的关联。
        我们主要用它来查找图片等内嵌资源的具体文件路径。

        返回:
            Dict[str, str]: 一个关系字典，键是关系ID (rId)，值是目标文件路径。
        """
        rels_content = self._read_xml("word/_rels/document.xml.rels")
        if not rels_content:
            return {}
        soup = BeautifulSoup(rels_content, "xml")
        return {rel['Id']: rel['Target'] for rel in soup.find_all("Relationship")}

    def _to_points(self, value: Optional[str], unit: str = 'dxa') -> Optional[float]:
        """
        将 OOXML 中常用的单位（如 dxa, half-points）转换为标准的“点”(Points)。

        - 'dxa' (twentieths of a point): 1/20 磅，常用于缩进和间距。
        - 'half-points': 1/2 磅，常用于字号。
        - 'eighth-points': 1/8 磅。

        参数:
            value (Optional[str]): 从 XML 中读取的原始值字符串。
            unit (str): 值的单位，默认为 'dxa'。

        返回:
            Optional[float]: 转换后的磅值。如果输入无效，则返回 None。
        """
        if value is None: return None
        try:
            numeric_value = float(value)
            if unit == 'dxa': return numeric_value / 20.0
            if unit == 'half-points': return numeric_value / 2.0
            if unit == 'eighth-points': return numeric_value / 8.0
            return numeric_value
        except (ValueError, TypeError):
            return None

    def _extract_character_properties(self, r_pr_tag: Optional[Tag]) -> Dict[str, Any]:
        """
        从 <w:rPr> (Run Properties) 标签中提取字符级别的格式信息。

        例如：加粗、斜体、下划线、字号、颜色等。

        参数:
            r_pr_tag (Optional[Tag]): BeautifulSoup 解析出的 <w:rPr> 标签对象。

        返回:
            Dict[str, Any]: 包含字符格式属性的字典。
        """
        if not r_pr_tag: return {}
        properties = {}
        if r_pr_tag.find("w:b"): properties["bold"] = True
        if r_pr_tag.find("w:i"): properties["italic"] = True
        if u_tag := r_pr_tag.find("w:u"): properties["underline"] = u_tag.get("w:val", "single")
        if r_pr_tag.find("w:strike"): properties["strike"] = True
        if r_pr_tag.find("w:vertAlign", {"w:val": "superscript"}): properties["superscript"] = True
        if r_pr_tag.find("w:vertAlign", {"w:val": "subscript"}): properties["subscript"] = True
        if (sz := r_pr_tag.find("w:sz")) and sz.get("w:val"):
            properties["size"] = self._to_points(sz.get("w:val"), 'half-points')
        if (color := r_pr_tag.find("w:color")) and color.get("w:val"): properties["color"] = color.get("w:val")
        if font_tag := r_pr_tag.find("w:rFonts"):
            properties["font"] = {k: v for k, v in {"ascii": font_tag.get("w:ascii"), "hAnsi": font_tag.get("w:hAnsi"),
                                                    "eastAsia": font_tag.get("w:eastAsia"),
                                                    "hint": font_tag.get("w:hint")}.items() if v is not None}
        return properties

    def _extract_paragraph_properties(self, p_pr_tag: Optional[Tag]) -> Dict[str, Any]:
        """
        从 <w:pPr> (Paragraph Properties) 标签中提取段落级别的格式信息。

        例如：对齐方式、缩进、行距、段前段后间距等。

        参数:
            p_pr_tag (Optional[Tag]): BeautifulSoup 解析出的 <w:pPr> 标签对象。

        返回:
            Dict[str, Any]: 包含段落和默认字符格式属性的字典。
        """
        if not p_pr_tag: return {}
        para_props = {}
        if (jc := p_pr_tag.find("w:jc")) and jc.get("w:val"): para_props["alignment"] = jc.get("w:val")
        if ind := p_pr_tag.find("w:ind"):
            para_props["indent"] = {k: v for k, v in {"left": self._to_points(ind.get("w:left")),
                                                      "right": self._to_points(ind.get("w:right")),
                                                      "firstLine": self._to_points(ind.get("w:firstLine")),
                                                      "hanging": self._to_points(ind.get("w:hanging"))}.items() if
                                    v is not None}
        if spacing := p_pr_tag.find("w:spacing"):
            spacing_props = {"before": self._to_points(spacing.get("w:before")),
                             "after": self._to_points(spacing.get("w:after")), }
            if (line_val := spacing.get("w:line")) and (line_rule := spacing.get("w:lineRule")):
                if line_rule == "auto":
                    try:
                        spacing_props["lineHeight"] = {"mode": "multiple", "value": float(line_val) / 240.0}
                    except ValueError:
                        pass
                else:
                    spacing_props["lineHeight"] = {"mode": line_rule, "value": self._to_points(line_val)}
            para_props["spacing"] = {k: v for k, v in spacing_props.items() if v is not None}
        # 段落属性中可能也包含应用于整个段落的默认字符属性 (<w:rPr>)
        char_props = self._extract_character_properties(p_pr_tag.find("w:rPr"))
        result = {}
        if para_props: result["paragraph_properties"] = para_props
        if char_props: result["character_properties"] = char_props
        return result

    def _extract_table_properties(self, tbl_pr_tag: Optional[Tag]) -> Dict[str, Any]:
        """
        从 <w:tblPr> (Table Properties) 标签中提取表格级别的格式信息。

        例如：表格对齐方式、整体宽度等。

        参数:
            tbl_pr_tag (Optional[Tag]): BeautifulSoup 解析出的 <w:tblPr> 标签对象。

        返回:
            Dict[str, Any]: 包含表格格式属性的字典。
        """
        if not tbl_pr_tag: return {}
        properties = {}
        if (jc_tag := tbl_pr_tag.find("w:jc")) and jc_tag.get("w:val"): properties["alignment"] = jc_tag.get("w:val")
        if (tbl_w_tag := tbl_pr_tag.find("w:tblW")) and tbl_w_tag.get("w:w"):
            properties["width"] = {"value": self._to_points(tbl_w_tag.get("w:w")), "type": tbl_w_tag.get("w:type")}
        return properties

    def _parse_styles(self, styles_xml: Optional[bytes]) -> Dict[str, Any]:
        """
        解析 `word/styles.xml` 文件，提取所有样式定义。

        此函数构建一个以样式ID为键的字典，值为该样式的详细信息（名称、类型、格式属性）。
        特别地，它会识别出哪些是“标题”样式（例如 "heading 1"）并记录其级别，
        这是构建文档层级结构的基础。

        参数:
            styles_xml (Optional[bytes]): `word/styles.xml` 文件的字节内容。

        返回:
            Dict[str, Any]: 一个包含所有已解析样式的字典。
        """
        if not styles_xml: return {}
        soup = BeautifulSoup(styles_xml, "xml")
        style_map = {}
        for style in soup.find_all("w:style"):
            style_id = style.get("w:styleId")
            if not style_id: continue
            style_type = style.get("w:type")
            name = (name_tag.get("w:val") if (name_tag := style.find("w:name")) else "")

            # 处理段落样式
            if style_type == "paragraph":
                if style.get("w:default") == "1": self.default_para_style_id = style_id
                level = None
                # 方法一：直接从 <w:outlineLvl> 标签获取大纲级别
                if (outline_lvl := style.find("w:outlineLvl")) and outline_lvl.get("w:val"):
                    level = int(outline_lvl.get("w:val")) + 1  # 级别从0开始，我们转为从1开始
                # 方法二：如果上面方法失败，尝试从样式名称中用正则匹配 "heading X" 或 "标题 X"
                elif (match := re.search(r'(heading|标题)\s*(\d+)', name, re.IGNORECASE)):
                    level = int(match.group(2))

                properties = self._extract_paragraph_properties(style.find("w:pPr"))
                if r_pr_props := self._extract_character_properties(style.find("w:rPr")):
                    properties.setdefault("character_properties", {}).update(r_pr_props)
                style_map[style_id] = {"name": name, "level": level, "type": style_type, "properties": properties}

            # 处理表格样式
            elif style_type == "table":
                table_props = {"name": name, "type": style_type,
                               "properties": self._extract_table_properties(style.find("w:tblPr"))}
                self.table_styles[style_id] = table_props
                style_map[style_id] = table_props
        return style_map

    def _parse_numbering(self, num_xml: Optional[bytes]) -> Dict[str, Any]:
        """
        解析 `word/numbering.xml` 文件，提取所有列表编号和项目符号的定义。

        OOXML 中的列表比较复杂，分为 `abstractNum` (抽象定义) 和 `num` (具体实例)。
        此函数将它们解析并关联起来，构建一个以 `numId` 为键的字典，
        值为该列表各级别的格式定义（如 "1, 2, 3", "a, b, c", "•, •, •"）。

        参数:
            num_xml (Optional[bytes]): `word/numbering.xml` 文件的字节内容。

        返回:
            Dict[str, Any]: 一个包含所有已解析编号列表定义的字典。
        """
        if not num_xml: return {}
        soup = BeautifulSoup(num_xml, "xml")

        # 1. 解析抽象编号定义 (abstractNum)
        abstract_nums = {}
        for anum in soup.find_all("w:abstractNum"):
            abstract_num_id = anum.get("w:abstractNumId")
            if not abstract_num_id: continue
            levels = {}
            for lvl in anum.find_all("w:lvl"):
                ilvl, num_fmt_tag = lvl.get("w:ilvl"), lvl.find("w:numFmt")
                lvl_text_tag, start_tag = lvl.find("w:lvlText"), lvl.find("w:start")
                if not (ilvl and num_fmt_tag): continue
                level_data = {"format": num_fmt_tag.get("w:val"),
                              "text": lvl_text_tag.get("w:val") if lvl_text_tag else "",
                              "start": int(start_tag.get("w:val")) if start_tag and start_tag.get("w:val") else 1}
                levels[ilvl] = level_data
            abstract_nums[abstract_num_id] = levels

        # 2. 解析具体编号实例 (num)，并将其链接到抽象定义
        num_map = {}
        for num in soup.find_all("w:num"):
            num_id = num.get("w:numId")
            if not num_id: continue
            if abstract_num_id_tag := num.find("w:abstractNumId"):
                abstract_num_id = abstract_num_id_tag.get("w:val")
                if abstract_num_id in abstract_nums:
                    num_map[num_id] = abstract_nums[abstract_num_id]
        return num_map

    def _get_image_data(self, r_id: str) -> Optional[str]:
        """
        根据资源关系ID (rId) 获取图片的 Base64 编码字符串。

        当在文档 XML 中遇到一个图片时，它只包含一个指向关系文件的ID（如 "rId8"）。
        此函数使用这个ID：
        1. 在 `self.rels` 字典中查找到图片的真实文件路径（如 "media/image1.png"）。
        2. 从 .docx (zip) 文件中读取该图片文件。
        3. 将图片内容编码为 Base64 字符串，以便可以嵌入到 JSON 或 HTML 中。

        参数:
            r_id (str): 图片的资源关系ID。

        返回:
            Optional[str]: 图片的 Base64 编码字符串。如果找不到，则返回 None。
        """
        if r_id in self.rels:
            target = self.rels[r_id]
            from os.path import normpath, join
            # 路径需要规范化，因为 target 可能是 "../media/image1.png" 这样的相对路径
            base_path = "word"
            image_path = normpath(join(base_path, target)).replace('\\', '/')
            try:
                with self.zip_file.open(image_path) as img_file:
                    return base64.b64encode(img_file.read()).decode('utf-8')
            except KeyError:
                app_logger.error(f"图片文件未在压缩包中找到: {image_path}")
        return None

    def _parse_runs(self, p_tag: Tag) -> str:
        """
        解析一个段落标签(<w:p>)中的所有文本块(<w:r>)，并拼接它们的文本内容(<w:t>)。

        一个段落的文本可能被分割在多个 "run" 中，每个 run 可以有不同的格式。
        此函数只提取纯文本内容。

        参数:
            p_tag (Tag): BeautifulSoup 解析出的 <w:p> 标签对象。

        返回:
            str: 该段落的完整纯文本内容。
        """
        return "".join(t.text for t in p_tag.find_all('w:t'))

    def _get_paragraph_text(self, p_element: Any) -> str:
        """
        从段落的 XML 元素或字符串中提取纯文本。
        这是一个对 `_parse_runs` 的封装，可以处理字符串或 BeautifulSoup 标签。

        参数:
            p_element (Any): 可以是 <w:p> 元素的 XML 字符串，或 BeautifulSoup 的 Tag 对象。

        返回:
            str: 段落的纯文本。
        """
        if isinstance(p_element, str):
            p_element = BeautifulSoup(p_element, "xml").find("w:p")
        if p_element:
            return self._parse_runs(p_element)
        return ""

    def _get_text_from_element(self, element_xml: str) -> str:
        """
        从任何给定的 OOXML 元素（字符串格式）中递归地提取所有纯文本。

        这对于从一个大的 OOXML 块（可能包含多个段落、表格）中提取全部文本很有用。

        参数:
            element_xml (str): 包含一个或多个 OOXML 元素的 XML 字符串。

        返回:
            str: 拼接后的所有纯文本。
        """
        soup = BeautifulSoup(element_xml, "xml")
        all_text = []
        for p_tag in soup.find_all("w:p"):
            text = self._parse_runs(p_tag)
            if text.strip():
                all_text.append(text)
        return "".join(all_text)

    def _get_text_from_cell_content(self, content_list: List[Dict[str, str]]) -> str:
        """
        从一个包含多个内容块的列表中提取所有纯文本。
        这主要用于处理表格单元格，因为一个单元格可能包含多个段落。

        参数:
            content_list (List[Dict[str, str]]): 内容块列表，每个块是一个字典。

        返回:
            str: 拼接后的所有纯文本。
        """
        full_text = []
        for block in content_list:
            if block.get("type") == "ooxml":
                text = self._get_text_from_element(block.get("data", ""))
                if text.strip():
                    full_text.append(text)
        return "".join(full_text).strip()

    def _extract_kv_from_table_block(self, table_block: Dict[str, str]) -> Dict[str, str]:
        """
        从一个包含表格的 OOXML 内容块中，提取键值对（Key-Value）。

        此函数假定表格是两列的结构，第一列是键，第二列是值。
        这是一种针对特定文档格式的、业务逻辑较强的解析。

        参数:
            table_block (Dict[str, str]): 类型为 'ooxml' 的内容块，其 'data' 字段包含表格的 OOXML。

        返回:
            Dict[str, str]: 从表格中提取的键值对字典。
        """
        kv_data = {}
        if table_block.get("type") != "ooxml":
            return kv_data
        table_xml = table_block.get("data", "")
        soup = BeautifulSoup(table_xml, "xml")
        table = soup.find("w:tbl")
        if not table:
            return kv_data
        for row in table.find_all("w:tr", recursive=False):
            cells = row.find_all("w:tc", recursive=False)
            if len(cells) == 2:
                key_text = self._get_paragraph_text(cells[0])
                value_text = self._get_paragraph_text(cells[1])
                if key_text:
                    kv_data[key_text.strip()] = value_text.strip()
        return kv_data

    def _extract_kv_from_colon_paragraph_block(self, p_block: Dict[str, str]) -> Optional[Tuple[str, str]]:
        """
        从一个段落内容块中，提取由冒号分隔的键值对。

        例如，对于文本 "项目名称：XXX项目"，此函数会提取 ("项目名称", "XXX项目")。
        支持中英文冒号。这也是一种业务逻辑较强的解析。

        参数:
            p_block (Dict[str, str]): 类型为 'ooxml' 的内容块，其 'data' 字段包含段落的 OOXML。

        返回:
            Optional[Tuple[str, str]]: 如果成功提取，返回 (键, 值) 元组；否则返回 None。
        """
        if p_block.get("type") != "ooxml":
            return None
        text = self._get_paragraph_text(p_block.get("data", "")).strip()
        # 兼容中文和英文冒号
        if '：' in text:
            parts = text.split('：', 1)
        elif ':' in text:
            parts = text.split(':', 1)
        else:
            return None
        key = parts[0].strip()
        value = parts[1].strip()
        if key:
            return key, value
        return None

    def _process_title_page_content_blocks(self, content_blocks: List[Dict[str, str]]) -> Dict[str, str]:
        """
        处理文档标题页（封面）的内容块，从中提取所有的键值对信息。

        它会综合运用表格解析和冒号段落解析两种策略。

        参数:
            content_blocks (List[Dict[str, str]]): 从文档开头到特定分隔符（如“保密声明”）之间的所有内容块。

        返回:
            Dict[str, str]: 从标题页提取的所有键值对的集合。
        """
        kv_data = {}
        for block in content_blocks:
            if block.get("type") != "ooxml":
                continue
            soup = BeautifulSoup(block.get("data", ""), "xml")
            # 如果块是表格，用表格解析器
            if soup.find("w:tbl"):
                table_kvs = self._extract_kv_from_table_block(block)
                kv_data.update(table_kvs)
            # 如果块是段落，用冒号段落解析器
            elif soup.find("w:p"):
                p_kv = self._extract_kv_from_colon_paragraph_block(block)
                if p_kv:
                    kv_data[p_kv[0]] = p_kv[1]
        return kv_data

    def _find_section_by_title(self, nodes: List[Dict[str, Any]], title_keyword: str) -> Optional[Dict[str, Any]]:
        """
        在文档的层级结构中，递归地查找第一个标题包含特定关键字的章节。

        参数:
            nodes (List[Dict[str, Any]]): 要搜索的章节节点列表（即 `self.sections`）。
            title_keyword (str): 要在章节标题中搜索的关键字。

        返回:
            Optional[Dict[str, Any]]: 找到的第一个匹配的章节字典。如果没找到，返回 None。
        """
        for node in nodes:
            if node.get("title") and title_keyword in node.get("title", ""):
                return node
            if "children" in node and node["children"]:
                found_in_child = self._find_section_by_title(node.get("children", []), title_keyword)
                if found_in_child:
                    return found_in_child
        return None

    def _generate_table_of_contents(self, sections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        根据已构建的文档层级结构，生成一个简单的目录（Table of Contents）。

        这个目录本身也是一个嵌套的列表，反映了文档的章节结构。

        参数:
            sections (List[Dict[str, Any]]): 文档的顶层章节列表。

        返回:
            List[Dict[str, Any]]: 表示目录的嵌套列表。
        """
        table_of_contents = []
        for section in sections:
            if title_text := section.get("title"):
                title_text = title_text.strip()
                if not title_text: continue
                entry = {"title": title_text, "level": section.get("level", 1)}
                if children := section.get("children"):
                    child_toc = self._generate_table_of_contents(children)
                    if child_toc: entry["children"] = child_toc
                table_of_contents.append(entry)
        return table_of_contents

    def _build_document_hierarchy(self, body_element: Tag) -> List[Dict[str, Any]]:
        """
        [★★ 核心函数 - 构建文档的层级结构 ★★]

        此函数遍历 `<w:body>` 中的所有顶级元素（段落、表格等），
        通过识别应用了“标题”样式的段落，来构建一个嵌套的树状（或层级）结构。

        处理流程：
        1. 遍历 body 的每个子元素。
        2. 如果元素是一个标题段落，则认为一个旧章节结束，一个新章节开始。
        3. 将旧章节缓存的内容（段落、表格、图片）打包处理，存入旧章节的 `content_blocks`。
        4. 创建新的章节节点，并根据标题级别放入层级结构的正确位置。
        5. 如果元素不是标题，则将其追加到当前章节的内容缓存中。
        6. 在处理过程中，图片会被识别并单独作为一个 "image" 类型的块，其他都是 "ooxml" 块。

        参数:
            body_element (Tag): BeautifulSoup 解析出的 `<w:body>` 标签对象。

        返回:
            List[Dict[str, Any]]: 一个代表整个文档结构的顶级章节列表。每个章节都是一个字典，
                                  可能包含 `children` 字段来表示子章节。
        """
        node_path: List[Dict[str, Any]] = []  # 维护从根到当前节点的路径，像一个栈
        result_sections = []  # 最终返回的顶级章节列表
        section_content_elements: List[Tag] = []  # 临时收集当前章节的所有内容元素

        def _finalize_current_section():
            """
            辅助函数：完成当前章节的内容收集与打包。
            当遇到新标题或文档结束时调用。
            """
            if not (node_path and section_content_elements):
                return

            current_section = node_path[-1]
            ooxml_fragments_buffer = []

            def package_buffer():
                """将缓冲区中的 OOXML 片段打包成一个内容块。"""
                if ooxml_fragments_buffer:
                    # 【关键】对每个由多个元素组成的 ooxml 内容块，在末尾添加空段落
                    complete_ooxml = self._package_complete_ooxml(
                        ooxml_fragments_buffer,
                        self.styles_xml_content,
                        self.numbering_xml_content,
                        add_empty_paragraph=True
                    )
                    current_section['content_blocks'].append({
                        "type": "ooxml",
                        "data": complete_ooxml
                    })
                    ooxml_fragments_buffer.clear()

            # 遍历缓存的章节内容元素
            for element in section_content_elements:
                # 检查元素是否包含图片
                blip_tag = element.find("a:blip", {"r:embed": True})
                if blip_tag:
                    # 发现图片，意味着一个内容块的中断。
                    # 1. 先打包在它之前的所有 OOXML 内容。
                    package_buffer()

                    # 2. 处理并添加图片块。
                    r_id = blip_tag['r:embed']
                    base64_data = self._get_image_data(r_id)
                    if base64_data:
                        current_section['content_blocks'].append({"type": "image", "data": base64_data})
                    else:
                        app_logger.warning(f"无法获取 rId 为 {r_id} 的图片 Base64 数据，将此块存为 ooxml")
                        ooxml_fragments_buffer.append(str(element))
                else:
                    # 如果不是图片，将其 XML 字符串添加到缓冲区
                    ooxml_fragments_buffer.append(str(element))

            # 循环结束后，打包所有剩余的 OOXML 内容
            package_buffer()
            section_content_elements.clear()

        # --- 主循环：遍历 body 内所有元素 ---
        for element in body_element.children:
            if not isinstance(element, Tag) or not element.name:
                continue

            item_level, title_text = None, None

            # 判断当前元素是否是一个“标题”段落
            if element.name == "p":
                p_pr = element.find("w:pPr")
                if p_pr and (p_style_tag := p_pr.find("w:pStyle")):
                    style_id = p_style_tag.get("w:val")
                    if style_id in self.styles:
                        style_info = self.styles[style_id]
                        if style_info.get("level") is not None:
                            item_level = style_info["level"]
                            title_text = self._get_paragraph_text(element)

            # 如果是标题，则开启新章节
            if item_level is not None and title_text is not None:
                # 1. 先完成并打包前一个章节的内容
                _finalize_current_section()

                # 2. 为标题本身生成一个独立的、完整的 OOXML 包
                #    【注意】标题本身不需要添加末尾的空段落，所以 add_empty_paragraph=False
                title_ooxml = self._package_complete_ooxml(
                    [str(element)],
                    self.styles_xml_content,
                    self.numbering_xml_content,
                    add_empty_paragraph=False
                )

                # 3. 创建新的章节节点
                section_node = {
                    "type": "section",
                    "level": item_level,
                    "title": title_text,
                    "title_ooxml": title_ooxml,  # 存储标题的独立OOXML包
                    "content_blocks": [],
                    "children": []
                }

                # 4. 调整节点在层级树中的位置
                while node_path and node_path[-1]['level'] >= item_level:
                    node_path.pop()

                if node_path:  # 如果路径不为空，作为子节点
                    node_path[-1]['children'].append(section_node)
                else:  # 否则作为顶级节点
                    result_sections.append(section_node)

                node_path.append(section_node)  # 将当前节点压入路径栈
            else:
                # 如果不是标题，就作为当前章节的内容元素收集起来
                if node_path:
                    section_content_elements.append(element)

        # 文档末尾，处理最后一个章节的内容
        _finalize_current_section()

        return result_sections

    def parse_to_structured_dict(self, extract_keys_only: bool = False) -> Dict[str, Any]:
        """
        执行完整的解析流程，返回最终的结构化字典。

        这个字典包含两个顶级键:
        - "sections": 由 `_build_document_hierarchy` 生成的文档层级结构。
        - "key_information": 从特定章节（封面、摘要、签字页）提取的键值对信息。

        参数:
            extract_keys_only (bool): 如果为 True，则在 key_information 中只保留键，值设为空字符串。
                                      这在需要生成待填写的模板时很有用。

        返回:
            Dict[str, Any]: 包含完整解析结果的字典。
        """
        if not self.doc_xml_content: return {}

        soup = BeautifulSoup(self.doc_xml_content, "xml")
        body = soup.find("w:body")
        if not body: return {}

        # 1. 构建文档的层级内容结构
        hierarchical_content = self._build_document_hierarchy(body)

        # 2. 基于层级结构生成目录
        table_of_contents = self._generate_table_of_contents(hierarchical_content)

        # 3. 为了提取封面等信息，我们需要一个线性的内容块列表
        #    这部分逻辑有些重复，但为了兼容现有的键值提取方法而保留。
        linear_content_blocks = []
        for element in body.children:
            if not isinstance(element, Tag) or not element.name:
                continue
            blip_tag = element.find("a:blip", {"r:embed": True})
            if blip_tag:
                r_id = blip_tag['r:embed']
                base64_data = self._get_image_data(r_id)
                if base64_data:
                    linear_content_blocks.append({"type": "image", "data": base64_data})
                else:
                    # 对于线性的、单个元素组成的块，同样需要打包并添加空段落
                    linear_content_blocks.append({"type": "ooxml", "data": self._package_complete_ooxml([str(element)],
                                                                                                        self.styles_xml_content,
                                                                                                        self.numbering_xml_content,
                                                                                                        add_empty_paragraph=True)})
            else:
                linear_content_blocks.append({"type": "ooxml", "data": self._package_complete_ooxml([str(element)],
                                                                                                    self.styles_xml_content,
                                                                                                    self.numbering_xml_content,
                                                                                                    add_empty_paragraph=True)})
        # 4. 提取特定部分的信息
        # 4.1 提取封面信息：找到“保密声明”作为封面结束的标志
        title_page_data = {}
        split_index = -1
        for i, block in enumerate(linear_content_blocks):
            if block.get("type") == "ooxml":
                element_text = self._get_text_from_element(block.get("data", ""))
                if element_text:
                    cleaned_text = re.sub(r'\s+', '', element_text)
                    if "保密声明" in cleaned_text or "保密申明" in cleaned_text:
                        split_index = i
                        break

        if split_index != -1:
            title_page_blocks = linear_content_blocks[:split_index]
            title_page_data = self._process_title_page_content_blocks(title_page_blocks)
        else:
            app_logger.warning("未找到 '保密声明'或'保密申明'，无法提取标题页信息。")

        # 4.2 提取方案摘要信息
        summary_data = {}
        summary_section = self._find_section_by_title(hierarchical_content, "方案摘要")
        if summary_section:
            for block in summary_section.get("content_blocks", []):
                if block.get("type") == "ooxml":
                    soup = BeautifulSoup(block.get("data", ""), "xml")
                    if soup.find("w:tbl"):  # 摘要信息通常在表格里
                        summary_data = self._extract_kv_from_table_block(block)
                        break

        # 4.3 提取签字页信息
        signature_data = {}
        signature_section = self._find_section_by_title(hierarchical_content, "方案批准签字页")
        if signature_section:
            for block in signature_section.get("content_blocks", []):
                if block.get("type") == "ooxml":
                    soup = BeautifulSoup(block.get("data", ""), "xml")
                    if soup.find("w:tbl"):  # 签字信息通常在表格里
                        signature_data = self._extract_kv_from_table_block(block)
                        break

        # 5. 如果设置了 extract_keys_only，则清空所有提取到的值
        if extract_keys_only:
            app_logger.info("检测到 extract_keys_only=True，将清空 key_information 中相关字段的 value。")
            title_page_data = {key: "" for key in title_page_data.keys()}
            summary_data = {key: "" for key in summary_data.keys()}
            signature_data = {key: "" for key in signature_data.keys()}

        # 6. 组装 key_information 部分，使用更严格的条件来判断字典是否“有意义”。
        #    这个判断对 extract_keys_only 为 true 和 false 的情况都有效。
        key_information = {}

        # 提取到的原始数据源
        sources = {
            "title_page": title_page_data,
            "protocol_summary": summary_data,
            "approval_signature_page": signature_data
        }

        if extract_keys_only:
            # --- 模式一：只提取键 ---
            # 只要原始解析出了数据（字典不为空），就保留其结构，并将所有值置为空字符串
            app_logger.info("模式: extract_keys_only=True. 保留键，值置空。")
            for key, data_dict in sources.items():
                # 过滤掉key为空字符串的特殊情况
                cleaned_keys_dict = {k.strip(): "" for k in data_dict.keys() if k and k.strip()}
                if cleaned_keys_dict:
                    key_information[key] = cleaned_keys_dict
        else:
            # --- 模式二：提取键和值 ---
            # 清洗数据，只保留那些键和值都有效（非空、非纯空格）的条目
            app_logger.info("模式: extract_keys_only=False. 提取有效键值对。")
            for key, data_dict in sources.items():
                cleaned_data = {
                    k.strip(): v.strip()
                    for k, v in data_dict.items()
                    if k and k.strip() and v and v.strip()
                }
                # 只有当清洗后字典仍有内容时，才将其加入最终结果
                if cleaned_data:
                    key_information[key] = cleaned_data

        # 目录（table_of_contents）不受影响，只要有就添加
        if table_of_contents:
            key_information["table_of_contents"] = table_of_contents

        # 返回最终结果
        return {"sections": hierarchical_content, "key_information": key_information}


def extract_structured_protocol(docx_path: str, extract_keys_only: bool = False) -> Dict[str, Any]:
    """
    一个顶层的 API 函数，封装了 DocxParser 的实例化和调用过程。

    这是外部调用者应该使用的主要入口点。它隐藏了内部实现的复杂性。

    参数:
        docx_path (str): 指向 .docx 文件的路径。
        extract_keys_only (bool): 是否只提取键名，用于生成模板。

    返回:
        Dict[str, Any]: 从文档中提取的结构化数据。

    异常:
        FileNotFoundError: 如果提供的 docx_path 不存在。
        Exception: 在解析过程中发生的任何其他未捕获的异常。
    """
    try:
        app_logger.info(f"开始解析 DOCX 文件: {docx_path}")
        parser = DocxParser(docx_path)
        structured_data = parser.parse_to_structured_dict(extract_keys_only=extract_keys_only)
        app_logger.info(f"文件解析成功: {docx_path}")
        return structured_data
    except FileNotFoundError:
        app_logger.error(f"文件未找到: {docx_path}")
        raise
    except Exception as e:
        app_logger.error(f"解析文件时发生未知错误: {docx_path}", exc_info=True)
        raise

if __name__ == '__main__':
    """
    这是一个主执行块，用于直接运行此脚本进行测试和调试。
    它会：
    1. 指定一个 .docx 文件路径。
    2. 调用解析函数。
    3. 将结果以格式化的 JSON 形式保存到文件中。
    4. 在控制台打印出解析结果的概览和一些关键的检查项，
       用于验证核心逻辑（如空段落修复）是否按预期工作。
    """
    try:
        file_path = "./data4extract/jietong/protocol.docx"
        output_filename = "parsed_protocol_v5_debug_commented.json"

        data = extract_structured_protocol(file_path)

        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        app_logger.info(f"解析完成！结果已成功保存到文件: {output_filename}")

        app_logger.info("--- 解析结果概览 ---")
        app_logger.info(f"顶层键: {list(data.keys())}")

        if sections := data.get("sections"):
            app_logger.info(f"共找到 {len(sections)} 个顶级章节。")
            if sections:
                first_section = sections[0]
                app_logger.info(f"第一个顶级章节: 级别={first_section.get('level')}, 标题=\"{first_section.get('title', '')}\"")

                if 'title_ooxml' in first_section and first_section['title_ooxml']:
                    title_ooxml = first_section['title_ooxml']
                    app_logger.info(f"  - [√] 章节包含 'title_ooxml' 字段，数据长度: {len(title_ooxml)}")
                    try:
                        soup = BeautifulSoup(title_ooxml, "xml")
                        doc_part = soup.find("pkg:part", {"pkg:name": "/word/document.xml"})
                        if doc_part and (xml_data := doc_part.find("pkg:xmlData")) and (body_content := xml_data.find("w:body")):
                            body_str = str(body_content)
                            if body_str.endswith("<w:p/></w:body>"):
                                app_logger.warning(f"    - [!] 警告：标题 OOXML 错误地包含了空段落。")
                            else:
                                app_logger.info(f"    - [√] 检查通过：标题 OOXML 未包含末尾的空段落，符合预期。")
                        else:
                            app_logger.warning(f"    - [?] 检查失败：无法在标题 OOXML 中找到 w:body 标签。")
                    except Exception as e:
                        app_logger.error(f"    - [X] 检查标题 OOXML 时出错: {e}")
                else:
                    app_logger.error(f"  - [X] 字段缺失：章节 'title_ooxml' 字段缺失或为空。")

                app_logger.info(f"第一个顶级章节包含 {len(first_section.get('content_blocks', []))} 个内容块。")
                if content_blocks := first_section.get('content_blocks'):
                    for i, block in enumerate(content_blocks[:2]):
                        app_logger.info(f"  - 内容块 {i + 1}: 类型={block.get('type')}")
                        if block.get('type') == 'image':
                            app_logger.info(f"    图片数据长度 (Base64): {len(block.get('data', ''))}")
                        elif block.get('type') == 'ooxml':
                            ooxml_data = block.get('data', '')
                            app_logger.info(f"    OOXML 数据包长度: {len(ooxml_data)}")
                            if "styles.xml" in ooxml_data:
                                app_logger.info("    - [√] OOXML 包中已包含 styles.xml")
                            if "numbering.xml" in ooxml_data:
                                app_logger.info("    - [√] OOXML 包中已包含 numbering.xml")
                            try:
                                soup = BeautifulSoup(ooxml_data, "xml")
                                doc_part = soup.find("pkg:part", {"pkg:name": "/word/document.xml"})
                                if doc_part and (xml_data := doc_part.find("pkg:xmlData")) and (body_content := xml_data.find("w:body")):
                                    body_str = str(body_content)
                                    if body_str.endswith("<w:p/></w:body>"):
                                        app_logger.info("    - [√] 检查通过：内容块正确地以空段落 '<w:p/>' 结尾，修复已应用。")
                                    else:
                                        app_logger.warning("    - [!] 警告：内容块未以空段落结尾。")
                                else:
                                    app_logger.warning(f"    - [?] 检查失败：无法在内容块 OOXML 中找到 w:body 标签。")
                            except Exception as e:
                                app_logger.error(f"    - [X] 检查内容块 OOXML 时出错: {e}")

        if ki := data.get("key_information"):
            app_logger.info(f"\nkey_information 包含: {list(ki.keys())}")
            app_logger.info(f"标题页键值对数量: {len(ki.get('title_page', {}))}")
            app_logger.info(f"方案摘要键值对数量: {len(ki.get('protocol_summary', {}))}")
            app_logger.info(f"签字页键值对数量: {len(ki.get('approval_signature_page', {}))}")
            app_logger.info(f"目录条目数量: {len(ki.get('table_of_contents', []))}")

    except FileNotFoundError:
        app_logger.error(f"错误: 找不到文件 '{file_path}'。请确保文件路径正确，或修改 'file_path' 变量。")
    except Exception as e:
        app_logger.error(f"处理过程中发生错误: {e}", exc_info=True)