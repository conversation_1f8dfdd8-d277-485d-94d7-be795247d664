# utils/tfl_matcher_utils.py
import copy
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity


def match_tfls(in_text_tfls: list, tfls: list) -> list:
    """
    使用 TF-IDF 和余弦相似度，将 tfls 列表中的项分配给最匹配的 in_text_tfls 项。
    此函数是核心业务逻辑。流程：
    1. 准备语料库 (Corpus)
    2. 初始化并训练 TF-IDF 模型
    3. 拆分 TF-IDF 矩阵
    4. 计算余弦相似度

    Args:
        in_text_tfls: 'in-text TFLs' 的列表。
                      每个元素是一个字典，例如：{"sectionName": "...", "inTextName": "..."}
        tfls: 'TFLs' 的列表。
              每个元素是一个字典，例如：{"id": 1, "name": "...", "code": "..."}

    Returns:
        一个包含匹配结果的列表。

    Raises:
        ValueError: 如果输入列表为空。
    """
    if not in_text_tfls or not tfls:
        raise ValueError("输入列表 'in_text_tfls' 或 'tfls' 不能为空。")

    # 1. 准备语料库 (Corpus)
    in_text_names = [item.get("inTextName", "") for item in in_text_tfls]
    tlf_names = [item.get("name", "") for item in tfls]
    corpus = in_text_names + tlf_names

    # 2. 初始化并训练 TF-IDF 模型
    vectorizer = TfidfVectorizer(analyzer='char')
    tfidf_matrix = vectorizer.fit_transform(corpus)

    # 3. 拆分 TF-IDF 矩阵
    num_in_text = len(in_text_names)
    in_text_vectors = tfidf_matrix[:num_in_text]
    tlf_vectors = tfidf_matrix[num_in_text:]

    # 4. 计算余弦相似度
    cosine_similarities = cosine_similarity(tlf_vectors, in_text_vectors)

    # 5. 初始化结果结构
    result = copy.deepcopy(in_text_tfls)
    for item in result:
        item["tfls"] = []

    # 6. 遍历每个 TLF，根据最高相似度进行分配
    for i, tfl_item in enumerate(tfls):
        best_match_index = np.argmax(cosine_similarities[i])
        best_match_section_name = in_text_tfls[best_match_index]["sectionName"]

        for result_item in result:
            if result_item["sectionName"] == best_match_section_name:
                result_item["tfls"].append(tfl_item)
                break

    # 7. 直接返回处理好的列表
    return result