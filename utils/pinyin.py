from pypinyin import pinyin, Style


def chinese_name_to_english(chinese_name: str) -> str:
    """
    将中国人名的第一个汉字视为“姓”，后续汉字视为“名”，
    并分别转写为拼音，最终首字母大写，中间以空格分隔。

    示例：
      '傅智勇' -> 'Fu Zhi Yong'
      '王小明' -> 'Wang Xiao Ming'
    """
    # 使用 pypinyin 将姓名转换为拼音（不带声调）
    # pinyin(...) 返回形如 [['fu'], ['zhi'], ['yong']] 的二维数组
    pinyins = pinyin(chinese_name, style=Style.NORMAL, heteronym=False)

    # 压平为一维列表，比如 ['fu', 'zhi', 'yong']
    pinyin_list = [item[0] for item in pinyins]

    # 若姓名长度小于 2，或其它异常情况可自行处理
    if len(pinyin_list) == 0:
        return ""
    if len(pinyin_list) == 1:
        # 如果只有一个字，直接首字母大写返回
        return pinyin_list[0].capitalize()

    # 第一个字为姓
    surname = pinyin_list[0].capitalize()
    # 后面的字作为名
    given_name_parts = [x.capitalize() for x in pinyin_list[1:]]
    # 将名用空格拼接，如 "Zhi Yong"
    given_name = " ".join(given_name_parts)

    # 姓和名之间也用空格
    return f"{surname} {given_name}"


# ========== 测试 ==========
if __name__ == "__main__":
    names = ["傅智勇", "王小明", "李雷", "欧阳娜娜"]
    for cn_name in names:
        en_name = chinese_name_to_english(cn_name)
        print(f"{cn_name} -> {en_name}")
