"""
解析RTF中TFL数据，需要解析出TFL属性(caption, footnote, data, files, etc.)，并且将TFL中的数据、图片上传至OSS。

"""
import re
import os
import uuid
import json
import subprocess
from striprtf.striprtf import rtf_to_text
from servers.file_server import upload_and_return_file_info_with_key
from bs4 import BeautifulSoup

# rtf 文件存储路径
RTF_FILES_FOLDER = 'rtf'
if not os.path.exists(RTF_FILES_FOLDER):
    os.makedirs(RTF_FILES_FOLDER)


def has_images_in_rtf(rtf_file_path: str):
    """
    判断是否为包含图片的RTF文件

    :param rtf_file_path: RTF文件路径
    """
    with open(rtf_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    if re.search(r'\\pict[\s\S]*?(?:\\pngblip|\\jpegblip)', content, re.IGNORECASE):
        return True
    else:
        return False


def extract_tbl_text_from_rtf(rtf_file_path: str):
    """
    从RTF文件中提取表格并返回文本

    :param rtf_file_path: RTF文件路径
    """
    with open(rtf_file_path, 'r', encoding='utf-8') as file:
        rtf_content = file.read()
    plain_text = rtf_to_text(rtf_content)
    return plain_text


def extract_tbl_code(text: str):
    """
    提取出TFL标题文本中的表格编号

    :param text: TFL表格标题
    """
    match = re.search(r"\d+(?:\.\d+)+", text)
    return match.group() if match else ''


def extract_tbl_caption(text: str):
    """
    提取出TFL标题文本中的表格名称

    :param text: TFL表格标题
    """
    pattern = r'^[图表]\d+(?:\.\d+)*\s+([\s\S]*)$'

    match = re.match(pattern, text.strip())
    if match:
        return match.group(1).strip()
    else:
        return text.strip()


def extract_images_from_rtf(rtf_file_path: str):
    """
    解析图片类型的tfl文件中的数据，需要识别内容和内嵌的图片解析

    :param rtf_file_path: RTF 文件名（在 working_dir 目录下）
    :return: 提取后的html文件所在路径
    """
    # 解析rtf 文件目录
    rtf_dir = os.path.dirname(rtf_file_path)
    rtf_fname = os.path.basename(rtf_file_path)

    # 创建 output_html 子目录
    output_dir = os.path.join(rtf_dir, "output_html")
    os.makedirs(output_dir, exist_ok=True)

    # 构建输出 HTML 文件路径
    output_html_path = os.path.join(output_dir, "output.html")

    # 执行 unrtf --html 并将输出重定向到 output.html
    with open(output_html_path, 'w', encoding='utf-8') as html_file:
        result = subprocess.run(
            ['unrtf', '--html', '../' + rtf_fname],
            cwd=output_dir,  # 指定工作目录，即image文件的生成路径
            stdout=html_file,  # 将标准输出写入文件
            stderr=subprocess.PIPE,
            text=True
        )
    if result.returncode != 0:
        print("执行失败，错误信息：")
        print(result.stderr)
    else:
        print(f"HTML 文件已成功生成在：{output_html_path}")

    return output_html_path


def extract_images_header(html_path: str):
    """
    提取HTML中的纯文本内容，并自动解码HTML实体（如中文）
    :param html_path: HTML文件路径
    :return: 提取后的纯文本
    """
    with open(html_path, 'r', encoding='utf-8') as html_file:
        html_content = html_file.read()
        soup = BeautifulSoup(html_content, "html.parser")
        text = soup.body.get_text()

    return text.strip()


def save_tbl_cells_to_file(rtf_file_path: str, cells: list):
    """
    将tfl表格中的数据写入到json文件

    :param rtf_file_path: rtf文件路径
    :param  cells: 单元格数据，二维数组
    :return: json文件的路径
    """
    parent_dir = os.path.dirname(rtf_file_path)
    rtf_name = os.path.basename(rtf_file_path)
    data_dir = os.path.join(parent_dir, 'data')
    os.makedirs(data_dir, exist_ok=True)
    file_name = f'{rtf_name.replace(".", "-")}-{uuid.uuid4()}.json'
    json_file_path = os.path.join(data_dir, file_name)

    with open(json_file_path, 'w', encoding='utf-8') as f:
        json.dump({"data": cells}, f, ensure_ascii=False)
    return json_file_path

def upload_cells_to_oss(data_file_path: str, parent_oss_key: str):
    """
    上传tfl data到oss

    :param data_file_path: same with file_key
    :param parent_oss_key: parent_oss_key
    :return: The data parsed from RTF file. If TFL type is T, it should be an 2-d array. Otherwise, it should be an array of picture files.
    """
    file_name = os.path.basename(data_file_path)
    file_key = f'{parent_oss_key}/{file_name}-{uuid.uuid4()}'
    return upload_and_return_file_info_with_key(data_file_path, file_name, file_key)


def upload_images_to_oss(html_path: str, parent_oss_key: str):
    """
    将rtf中解析出的图片上传到oss

    :param html_path: 包含html文件以及图片文件的路径
    :param parent_oss_key: parent_oss_key
    :return: 图片文件在oss中的file info，数组类型
    """
    html_dir = os.path.dirname(html_path)
    jpg_files = []
    for root, dirs, files in os.walk(html_dir):
        for file in files:
            if file.lower().endswith('.jpg'):
                jpg_files.append(os.path.join(root, file))

    file_infos = []
    base_file_key = f'{parent_oss_key}-{uuid.uuid4()}'
    for image_file in jpg_files:
        file_name = os.path.basename(image_file)
        file_key = f'{base_file_key}/{file_name}'
        file_infos.append(upload_and_return_file_info_with_key(image_file, file_name, file_key))

    return file_infos


def extract_tfl_from_rtf(rtf_file_path: str, original_file_key: str):
    """
    从RTF文件中提取TFL数据，并将数据（表格、图片）上传到OSS，最后返回数据信息

    :param rtf_file_path: RTF文件路径
    :param original_file_key: RTF oss key
    :return: rtf中解析出的tfl_data list（TODO - 目前是单tfl，需要拓展支持多个tfl在同一个rtf中的解析方法 )
    """
    print(f'开始解析rtf - rtf_path:{rtf_file_path}, parent_oss_key:{original_file_key}')

    # 1. 判断rtf中是否包含图片
    has_image_flag = has_images_in_rtf(rtf_file_path)

    if has_image_flag:
        # 2.1 抽取rtf中的图片，并将文字部分生成html文件
        output_html_path = extract_images_from_rtf(rtf_file_path)

        # 2.2 解析标题等属性字段
        header = extract_images_header(output_html_path)
        caption = extract_tbl_caption(header)  ## 表标题
        code = extract_tbl_code(header)  ## 序号

        # 2.3 将文件上传到oss
        image_files = upload_images_to_oss(output_html_path, original_file_key)

        # 2.4 组装输出结果
        return {
            "type": "F",
            "header": header,
            "code": code,
            "caption": caption,
            "files": image_files
        }
    else:
        # 2.1 抽取tfl table
        text = extract_tbl_text_from_rtf(rtf_file_path)
        # print(text)

        # 2.2 解析各个属性字段
        parts = text.split('\n|\n')  # 表头和表体之间有空行
        header = parts[0]  # 表标题
        footnote = parts[-1]  ## 脚注

        caption = extract_tbl_caption(header)  # 表标题
        code = extract_tbl_code(header)  ## 序号
        cells = []
        csv_rows = []

        # 2.3 解析单元格数据
        body = '\n'.join(parts[1:])  ## 表体（剩余部分）- 按行分割
        lines = body.split('|\n')
        for line in lines:
            cells.append(line.split('|'))
            csv_rows.append(line.replace('|', ','))

        # 2.4 保存数据到json，并上传到oss
        data_file_path = save_tbl_cells_to_file(rtf_file_path, cells)
        oss_file_info = upload_cells_to_oss(data_file_path, original_file_key)

        # 2.5 组装输出结果
        tfl_data = {
            "type": 'T',
            "header": header,
            "code": code,
            "caption": caption,
            "footnote": footnote,
            "cells": cells,
            "rowCount": len(cells),
            "colCount": len(cells[0]) if len(cells) > 0 else 0,
            "files": [oss_file_info]
        }

        return tfl_data
