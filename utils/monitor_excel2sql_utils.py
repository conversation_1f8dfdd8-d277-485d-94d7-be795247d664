import pandas as pd
from sqlalchemy import create_engine, text, inspect
import pymysql
import re
import time
import io
from typing import Dict, Tu<PERSON>, List
from concurrent.futures import ThreadPoolExecutor, as_completed

from logger.logger import app_logger
from configurer.yy_nacos import config as nacos_config

try:
    from tqdm import tqdm

    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False


# 该函数无需修改
def get_mysql_config() -> Dict:
    """从Nacos获取固定的应用数据库配置"""
    db_conf = nacos_config.get('mysql-monitor-config')
    if not db_conf:
        app_logger.error("Nacos中未找到'mysql-monitor-config'配置")
        raise ValueError("数据库应用配置未找到")
    return db_conf


# 该函数无需修改
def drop_all_tables(mysql_config: Dict):
    """清空指定数据库中的所有表"""
    db_name = mysql_config['database']
    app_logger.warning(f"⚠️ 正在清空数据库 '{db_name}' 中的所有表...")

    engine = create_engine(
        f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}@{mysql_config['host']}/{db_name}?charset=utf8mb4",
        pool_pre_ping=True
    )

    try:
        with engine.connect() as conn:
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            if not tables:
                app_logger.info(f"数据库 '{db_name}' 为空，无需清空。")
                return

            conn.execute(text("SET FOREIGN_KEY_CHECKS = 0;"))
            for table in tables:
                conn.execute(text(f"DROP TABLE IF EXISTS `{table}`"))
                app_logger.info(f"🗑️ 已删除表: {table}")
            conn.execute(text("SET FOREIGN_KEY_CHECKS = 1;"))
            conn.commit()
        app_logger.info(f"✅ 数据库 '{db_name}' 清空完毕。")
    except Exception as e:
        app_logger.error(f"清空数据库 '{db_name}' 失败: {e}", exc_info=True)
        raise
    finally:
        engine.dispose()


# 以下清洁函数无需修改
def clean_column_name(name: str) -> str:
    """清理列名，使其符合SQL规范"""
    name = str(name).strip()
    name = re.sub(r'[\(\)（）\[\]【】\{\}]', '_', name)
    name = name.replace(' ', '_')
    name = re.sub(r'_+', '_', name)
    name = name.strip('_')
    name = re.sub(r'[^\w\u4e00-\u9fa5]', '', name)
    return name.lower()


def clean_dataframe_columns(df: pd.DataFrame) -> pd.DataFrame:
    """清理DataFrame的所有列名"""
    df.columns = [clean_column_name(col) for col in df.columns]
    return df


def clean_dataframe_data(df: pd.DataFrame) -> pd.DataFrame:
    """清理DataFrame中的数据值，去除字符串类型数据的前后空格"""
    df_cleaned = df.copy()

    # 遍历所有列，对字符串类型的数据进行strip操作
    for column in df_cleaned.columns:
        # 检查列的数据类型
        # object类型通常包含字符串，但也可能包含混合类型数据
        # 还需要检查string类型（pandas 1.0+新增的专用字符串类型）
        if df_cleaned[column].dtype == 'object' or str(df_cleaned[column].dtype).startswith('string'):
            # 对每个值进行检查和处理
            df_cleaned[column] = df_cleaned[column].apply(
                lambda x: x.strip() if isinstance(x, str) and pd.notna(x) else x
            )

    return df_cleaned


# process_single_sheet 函数 - 已添加数据清理功能
def process_single_sheet(args: Tuple[pd.DataFrame, str, Dict]) -> Dict:
    """处理单个DataFrame并将其写入指定数据库的表中"""
    df, sheet_name, mysql_config = args
    start_time = time.time()
    try:
        engine = create_engine(
            f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}@{mysql_config['host']}/{mysql_config['database']}?charset=utf8mb4",
            pool_pre_ping=True,
            pool_recycle=3600
        )
        if df.empty:
            return {
                'sheet_name': sheet_name, 'status': 'skipped', 'message': 'Sheet为空',
                'rows': 0, 'time': f"{time.time() - start_time:.2f}s"
            }
        # 清理列名
        df = clean_dataframe_columns(df)
        # 清理数据值，去除字符串数据的前后空格
        df = clean_dataframe_data(df)
        table_name = clean_column_name(sheet_name)
        with engine.connect() as conn:
            df.to_sql(
                name=table_name,
                con=conn,
                index=False,
                if_exists='append',
                method='multi',
                chunksize=1000
            )
            conn.commit()
        return {
            'sheet_name': sheet_name, 'status': 'success', 'table_name': table_name,
            'rows': len(df), 'time': f"{time.time() - start_time:.2f}s"
        }
    except Exception as e:
        app_logger.error(f"处理Sheet '{sheet_name}' 时出错: {e}", exc_info=True)
        return {
            'sheet_name': sheet_name, 'status': 'error', 'error': str(e),
            'time': f"{time.time() - start_time:.2f}s"
        }
    finally:
        if 'engine' in locals():
            engine.dispose()


# 1. 修改函数签名，接收 sheets_to_import 参数
def excel_to_mysql_parallel(excel_stream: io.BytesIO, mysql_config: Dict, sheets_to_import: List[str],
                            max_workers: int = 4) -> List[Dict]:
    """
    并行将内存中的Excel流中指定的Sheet导入到MySQL数据库中
    """
    start_time = time.time()

    try:
        # 1. 在所有操作之前，先清空目标数据库中的所有表 (逻辑保持不变)
        drop_all_tables(mysql_config)

        # 2. 根据指定的列表读取sheets
        app_logger.info(f"准备从Excel中读取指定的 {len(sheets_to_import)} 个Sheet: {sheets_to_import}")

        # 如果调用方传入一个空列表，则直接返回，不执行任何导入操作
        if not sheets_to_import:
            app_logger.warning("要导入的Sheet列表为空，跳过所有导入操作。")
            return []

        try:
            # pandas.read_excel 可以直接接收一个sheet名称的列表
            all_dfs = pd.read_excel(excel_stream, sheet_name=sheets_to_import)
        except ValueError as e:
            # 如果列表中的某个sheet不存在，pandas会抛出ValueError，我们将其重新抛出
            # 以便API层捕获并返回一个友好的400错误给用户。
            app_logger.error(f"读取指定的Sheet时出错: {e}")
            raise ValueError(f"无法找到指定的Sheet或文件格式错误，请检查Sheet名称是否正确: {e}") from e

        total_sheets_found = len(all_dfs)
        app_logger.info(
            f"在Excel文件中成功找到 {total_sheets_found} 个指定的Sheet，准备导入到数据库 '{mysql_config['database']}'。")

        # 3. 准备并行处理的参数 (逻辑保持不变)
        args_list = [(df, sheet_name, mysql_config) for sheet_name, df in all_dfs.items()]

        # 4. 并行执行 (逻辑保持不变)
        results = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_sheet = {executor.submit(process_single_sheet, args): args[1] for args in args_list}

            iterable = as_completed(future_to_sheet)
            if TQDM_AVAILABLE:
                iterable = tqdm(iterable, total=total_sheets_found, desc=f"导入到 '{mysql_config['database']}'",
                                unit="sheet")

            for future in iterable:
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    sheet_name = future_to_sheet[future]
                    app_logger.error(f"Sheet '{sheet_name}' 的未来任务执行异常: {e}", exc_info=True)
                    results.append({'sheet_name': sheet_name, 'status': 'error', 'error': str(e)})

        total_time = time.time() - start_time
        app_logger.info(f"数据库 '{mysql_config['database']}' 导入完成，总耗时: {total_time:.2f} 秒。")

        return results

    except Exception as e:
        # 捕获其他可能的异常
        app_logger.error(f"处理Excel流到数据库 '{mysql_config['database']}' 失败: {e}", exc_info=True)
        raise