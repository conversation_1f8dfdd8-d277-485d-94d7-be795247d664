import json
import os
import time
from typing import Dict

import nacos

from configurer.singleton import singleton
from config.nacos_config import nacos_config
from logger.logger import app_logger

init_configs = {
    "lamma_parse_config": "{}",
    "asr_config": "{}",
    "yiya-gateway-config": "{}",
    "mysql-monitor-config": "{}",
    "llm-vl-config": "{}"
}

config: Dict[str, Dict] = {}


def watcher(args):
    data_id = args['data_id']
    content = args['content']
    try:
        data = json.loads(content)
        config[data_id] = data
        app_logger.info(f"配置变更：{data_id} => {content}")
    except Exception as e:
        app_logger.error(f"配置解析异常发生错误:{data_id} => {content}, {e}")
        app_logger.error(f"详细信息: {e.args}")


@singleton
class Nacos:
    def __init__(self):
        self.group_id = ""
        self.client = None
        self._init_client()
        self._load_configs()

    def _init_client(self):
        """初始化 Nacos 客户端，带重试机制"""
        if not nacos_config.should_enable_nacos():
            app_logger.info("Nacos 已被禁用，跳过初始化")
            return

        max_retries = nacos_config.max_retries
        retry_delay = nacos_config.retry_delay

        for attempt in range(max_retries):
            try:
                app_logger.info(f"初始化 Nacos 客户端 (尝试 {attempt + 1}/{max_retries})")

                # 使用配置类获取客户端配置
                client_config = nacos_config.get_client_config()
                self.client = nacos.NacosClient(**client_config)

                # 测试连接（不使用 timeout 参数，因为 0.1.14 版本不支持）
                test_config = self.client.get_config("test", self.group_id)
                app_logger.info("Nacos 客户端初始化成功")
                return

            except Exception as e:
                app_logger.error(f"Nacos 客户端初始化失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    app_logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    app_logger.error("Nacos 客户端初始化最终失败")
                    raise

    def _load_configs(self):
        """加载配置，带错误处理"""
        if not self.client:
            app_logger.error("Nacos 客户端未初始化，无法加载配置")
            return

        for data_id, default_content in init_configs.items():
            try:
                app_logger.info(f"加载配置: {data_id}")

                # 尝试获取配置
                data = self._get_config_with_retry(data_id, self.group_id)
                if data:
                    config[data_id] = json.loads(data)
                    app_logger.info(f"成功加载配置: {data_id}")
                else:
                    # 尝试从默认组获取
                    data = self._get_config_with_retry(data_id, "DEFAULT_CONFIG")
                    if data:
                        config[data_id] = json.loads(data)
                        app_logger.info(f"从默认组加载配置: {data_id}")
                    else:
                        # 使用默认配置
                        config[data_id] = json.loads(default_content)
                        app_logger.warning(f"使用默认配置: {data_id}")

                # 添加配置监听器（可选，如果连接不稳定可以跳过）
                if nacos_config.enable_watcher:
                    try:
                        self.client.add_config_watcher(data_id, self.group_id, watcher)
                        app_logger.info(f"成功添加配置监听器: {data_id}")
                    except Exception as e:
                        app_logger.warning(f"添加配置监听器失败: {data_id}, 错误: {e}")
                else:
                    app_logger.info(f"配置监听器已禁用，跳过: {data_id}")

            except Exception as e:
                app_logger.error(f"加载配置失败: {data_id}, 错误: {e}")
                # 使用默认配置
                config[data_id] = json.loads(default_content)

    def _get_config_with_retry(self, data_id, group_id, max_retries=None):
        """带重试的配置获取"""
        if max_retries is None:
            max_retries = nacos_config.config_retry

        for attempt in range(max_retries):
            try:
                # 不使用 timeout 参数，因为 nacos-sdk-python==0.1.14 不支持
                return self.client.get_config(data_id, group_id)
            except Exception as e:
                app_logger.warning(f"获取配置失败 {data_id} (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(1)
        return None


if __name__ == '__main__':
    mse = Nacos()
    d = config.get('lamma_parse_config')
    asr = config.get('asr_config')
    dd = config.get('yiya-gateway-config')
    ds = config.get('mysql-monitor-config')
    vl = config.get('llm-vl-config')
    print(d)
    print(asr)
    print(dd)
    print(vl)
    print(ds)
    time.sleep(1000)
