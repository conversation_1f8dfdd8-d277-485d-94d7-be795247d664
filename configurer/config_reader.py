import sys
import os

# 添加项目的父目录到sys.path中，以便Python能找到子目录中的包
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, project_root)

from configurer.yy_nacos import config
from exception.exception import BaseError


def get_lamma_index_config():
    d = config['lamma_parse_config']
    if not d:
        raise BaseError(500, "lamma_parse_config not configured.")
    return d

def get_asr_config():
    d = config['asr_config']
    if not d:
        raise BaseError(500, "asr_config not configured.")
    return d


def get_gate_config():
    d = config['yiya-gateway-config']
    if not d:
        raise BaseError(500, "yiya-gateway-config not configured.")
    return d

def get_vl_config():
    d = config['llm-vl-config']
    if not d:
        raise BaseError(500, "llm-vl-config not configured.")
    return d