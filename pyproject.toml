[project]
name = "yiya-ai-bot"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11,<3.12"
dependencies = [
    # --- 跨平台核心依赖 ---
    "loguru==0.7.2",
    "docxtpl==0.19.1",
    "fastapi[standard]==0.115.5",
    "uvicorn==0.32.1",
    "nacos-sdk-python==0.1.14",
    "openai==1.55.0",
    "openpyxl==3.1.5",
    "pyreadstat==1.2.9",
    "python-docx==1.1.2",
    "python-dotenv>=1.1.0",
    "pyyaml==6.0.2",
    "requests==2.32.3",
    "flask==3.1.0", # 如果确实需要 Flask
    "llama-parse==0.5.15",
    "llama-index==0.12.1",
    "oss2==2.19.1",
    "pandas==2.2.3",
    "funasr==1.2.0",
    "dashscope==1.20.14",
    "pydub==0.25.1",
    # --- 仅在 Linux 上安装的生产环境服务器 ---
    "gunicorn==22.0.0; sys_platform == 'linux'",
    # --- 仅在 Linux 上安装的 Paddle 包 ---
    "paddlehub==2.4.0; sys_platform == 'linux'",
    "paddleocr==2.9.1; sys_platform == 'linux'",
    "markitdown>=0.1.2",
    "opencv-python>=*********",
    "frontend>=0.0.3",
    "pymupdf>=1.26.3",
    "patool>=1.12", # <--- 这里是修改的地方
    "xlsxwriter>=3.2.5",
    "orjson>=3.10.18",
    "pymysql>=1.1.1",
    "hanlp>=2.1.1",
    "celery>=5.5.3",
    "redis>=6.2.0",
    "pydantic-settings>=2.10.1",
    "circus>=0.19.0",
    "speedtest-cli>=2.1.3",
]

[dependency-groups]
dev = [
    "ruff>=0.11.11",
]
