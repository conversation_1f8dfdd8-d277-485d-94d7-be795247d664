# api/protocol_api.py

import os
import time
import io
import random
import orjson
import gc
from fastapi import APIRouter
from pydantic import BaseModel, Field
from models.result import make_fail, make_success
from models.protocol_vo import ProtocolFileRequest
from utils.oss_utils import download_file_to_stream, write_large_file_from_stream, write_large_file_from_stream_parallel
from utils.protocol_utils import extract_structured_protocol
from logger.logger import app_logger
from oss2.exceptions import NoSuchKey

router = APIRouter(prefix="/protocols", tags=["Protocols"])


@router.post('/extract-by-docx')
async def extract_structure_from_docx(request: ProtocolFileRequest):
    """
    从OSS上的.docx方案文件中解析出层级化JSON结构。
    - sections 部分（内容主体）以 { "sections": [...] } 格式上传回OSS。
    - key_information 部分（元数据）在API响应中直接返回。
    包含详细的性能监控：下载、处理、序列化、上传各阶段耗时统计
    """
    global_start_time = time.time()
    stage_times = {}

    file_key = request.protocol_file.file_key
    extract_keys_only = getattr(request, 'extract_keys_only', False)

    try:
        # 阶段1: 下载文件
        stage_start = time.time()
        app_logger.info(f"开始从OSS下载文件: {file_key}")
        oss_stream = download_file_to_stream(file_key)
        download_time = time.time() - stage_start
        stage_times['download'] = download_time
        app_logger.info(f"文件下载完成，耗时: {download_time:.2f}s")

        # 阶段2: 处理文件（解析docx）
        stage_start = time.time()
        with io.BytesIO(oss_stream.read()) as docx_stream:
            if hasattr(oss_stream, 'close'):
                oss_stream.close()
            protocol_data = extract_structured_protocol(docx_stream, extract_keys_only=extract_keys_only)
        process_time = time.time() - stage_start
        stage_times['process'] = process_time
        app_logger.info(f"文件处理完成，耗时: {process_time:.2f}s")

        # 将解析结果分离：key_information 用于API直接返回，sections 用于上传OSS
        key_information = protocol_data.get("key_information", {})
        sections_to_upload = protocol_data.get("sections", [])

        # 阶段3: JSON序列化
        stage_start = time.time()

        ### 修正点 Start ###
        # 创建一个只包含 sections 键的新字典，以确保上传的JSON文件保留根键。
        data_for_oss = {"sections": sections_to_upload}
        json_bytes = orjson.dumps(data_for_oss)
        ### 修正点 End ###

        serialize_time = time.time() - stage_start
        stage_times['serialize'] = serialize_time

        json_size_mb = len(json_bytes) / (1024 * 1024)
        app_logger.info(f"JSON序列化完成，生成 {json_size_mb:.2f}MB 数据，耗时: {serialize_time:.2f}s")

        # 内存清理
        del protocol_data
        del sections_to_upload
        del data_for_oss  # 清理新创建的字典
        gc.collect()

        # 阶段4: 上传OSS
        stage_start = time.time()
        timestamp_ms = int(time.time() * 1000)
        random_suffix = random.randint(100000, 999999)
        oss_json_key = f"protocols/oss-{timestamp_ms}-{random_suffix}.json"

        with io.BytesIO(json_bytes) as json_stream:
            if json_size_mb > 50:
                write_large_file_from_stream_parallel(
                    json_stream,
                    oss_json_key,
                    part_size=20 * 1024 * 1024,
                    max_workers=2
                )
            else:
                write_large_file_from_stream(json_stream, oss_json_key)

        upload_time = time.time() - stage_start
        stage_times['upload'] = upload_time
        app_logger.info(f"上传OSS完成，耗时: {upload_time:.2f}s")

        # 清理内存
        del json_bytes
        gc.collect()

        # 计算总耗时和性能统计
        total_time = time.time() - global_start_time
        stage_times['total'] = total_time

        # 性能报告（保留2位小数）
        performance_report = {
            'stage_times': {k: round(v, 2) for k, v in stage_times.items()},
            'file_size_mb': round(json_size_mb, 2),
            'throughput': {
                'download': round(json_size_mb / download_time,
                                  2) if 'download' in stage_times and download_time > 0 else 0,
                'upload': round(json_size_mb / upload_time, 2) if 'upload' in stage_times and upload_time > 0 else 0
            }
        }

        app_logger.info(f"性能统计 - 总耗时: {round(total_time, 2)}s, "
                        f"下载: {round(stage_times.get('download', 0), 2)}s, "
                        f"处理: {round(stage_times.get('process', 0), 2)}s, "
                        f"序列化: {round(stage_times.get('serialize', 0), 2)}s, "
                        f"上传: {round(stage_times.get('upload', 0), 2)}s")

        # 构建新的响应体，包含 oss_key (指向sections) 和 key_information 对象
        response_data = {
            "oss_key": oss_json_key,
            "key_information": key_information
        }

        return make_success(response_data, int(total_time * 1000))

    except NoSuchKey:
        app_logger.warning(f"OSS文件未找到 (NoSuchKey)，请求的Key为: '{file_key}'")
        return make_fail(404, f"File not found on OSS. Key: {file_key}")

    except Exception as e:
        # 使用 logger.exception 可以自动记录完整的堆栈信息，非常适合调试
        app_logger.exception(f"处理文件时发生未知错误, Key: '{file_key}'. 错误: {e}")
        gc.collect()
        # 将具体的错误信息返回给客户端，或者一个通用的错误ID
        return make_fail(500, f"Internal Server Error: {e}")