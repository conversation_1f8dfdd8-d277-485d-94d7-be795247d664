import json
import os
from io import BytesIO
from time import sleep

import numpy as np
import requests
from PIL import Image
from fastapi import APIRouter,FastAPI, File, UploadFile

from api.cache import get_cache, set_cache
from models.file_info import FileInfo
from models.ocr_vo import OcrRequest, OcrResponse, RenameZip
from servers import file_server, paddle_ocr
import logging
from datetime import datetime
import time
import uuid
import pandas as pd




router = APIRouter(include_in_schema=False)
logger = logging.getLogger(name=__name__)


current_path = "/root/yiya-ai-bot/"


def udoc(file_key):
    paddle_ocr.uc_pic(file_key=file_key, file_name=file_key)



def ocr_rotate(file_key):
    rotate_angle = paddle_ocr.rotate_pic(file_key=file_key, file_name=file_key)
    if rotate_angle != "0":
        source_url = file_server.get_file_url(file_key)
        response = requests.get(source_url)
        img = Image.open(BytesIO(response.content))
        original_mode = img.mode
        bg_color = (255, 255, 255)  # 白色背景
        # 如果是透明图像（如RGBA），需要指定背景色
        if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
            # 创建一个白色背景的新图像，并将原始图像粘贴上去
            img = img.convert('RGBA')
            alpha = img.split()[3]
            bg = Image.new("RGBA", img.size, bg_color + (255,))
            bg.paste(img, mask=alpha)
            img = bg
        rotated_img = img.rotate(int(rotate_angle), expand=True, fillcolor=bg_color[:3])
        # 确保旋转后的图像色彩模式与原始图像相同
        if original_mode != rotated_img.mode:
            rotated_img = rotated_img.convert(original_mode)
        img_byte_arr = BytesIO()
        rotated_img_rgb = rotated_img.convert('RGB')
        rotated_img_rgb.save(img_byte_arr, "JPEG")  # 或者根据需要选择其他格式，如 'JPEG'
        img_byte_arr = img_byte_arr.getvalue()
        file_server.put_pic(img_byte_arr,file_key)
        return file_key



@router.post('/excel_parse')
async def excel_parse(file: UploadFile = File(...)):

    content = await file.read()

    # 读取 Excel 文件
    df = pd.read_excel(content)

    # 显示前 5 行数据
    print(df.head())

    new_files = []
    # 遍历每一行
    for index, row in df.iterrows():
       # print(row['文件名列表'], index+1)
        data = {'file_name': row['文件名称'], 'index':  row['序号']}
        new_files.append(data)
    return new_files


@router.post('/unzip_file')
async def unzip_file(file: UploadFile = File(...)):

    filename = file.filename
    content = await file.read()
    current_time = str(uuid.uuid1())
    filename_extension = os.path.basename(filename)
    filename_name, filename_ = os.path.splitext(filename_extension)
    package_name = filename_name + current_time
    file_path = filename_name + current_time + filename_

    # 保存文件到服务器
    if not os.path.exists(current_path+"uploaded/"+package_name):
        os.makedirs(current_path+"uploaded/"+package_name)
    if not os.path.exists(current_path+"unzip_dir/"+package_name):
        os.makedirs(current_path+"unzip_dir/"+package_name)

    # set_cache(filename_name+"_run",True)
    with open(f"{current_path}uploaded/{file_path}", "wb") as f:
        f.write(content)
    if filename_ == ".zip":
        paddle_ocr.unzip_file(f"{current_path}uploaded/{file_path}",
                      f"{current_path}unzip_dir/{package_name}")
    else:
        paddle_ocr.extract_rar_or_other_formats(f"{current_path}uploaded/{file_path}",
                                                f"{current_path}unzip_dir/{package_name}")
    files = paddle_ocr.list_files_in_current_directory(f"{current_path}unzip_dir/{package_name}/")
    new_files = []
    for f in files:
        file_extension = os.path.basename(f)
        f_name, f_ = os.path.splitext(file_extension)
        if f_ and  '__MACOSX' not in f and not f_name.startswith('._'):
            data = {'file_url': f, 'file_name': file_extension,'package':package_name}
            new_files.append(data)
    return new_files




@router.post('/rotate_pic')
def rotate_pic(ocr_request: OcrRequest):
    logger.info(f"rotate_pic,{ocr_request.file_name},{ocr_request.file_key}")
    file_key = ocr_request.file_key
    ocr_rotate(file_key)




@router.post('/rename_zip')
def rotate_pic(rename: RenameZip):
    logger.info(f"rename_zip,{rename.rename_json}")
    new_files = []
    path= ""
    package = ""
    new_files_some_name = []
    data = []
    for i in rename.rename_json:
        rename_dict = json.loads(i)
        package = rename_dict["package"]
        path = current_path+"unzip_dir/"+rename_dict["package"]
        file_path = os.path.dirname(rename_dict["file_url"])
        file_extension = os.path.basename(rename_dict["file_url"])
        f_name, f_ = os.path.splitext(file_extension)
        # 来统计每个字符串出现的次数

        count = new_files_some_name.count(rename_dict["new_name"])
        new_files_some_name.append(rename_dict["new_name"])
        new_name = rename_dict["new_name"]
        if count > 0:
            new_name = new_name + str(count)
        if '__MACOSX' not in file_path and not file_path.startswith('._'):
            paddle_ocr.rename_file(rename_dict["file_url"],file_path+"/"+new_name+f_)
            new_files.append(file_path+"/"+new_name+f_)
            filename = rename_dict["file_url"][path.rindex('/') + 1:]
            data.append({"file_name":filename,"new_name":new_name+f_})
    # 使用json.dump()将数据写入文件
    with open(path+'record.json', 'w') as f:
        json.dump(data, f)
    new_files.append(path+'record.json')
    paddle_ocr.create_zip(path+"_out.zip",new_files)
    file_key = file_server.upload_zip_local_file(path+"_out.zip",package+".zip")
    return file_server.get_file_url(file_key)






@router.post('/unzip_rename')
async def unzip_rename(file: UploadFile = File(...)):

    filename = file.filename
    content = await file.read()
    current_time = str(uuid.uuid1())
    filename_extension = os.path.basename(filename)
    filename_name, filename_ = os.path.splitext(filename_extension)
    package_name = filename_name + current_time
    file_path = filename_name + current_time + filename_
    #is_run = get_cache(filename_name+"_run")
    #result = get_cache(filename_name)
   # logger.warning(f"{filename_name},is_run: {is_run},result:{result}")
    # if result:
    #     return  {"data": result, "success": True}
    # if is_run:
    #     while get_cache(filename_name) is  None:
    #         logger.warning(filename_name+",任务正在运行!")
    #         sleep(5)
    #     return  {"data": get_cache(filename_name), "success": True}
    # 保存文件到服务器
    if not os.path.exists(current_path+"uploaded/"+package_name):
        os.makedirs(current_path+"uploaded/"+package_name)
    if not os.path.exists(current_path+"unzip_dir/"+package_name):
        os.makedirs(current_path+"unzip_dir/"+package_name)

   # set_cache(filename_name+"_run",True)
    with open(f"{current_path}uploaded/{file_path}", "wb") as f:
        f.write(content)
    if filename_ == ".pdf":
        paddle_ocr.split_by_tag(filename_name,f"{current_path}uploaded/{file_path}",f"{current_path}unzip_dir/{package_name}")
    elif filename_ == ".zip":
        paddle_ocr.unzip_file(f"{current_path}uploaded/{file_path}",
                          f"{current_path}unzip_dir/{package_name}")
    else:
        paddle_ocr.extract_rar_or_other_formats(f"{current_path}uploaded/{file_path}",
                                                f"{current_path}unzip_dir/{package_name}")
    files = paddle_ocr.list_files_in_current_directory(f"{current_path}unzip_dir/{package_name}/")
    new_files = []
    end = False
    for f in files:
        file_extension = os.path.basename(f)
        f_name, f_ = os.path.splitext(file_extension)
        if f_ and  '__MACOSX' not in f and not f_name.startswith('._'):
            res = paddle_ocr.pdf_first_page_to_image(f,f_name+".jpg",current_path,package_name)
            data = {'file_info': res, 'end': end,"file_url": f, 'package': package_name}
            logger.warning(f"file_url:{f}")
            new_files.append(data)
    #set_cache(filename_name,new_files)
    #logger.info("set_cache success")
    return {"data": new_files, "success": True}


@router.post('/udoc_pic')
def udoc_pic(ocr_request: OcrRequest):
    logger.info(f"udoc_pic,{ocr_request.file_name},{ocr_request.file_key}")
    file_key = ocr_request.file_key
    udoc(file_key)



@router.post('/ocr_mask_sensitive')
def mask_sensitive(ocr_request: OcrRequest):
    logger.info(f"ocr_mask_sensitive,{ocr_request.type},{ocr_request.file_name},{ocr_request.file_key},{ocr_request.cut_point}")
    file_name = ocr_request.file_name
    cut_point = ocr_request.cut_point
    file_key = ocr_request.file_key
    type = ocr_request.type

    np_image = paddle_ocr.analysis(file_key=file_key, source_name=file_name, cut_point=cut_point, type=type)
    file_info = None
    if np_image is None:
        file_info = FileInfo(
            file_name=file_name,
            file_key=file_key,
            file_url=file_server.get_file_url(file_name)
        )
    else:
        # 获取文件名（不含路径）和文件扩展名
        file_base_name, file_extension = os.path.splitext(os.path.basename(file_name))

        current_time = datetime.now().strftime("%Y%m%d%H%M%S")
        # 构造新的文件名
        file_name = f"{file_base_name}_{current_time}{file_extension}"

        start_time = time.time()
        file_server.put_object(np_image,file_name)
        end_time = time.time()  # 记录结束时间
        logger.warning(f"read image took {end_time - start_time:.2f} seconds")
        # 获取文件名（不含路径）和文件扩展名
        file_base_name, file_extension = os.path.splitext(os.path.basename(file_name))

        current_time = datetime.now().strftime("%Y%m%d%H%M%S")
        # 构造新的文件名
        file_name = f"{file_base_name}_{current_time}{file_extension}"
        file_server.put_object(np_image,file_name)

        start_time = time.time()

        end_time = time.time()  # 记录结束时间
        logger.warning(f"{file_key}  read image took {end_time - start_time:.2f} seconds")

        file_info = FileInfo(
            file_name=file_name,
            file_key=file_name,
            file_url=file_server.get_file_url(file_name)
        )
    return build_success_result(file_info)

def pil_to_numpy(pil_image, mode='RGB'):
    """
    将PIL图像转换为NumPy数组，同时保证颜色模式一致。

    :param pil_image: PIL.Image对象
    :param mode: 转换后的图像模式 ('RGB' 或 'BGR')
    :return: NumPy数组
    """
    # 确保图像为RGB模式（丢弃Alpha通道）
    if pil_image.mode in ('RGBA', 'LA') or (pil_image.mode == 'P' and 'transparency' in pil_image.info):
        pil_image = pil_image.convert('RGB')
    # 将PIL图像转换为NumPy数组
    np_image = np.array(pil_image)

    return np_image

def build_success_result(result):
    res = OcrResponse(
        code=200,
        message="success",
        result=result,
        success=True,
    )
    # return json.dumps(res.__dict__)
    return res


def build_error_result(err):
    res = OcrResponse(
        code=500,
        message=str(err),
        result=None,
        success=False,
    )
    # return json.dumps(res.__dict__)
    return res
