# api/medical_report_api.py

import os
import time
import json
import random
from fastapi import APIRouter
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from models.result import make_fail, make_success
from utils.oss_utils import write_file,get_file_url
from utils.monitor_report_utils import fill_medical_report_excel
from logger.logger import app_logger

router = APIRouter(prefix="/medical-reports", tags=["Medical Reports"])

# 用于存储临时文件的本地文件夹
TEMP_FILES_FOLDER = 'temp_medical_reports'
if not os.path.exists(TEMP_FILES_FOLDER):
    os.makedirs(TEMP_FILES_FOLDER)

# 本地模板文件路径
TEMPLATE_FILE_PATH = 'template/monitor_report.xlsx'


class MedicalReportRequest(BaseModel):
    """医学数据审查报告请求模型"""
    report_data: Dict[str, List[Dict[str, Any]]] = Field(...,
                                                         description="医学报告数据，包含rule1, rule2, rule3等规则数据")

    class Config:
        json_schema_extra = {
            "example": {
                "report_data": {
                    "rule1_no_matching_data": [
                        {
                            "受试者编号": "001",
                            "不良事件名称_aeterm": "头痛",
                            "开始日期_aestdat": "2023-01-15"
                        }
                    ],
                    "rule2_duplicate_records": [
                        {
                            "受试者编号": "002",
                            "不良事件名称_aeterm": "恶心"
                        }
                    ],
                    "rule3_invalid_dates": [
                        {
                            "受试者编号": "003",
                            "不良事件名称_aeterm": "腹泻",
                            "开始日期_aestdat": "2023-01-20",
                            "d1用药日期": "2023-01-10",
                            "研究结束日期": "2023-02-10"
                        }
                    ]
                }
            }
        }


@router.post('/generate-report')
async def generate_medical_report(request: MedicalReportRequest):
    """
    生成医学数据审查报告Excel文件，并上传到OSS
    """
    start_time = time.time()
    report_data = request.report_data

    try:
        # 1. 检查本地模板文件是否存在
        if not os.path.exists(TEMPLATE_FILE_PATH):
            app_logger.error(f"本地模板文件不存在: {TEMPLATE_FILE_PATH}")
            return make_fail(404, f"Template file not found: {TEMPLATE_FILE_PATH}")

        # 2. 生成唯一的输出文件名
        timestamp_ms = int(time.time() * 1000)
        random_suffix = random.randint(1000, 9999)

        output_file_name = f"medical_report_{timestamp_ms}_{random_suffix}.xlsx"
        local_output_path = os.path.join(TEMP_FILES_FOLDER, output_file_name)

        # 3. 处理数据并填充Excel
        app_logger.info("开始处理数据并填充Excel模板")
        result_info = fill_medical_report_excel(
            report_data,
            TEMPLATE_FILE_PATH,
            local_output_path
        )
        app_logger.info(f"Excel填充完成，共处理 {result_info['total_records']} 条记录")

        # 4. 上传结果文件到OSS
        # 生成OSS Key，格式: medical-reports/report-{timestamp_ms}-{random_suffix}.xlsx
        random_suffix_6 = random.randint(100000, 999999)  # 6位随机数
        oss_result_key = f"medical-reports/report-{timestamp_ms}-{random_suffix_6}.xlsx"

        app_logger.info(f"正在上传结果文件到OSS, Key: {oss_result_key}")
        write_file(local_output_path, oss_result_key)
        app_logger.info("结果文件上传OSS成功。")

        # 5. 获取可下载链接
        oss_download_url = get_file_url(oss_result_key)

        # 6. 删除本地文件
        os.remove(local_output_path)
        app_logger.info(f"已删除本地结果文件: {local_output_path}")

        # 7. 准备返回数据
        response_data = {
            "oss_key": oss_result_key,
            "oss_download_url": oss_download_url,  # ⭐ 新增字段
            "total_records": result_info['total_records'],
            "rule_statistics": result_info['rule_statistics'],
            "generated_at": result_info['generated_at']
        }

        duration = time.time() - start_time
        time_cost_ms = int(duration * 1000)

        app_logger.info(f"医学报告生成并上传OSS成功, 耗时: {duration:.2f}s ({time_cost_ms}ms)")
        return make_success(response_data, time_cost_ms)

    except ValueError as e:
        app_logger.warning(f"数据处理错误: {str(e)}")
        return make_fail(400, f"Data processing error: {str(e)}")

    except Exception as e:
        app_logger.error(f"生成医学报告时发生未知错误. 错误类型: {type(e).__name__}", exc_info=True)
        return make_fail(500, "Internal Server Error")
