import logging
import os
import time
from datetime import datetime

from fastapi import APIRouter,Request
from markitdown import MarkItDown

from config.env_config import UPLOAD_FOLDER
from models.result import make_success, make_fail
from utils.file_utils import unzip_file
from utils.request_utils import get_filename_from_request

md = MarkItDown()
router = APIRouter(include_in_schema=False)

def load_file_markitdown(file_path):
    try:
        return md.convert(file_path).text_content
    except Exception as e:
        logging.error("MarkItDown Failed: ", e)
        return None


def load_file(file_path):
    """根据文件扩展名选择适当的加载器并读取文件内容"""
    return load_file_markitdown(file_path)


@router.post('/load_file')
async def load(request: Request):
    start = time.time()
    now = datetime.now()
    formatted_time = now.strftime("%Y%m%d%H%M%S")
    files = []
    try:
        filename = get_filename_from_request(request)

        if not filename:
            raise ValueError('文件名不能为空')

        current_file_path = os.path.dirname(__file__)
        file_path = os.path.join(current_file_path, '..', UPLOAD_FOLDER, filename)

        content = await request.body()

        # 保存上传的文件
        with open(file_path, "wb") as f:
            f.write(content)

        # 若为 zip 文件，则解压文件
        if filename.endswith('.zip'):
            extracted_files = unzip_file(file_path, UPLOAD_FOLDER)
            files.extend(extracted_files)
        else:
            files.append(file_path)

        # 加载文件内容
        res = [load_file(file) for file in files if file.find('__MACOSX') == -1]
        res = [item for item in res if item is not None]

        # 清理临时文件
        os.remove(file_path)

        # Step 4: 返回ASR结果
        end = time.time()
        return make_success(res, int(end - start))
    except Exception as e:
        logging.error("File Loader Failed: ", e)
        return make_fail(500, str(e))


if __name__ == '__main__':
    result = load_file('../test/bingli.md')
    # result = load_file_markitdown('../test/吴裕璇 （北京+CRA）.pdf')
    # result = load_file_markitdown('../test/Contract CRA-北京-王小月-TG-Candidate Report-20240828.pdf')
    print(result)
