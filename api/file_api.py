import asyncio
import logging
import os
import time
import uuid
from datetime import datetime

from fastapi import APIRouter, Request

from config.env_config import UPLOAD_FOLDER
from models.file_info import FileInfo
from models.result import make_success, make_fail
from servers.file_server import build_oss_key
from utils.file_utils import detect_file_type
from utils.oss_utils import write_file, get_file_url, download_file
from utils.request_utils import get_filename_from_request
from servers.file_server import upload_and_return_file_info

router = APIRouter(include_in_schema=False)
logger = logging.getLogger(name=__name__)


@router.post('/file/upload')
async def upload(request: Request):
    start = time.time()

    try:
        # --- 第 1 步：获取原始文件名，这个文件名可能会很长 ---
        # 这个文件名将用于OSS存储和最终的API响应。
        original_filename = get_filename_from_request(request)
        if not original_filename:
            # 如果请求中没有提供文件名，则生成一个基于时间的默认名
            now = datetime.now()
            formatted_time = now.strftime("%Y%m%d%H%M%S")
            original_filename = f"{formatted_time}.bin"  # 使用 .bin 作为通用扩展名

        # --- 第 2 步：为本地临时文件创建一个简短、唯一的文件名 ---
        # 我们只保留原始文件的扩展名，文件名主体用UUID替换，以确保简短且不重复。
        # os.path.splitext可以安全地分离文件名和扩展名
        _, extension = os.path.splitext(original_filename)
        # 使用 uuid.uuid4() 更常见且推荐，因为它不基于主机ID和时间
        temp_local_filename = f"{uuid.uuid4()}{extension}"

        # --- 第 3 步：构建本地临时文件的完整路径 ---
        current_file_path = os.path.dirname(__file__)
        upload_dir = os.path.join(current_file_path, '..', UPLOAD_FOLDER)
        # 确保上传目录存在，如果不存在则创建
        os.makedirs(upload_dir, exist_ok=True)

        # 使用简短的临时文件名构建路径，避免路径过长错误
        temp_file_path = os.path.join(upload_dir, temp_local_filename)

        # --- 第 4 步：将上传内容保存到本地临时文件 ---
        content = await request.body()
        with open(temp_file_path, "wb") as f:
            f.write(content)

        # --- 第 5 步：使用原始文件名构建OSS的Key ---
        key = build_oss_key(original_filename)

        # --- 第 6 步：将本地临时文件上传到OSS ---
        # 上传函数的目标是OSS的key，源是本地的temp_file_path
        write_file(temp_file_path, key)

        # --- 第 7 步：准备返回结果 ---
        # 返回给客户端的信息中，依然使用用户熟悉的原始文件名
        file_url = get_file_url(key)
        res = FileInfo(file_key=key, file_name=original_filename, file_url=file_url)

        # --- 第 8 步：清理本地临时文件 ---
        os.remove(temp_file_path)

        # --- 第 9 步：返回成功响应 ---
        end = time.time()
        return make_success(res, int(end - start))

    except Exception as e:
        # 建议使用 logging.exception 记录完整的错误堆栈，方便排查问题
        logging.exception("File Upload Failed:")
        return make_fail(500, str(e))


@router.post('/file/pdf_to_images')
async def convert_pdf_to_images(request: Request):
    # 1. 下载文件或直接读取
    from utils.request_utils import upload

    start = time.time()

    try:
        file_info = asyncio.run(upload(request))

        if not file_info:
            return make_fail(400, "file upload failed.")

        # 2. 判断文件类型
        file_type = detect_file_type(file_info.file_path)

        file_info_list = []

        # 3. 根据类型处理
        if file_type == 'image':
            file_info_list.append(file_info)
        elif file_type == 'pdf':
            from utils.pdf_util import pdf_to_images
            now = datetime.now()
            formatted_time = now.strftime("%Y%m%d%H%M%S")
            output_folder = f"{UPLOAD_FOLDER}/{formatted_time}/{file_info.file_name}"

            if not os.path.exists(output_folder):
                os.makedirs(output_folder)

            images = pdf_to_images(file_info.file_path, output_folder, dpi=200)

            for image in images:
                file_info = upload_and_return_file_info(image.file_path, image.file_name)
                file_info.file_path = None
                file_info_list.append(file_info)
        else:
            return make_fail(400, "Unsupported file type!")

        # 4. 返回
        return make_success(file_info_list, time_cost=int(time.time() - start) * 1000)
    except Exception as e:
        logger.error(f"Error processing file: ", e)
        return make_fail(500, "Internal Server Error")


TMP_FILES_FOLDER = 'uploads'
if not os.path.exists(TMP_FILES_FOLDER):
    os.makedirs(TMP_FILES_FOLDER)
@router.post('/file/pdf_to_images_by_ossinfo')
async def convert_pdf_to_images_by_ossinfo(file_info: FileInfo):
    # 1. 下载文件
    file_path = os.path.join(TMP_FILES_FOLDER, file_info.file_name)
    download_file(file_info.file_key, file_path)
    file_info.file_path = file_path
    file_info.file_url = get_file_url(file_info.file_key)

    start = time.time()

    try:
        # file_info = asyncio.run(upload(request))

        if not file_info:
            return make_fail(400, "file not found")

        # 2. 判断文件类型
        file_type = detect_file_type(file_info.file_path)

        file_info_list = []

        # 3. 根据类型处理
        if file_type == 'image':
            file_info_list.append(file_info)
        elif file_type == 'pdf':
            from utils.pdf_util import pdf_to_images
            now = datetime.now()
            formatted_time = now.strftime("%Y%m%d%H%M%S")
            output_folder = f"{UPLOAD_FOLDER}/{formatted_time}/{file_info.file_name}"

            if not os.path.exists(output_folder):
                os.makedirs(output_folder)

            images = pdf_to_images(file_info.file_path, output_folder, dpi=200)

            for image in images:
                file_info = upload_and_return_file_info(image.file_path, image.file_name)
                file_info.file_path = None
                file_info_list.append(file_info)
        else:
            return make_fail(400, "Unsupported file type!")

        # 4. 返回
        return make_success(file_info_list, time_cost=int(time.time() - start) * 1000)
    except Exception as e:
        logger.error(f"Error processing file: ", e)
        return make_fail(500, "Internal Server Error")
