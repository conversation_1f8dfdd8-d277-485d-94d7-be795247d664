# monitor_excel2sql_api.py

import time

from celery.result import AsyncResult
from fastapi import APIRouter, status
from pydantic import BaseModel, Field, HttpUrl

# 导入 Celery App 实例和任务
from celery_task.celery import celery_app
from celery_task.excel_import_task import import_excel_task
from logger.logger import app_logger

# 导入 make_success/make_fail 和 logger
# 导入需要 time_cost 参数的 make_success 函数
from models.result import make_fail, make_success

router = APIRouter(prefix="/importer", tags=["Excel Importer"])




class ExcelImportRequest(BaseModel):
    excel_url: HttpUrl = Field(..., description="需要导入的Excel文件的公开可访问URL")
    sheets_to_import: list[str] = Field(..., description="需要导入的Sheet名称列表")

    model_config = {
        "json_schema_extra": {
            "example": {
                "excel_url": "https://example.com/data/some_data_file.xlsx",
                "sheets_to_import": ["用户信息", "产品列表"]
            }
        }
    }



# --- 接口 1: 异步启动刷新任务 ---
@router.post("/refresh-from-url-async", status_code=status.HTTP_202_ACCEPTED, summary="异步从URL刷新数据库")
async def start_refresh_db_from_url(request: ExcelImportRequest):
    """
    接收Excel URL和Sheet列表，启动一个后台异步刷新任务:
    1.  将导入请求提交到后台任务队列。
    2.  立即返回一个任务ID。
    3.  客户端需要使用此ID去查询任务状态。
    """
    start_time = time.time()

    app_logger.info(f"收到异步刷新请求, URL: {request.excel_url}, Sheets: {request.sheets_to_import}")

    try:
        # 调用 .delay() 方法将任务放入Celery队列
        # Pydantic的HttpUrl类型需要转为字符串
        task = import_excel_task.delay(
            excel_url=str(request.excel_url),
            sheets_to_import=request.sheets_to_import
        )

        response_data = {
            "task_id": task.id,
            "message": "数据库刷新任务已成功提交，正在后台处理中。"
        }
        app_logger.info(f"任务已提交，Task ID: {task.id}")

        # 计算耗时（毫秒）
        time_cost = int((time.time() - start_time) * 1000)
        return make_success(response_data, time_cost)

    except Exception as e:
        app_logger.error(f"提交刷新任务时发生错误: {e}", exc_info=True)
        return make_fail(500, f"提交任务失败: {e}")


# --- 接口 2: 查询任务状态和结果 ---
@router.get("/import-status/{task_id}", summary="查询Excel导入任务的状态")
async def get_import_status(task_id: str):
    """
    根据任务ID查询后台导入任务的当前状态、进度和最终结果。
    当任务完成时，返回 OSS 文件链接。  # noqa: RUF002
    """
    start_time = time.time()

    app_logger.debug(f"查询任务状态, Task ID: {task_id}")

    # 从Celery结果后端获取任务结果对象
    task_result = AsyncResult(task_id, app=celery_app)

    # 根据任务状态构建清晰的响应数据
    if task_result.state == 'PENDING':
        response_data = {
            "task_id": task_id,
            "status": "pending",
            "message": "任务正在等待执行或任务ID不存在"
        }
    elif task_result.state == 'PROGRESS':
        response_data = {
            "task_id": task_id,
            "status": "running",
            "message": "任务正在执行中",
            "current_stage": task_result.info.get('current_stage', '') if task_result.info else '',
            "progress": task_result.info.get('progress', 0) if task_result.info else 0
        }
    elif task_result.state == 'SUCCESS':
        # 只返回核心的导入结果信息
        task_info = task_result.info or {}
        response_data = {
            "task_id": task_id,
            "status": "success",
            "message": "Excel 导入完成",
            "summary": {
                "total_sheets": len(task_info.get('sheets_to_import', [])),
                "successful_imports": task_info.get('successful_imports', 0),
                "failed_imports": task_info.get('failed_imports', 0),
                "total_time_seconds": task_info.get('total_time_seconds', 0),
                "completed_at": task_info.get('completed_at')
            },
            "import_results": task_info.get('import_results', [])
        }
    elif task_result.state == 'FAILURE':
        response_data = {
            "task_id": task_id,
            "status": "failed",
            "message": "任务执行失败",
            "error": str(task_result.info) if task_result.info else "未知错误"
        }

    # 计算耗时（毫秒）
    time_cost = int((time.time() - start_time) * 1000)
    return make_success(response_data, time_cost)


