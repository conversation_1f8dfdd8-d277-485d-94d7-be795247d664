#!/usr/bin/env python
import asyncio
import logging
import mimetypes
import os
import time
from datetime import datetime

from fastapi import APIRouter, Request

from config.env_config import UPLOAD_FOLDER
from configurer.config_reader import get_vl_config
from llm.readImg2md import convert_images_and_merge, convert_local_images_and_merge
from models.file_info import FileInfo
from models.result import make_fail, make_success
from utils.file_utils import detect_file_type

router = APIRouter(include_in_schema=False)
logger = logging.getLogger(name=__name__)


def extract_text_from_image(img_url: str):
    """
    使用视觉大模型 / OCR 模型提取图片文字及样式信息
    这里的 ocr_model 仅作示例，需要根据实际模型API来实现
    """
    markdown_output = convert_images_and_merge([img_url])
    return markdown_output


def extract_text_from_pdf(file_info: FileInfo):
    """
    1. 将 PDF 拆分为图片
    2. 对每一页图片做 OCR
    3. 合并结果并输出
    """
    # 将每一页转为图像列表
    from utils.pdf_util import pdf_to_images
    now = datetime.now()
    formatted_time = now.strftime("%Y%m%d%H%M%S")
    output_folder = f"{UPLOAD_FOLDER}/{formatted_time}/{file_info.file_name}"

    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    images = pdf_to_images(file_info.file_path, output_folder, dpi=300)

    config = get_vl_config()
    max_pages = config.get('max_pages', 30)
    images = images[:max_pages]

    final_markdown = convert_local_images_and_merge(images)

    return final_markdown


@router.post('/image/convert/markdown')
async def extract_doc_to_markdown(request: Request):
    # 1. 下载文件或直接读取
    from utils.request_utils import upload

    start = time.time()

    try:
        file_info = asyncio.run(upload(request))

        if not file_info:
            return make_fail(400, "file upload failed.")

        # 2. 判断文件类型
        file_type = detect_file_type(file_info.file_path)

        # 3. 根据类型处理
        if file_type == 'image':
            md_output = extract_text_from_image(file_info.file_url)
        elif file_type == 'pdf':
            md_output = extract_text_from_pdf(file_info)
        else:
            md_output = "Unsupported file type!"

        # 4. 返回
        return make_success(md_output, time_cost=(int)(time.time() - start) * 1000)
    except Exception as e:
        logger.error(f"Error processing file: ", e)
        return make_fail(500, "Internal Server Error")
