#!/usr/bin/env python3
# health_check.py
"""
Excel 导入系统健康检查脚本
检查各个组件是否正常工作
"""

import sys
import traceback
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def check_redis_connection():
    """检查 Redis 连接"""
    try:
        from config.redis_config import get_redis_client

        redis_client = get_redis_client()
        redis_client.ping()
        print("✅ Redis 连接正常")
        return True
    except Exception as e:
        print(f"❌ Redis 连接失败: {e}")
        return False


def check_mysql_connection():
    """检查 MySQL 连接"""
    try:
        from sqlalchemy import create_engine

        from utils.monitor_excel2sql_utils import get_mysql_config

        mysql_config = get_mysql_config()
        engine = create_engine(
            f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}@{mysql_config['host']}/{mysql_config['database']}?charset=utf8mb4",
            pool_pre_ping=True
        )

        with engine.connect() as conn:
            conn.execute("SELECT 1")

        engine.dispose()
        print(f"✅ MySQL 连接正常")
        return True
    except Exception as e:
        print(f"❌ MySQL 连接失败: {e}")
        return False


def check_oss_configuration():
    """检查 OSS 配置"""
    try:
        from servers.file_server import bucket

        bucket.get_bucket_info()
        print(f"✅ OSS 配置正常")
        return True
    except Exception as e:
        print(f"❌ OSS 配置失败: {e}")
        return False


def check_celery_configuration():
    """检查 Celery 配置"""
    try:
        from celery_task.celery import celery_app

        registered_tasks = list(celery_app.tasks.keys())
        excel_tasks = [task for task in registered_tasks if 'excel' in task.lower() and 'celery' in task.lower()]

        print("✅ Celery 配置正常")
        print(f"   注册的任务数量: {len(registered_tasks)}个")
        print(f"   Excel 相关任务: {len(excel_tasks)}个 (celery 相关的 Excel 任务)")  # celery 相关的 Excel 任务
        print(f"   所有任务: {registered_tasks}")
        return True
    except Exception as e:
        print(f"❌ Celery 配置失败: {e}")
        return False


def check_required_packages():
    """检查必需的包"""
    required_packages = [
        'pandas',
        'openpyxl',
        'celery',
        'redis',
        'sqlalchemy',
        'pymysql',
        'requests',
        'oss2'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (未安装)")
            missing_packages.append(package)

    if missing_packages:
        print(f"\n缺少的包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False

    return True


def check_directories():
    """检查必需的目录"""
    from config.settings import settings

    directories = [
        settings.TRANSLATE_FOLDER,
        settings.EXCEL_IMPORT_FOLDER
    ]

    all_ok = True
    for directory in directories:
        if Path(directory).exists():
            print(f"✅ 目录存在: {directory}")
        else:
            print(f"❌ 目录不存在: {directory}")
            all_ok = False

    return all_ok


def main():
    """主函数"""
    print("=" * 60)
    print("Excel 导入系统健康检查")
    print("=" * 60)

    checks = [
        ("必需的 Python 包", check_required_packages),
        ("目录结构", check_directories),
        ("Redis 连接", check_redis_connection),
        ("MySQL 连接", check_mysql_connection),
        ("OSS 配置", check_oss_configuration),
        ("Celery 配置", check_celery_configuration),
    ]

    results = []

    for check_name, check_func in checks:
        print(f"\n检查 {check_name}:")
        print("-" * 40)

        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ 检查 {check_name} 时发生错误: {e}")
            print("详细错误信息:")
            traceback.print_exc()
            results.append((check_name, False))

    # 总结
    print("\n" + "=" * 60)
    print("检查结果总结:")
    print("=" * 60)

    all_passed = True
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name}: {status}")
        if not result:
            all_passed = False

    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有检查都通过了！系统可以正常使用。")
        print("\n下一步:")
        print("1. 启动 Celery Worker: python start_celery.py")
        print("2. 启动 FastAPI 应用")
        print("3. 运行测试: python test_excel_import.py")
    else:
        print("⚠️ 有些检查未通过，请修复相关问题后再使用系统。")

    print("=" * 60)

    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
