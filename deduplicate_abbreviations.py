#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缩略语去重工具
用于去除JSONL文件中重复的缩略语条目。
主要逻辑：
1. 读取JSONL文件
2. 以缩略语、全称和定义为唯一标识符，去除重复条目
3. 保存去重后的数据到新文件
4. 保存重复条目到单独文件（可选）
"""

import json
import argparse
from typing import Dict, List, Set, Tuple


def load_jsonl(file_path: str) -> List[Dict]:
    """
    加载JSONL文件
    
    Args:
        file_path: JSONL文件路径
        
    Returns:
        包含所有条目的列表
    """
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:
                    try:
                        item = json.loads(line)
                        data.append(item)
                    except json.JSONDecodeError as e:
                        print(f"警告: 第{line_num}行JSON解析错误: {e}")
                        continue
    except FileNotFoundError:
        print(f"错误: 文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误: 读取文件时发生错误: {e}")
        return []
    
    return data


def deduplicate_abbreviations(data: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
    """
    去除重复的缩略语条目
    
    Args:
        data: 原始数据列表
        
    Returns:
        (去重后的数据列表, 重复的数据列表)
    """
    seen_combinations: Set[Tuple[str, str, str]] = set()
    unique_data = []
    duplicate_data = []
    
    for item in data:
        # 获取三个关键字段
        abbreviation = item.get('abbreviation', '').strip()
        full_form = item.get('full_form', '').strip()
        definition_cn = item.get('definition_cn', '').strip()
        
        # 创建唯一标识符（三元组）
        combination = (abbreviation, full_form, definition_cn)
        
        if combination in seen_combinations:
            # 重复条目
            duplicate_data.append(item)
            print(f"发现重复条目: {abbreviation} - {full_form} - {definition_cn}")
        else:
            # 唯一条目
            seen_combinations.add(combination)
            unique_data.append(item)
    
    return unique_data, duplicate_data


def save_jsonl(data: List[Dict], file_path: str) -> bool:
    """
    保存数据到JSONL文件
    
    Args:
        data: 要保存的数据列表
        file_path: 输出文件路径
        
    Returns:
        是否保存成功
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            for item in data:
                json_line = json.dumps(item, ensure_ascii=False)
                f.write(json_line + '\n')
        return True
    except Exception as e:
        print(f"错误: 保存文件时发生错误: {e}")
        return False


def print_statistics(original_count: int, unique_count: int, duplicate_count: int):
    """
    打印统计信息
    """
    print("\n" + "="*50)
    print("去重统计信息:")
    print(f"原始条目数量: {original_count}")
    print(f"去重后条目数量: {unique_count}")
    print(f"重复条目数量: {duplicate_count}")
    print(f"去重率: {duplicate_count/original_count*100:.2f}%")
    print("="*50)


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='缩略语去重工具')
    parser.add_argument('-i', '--input', default='缩略语.jsonl',
                       help='输入的JSONL文件路径 (默认: 缩略语.jsonl)')
    parser.add_argument('-o', '--output', default='缩略语_去重.jsonl', 
                       help='输出文件路径 (默认: 缩略语_去重.jsonl)')
    parser.add_argument('--save-duplicates', action='store_true',
                       help='是否保存重复条目到单独文件')
    
    args = parser.parse_args()
    
    print(f"正在加载文件: {args.input}")
    data = load_jsonl(args.input)
    
    if not data:
        print("没有数据可处理")
        return
    
    print(f"加载了 {len(data)} 条记录")
    print("正在进行去重...")
    
    unique_data, duplicate_data = deduplicate_abbreviations(data)
    
    # 打印统计信息
    print_statistics(len(data), len(unique_data), len(duplicate_data))
    
    # 保存去重后的数据
    print(f"\n正在保存去重后的数据到: {args.output}")
    if save_jsonl(unique_data, args.output):
        print("去重后的数据保存成功!")
    else:
        print("保存失败!")
        return
    
    # 如果指定了保存重复条目
    if args.save_duplicates and duplicate_data:
        duplicate_file = args.output.replace('.jsonl', '_重复条目.jsonl')
        print(f"正在保存重复条目到: {duplicate_file}")
        if save_jsonl(duplicate_data, duplicate_file):
            print("重复条目保存成功!")
        else:
            print("保存重复条目失败!")


if __name__ == "__main__":
    main()
