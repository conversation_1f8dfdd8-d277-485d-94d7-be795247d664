#!/usr/bin/env python3
# start_celery.py
"""
Celery Worker 启动脚本
用于启动 Excel 导入任务的 Celery Worker
"""

import os
import sys
import subprocess
from pathlib import Path
from logger.logger import app_logger

def start_celery_worker():
    """启动 Celery Worker"""
    
    # 确保在项目根目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    app_logger.info("=" * 60)
    app_logger.info("启动 Celery Worker for Excel Import Tasks")
    app_logger.info("=" * 60)
    app_logger.info(f"项目根目录: {project_root}")
    app_logger.info(f"当前工作目录: {os.getcwd()}")

    # 检查环境
    env = os.environ.get("ENV", "local")
    app_logger.info(f"运行环境: {env}")
    
    # 构建 Celery 启动命令
    cmd = [
        sys.executable, "-m", "celery",
        "-A", "celery_task",
        "worker",
        "--loglevel=INFO",
        "--pool=threads",
        "--concurrency=4"
    ]
    
    app_logger.info(f"启动命令: {' '.join(cmd)}")
    app_logger.info("=" * 60)
    app_logger.info("Celery Worker 正在启动...")
    app_logger.info("按 Ctrl+C 停止 Worker")
    app_logger.info("=" * 60)
    
    try:
        # 启动 Celery Worker
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        app_logger.info("\n收到停止信号，正在关闭 Celery Worker...")
    except subprocess.CalledProcessError as e:
        app_logger.error(f"启动 Celery Worker 失败: {e}")
        sys.exit(1)
    except Exception as e:
        app_logger.error(f"发生未知错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    start_celery_worker()
