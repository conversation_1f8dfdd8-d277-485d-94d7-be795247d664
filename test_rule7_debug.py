#!/usr/bin/env python3
"""
测试规则7的问题调试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.monitor_report_utils import process_rule7_data

def test_rule7_with_your_data():
    """测试你提供的规则7数据"""
    test_data = [
        {
            "受试者编号": "11008",
            "不良事件名称_aeterm": "社区获得性肺炎",
            # 注意：这里没有 "开始日期_aestdat" 字段，模拟你的实际数据
            "是否有药物或非药物治疗_aetrea": "合并药物治疗",
            "治疗手段": "药物名称：维生素B1片 用药开始时间：2024-06-09 给药原因：不良事件",
            "ctcae分级_aectcae": "2级",
            "当前治疗是否合理": "不合理",
            "判断依据": "系统显示当前使用的药物为维生素B1片，自2024-06-09开始用于治疗社区获得性肺炎。根据知识库1中'感染和传染性疾病-肺感染'的CTCAE分级描述，2级社区获得性肺炎属于中度症状，需要口服药物治疗，且明确推荐使用抗生素、抗真菌或抗病毒药物。知识库2中'肺炎'的分级描述也指出2级需要治疗。因此，该不良事件在CTCAE 2级情况下应当进行药物治疗。然而，维生素B1属于维生素类营养素，主要用于营养支持或代谢辅助，并不具备抗感染作用，不属于肺炎的推荐治疗药物，无法针对病原体或控制肺部感染。因此，尽管患者接受了'药物治疗'，但所用药物与社区获得性肺炎的规范治疗方案不符。"
        }
    ]
    
    result = process_rule7_data(test_data)
    print("测试你的数据 - 没有开始日期字段:")
    print(f"开始日期: '{result[0]['开始日期']}'")
    print(f"描述: {result[0]['描述']}")
    print()

def test_rule7_with_start_date():
    """测试包含开始日期的规则7数据"""
    test_data = [
        {
            "受试者编号": "11008",
            "不良事件名称_aeterm": "社区获得性肺炎",
            "开始日期_aestdat": "2024-05-27",  # 添加开始日期
            "是否有药物或非药物治疗_aetrea": "合并药物治疗",
            "治疗手段": "药物名称：维生素B1片 用药开始时间：2024-06-09 给药原因：不良事件",
            "ctcae分级_aectcae": "2级",
            "当前治疗是否合理": "不合理",
            "判断依据": "系统显示当前使用的药物为维生素B1片，自2024-06-09开始用于治疗社区获得性肺炎。"
        }
    ]
    
    result = process_rule7_data(test_data)
    print("测试包含开始日期的数据:")
    print(f"开始日期: '{result[0]['开始日期']}'")
    print(f"描述: {result[0]['描述']}")
    print()

if __name__ == "__main__":
    print("测试规则7开始日期处理逻辑")
    print("=" * 50)
    
    test_rule7_with_your_data()
    test_rule7_with_start_date()
    
    print("测试完成！")
