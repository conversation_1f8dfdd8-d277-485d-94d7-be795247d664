# =============================================================================
# Ruff 通用配置模板
# 适用于大多数Python项目，可直接复制使用
# 文档: https://docs.astral.sh/ruff/
# =============================================================================

# 目标Python版本 (根据项目需要调整: py38, py39, py310, py311, py312)
target-version = "py311"

# 每行最大字符数 (常用: 88, 100, 120)
line-length = 120

# 包含的文件模式
include = ["*.py", "*.pyi", "**/pyproject.toml", "*.ipynb"]

# 排除的文件和目录 (根据项目结构调整)
exclude = [
    # 版本控制
    ".bzr", ".direnv", ".eggs", ".git", ".git-rewrite", ".hg", ".svn",
    
    # 缓存和构建目录
    ".mypy_cache", ".nox", ".pants.d", ".pytype", ".ruff_cache", ".tox",
    "__pycache__", "__pypackages__", "_build", "buck-out", "build", "dist",
    
    # 虚拟环境
    ".venv", "venv", "env", "ENV",
    
    # 依赖目录
    "node_modules",
    
    # 常见的项目特定目录 (根据需要删除或添加)
    "migrations",      # Django migrations
    "static",          # 静态文件
    "media",           # 媒体文件
    "locale",          # 国际化文件
    "docs/_build",     # 文档构建目录
    "site-packages",   # 包目录
]

# =============================================================================
# Linting 配置
# =============================================================================
[lint]

# 启用的规则集 (核心推荐规则)
select = [
    # 核心规则
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings  
    "F",   # Pyflakes (未使用导入、变量等)
    
    # 导入排序
    "I",   # isort
    
    # 代码质量
    "B",   # flake8-bugbear (常见bug模式)
    "C4",  # flake8-comprehensions (列表推导式优化)
    "UP",  # pyupgrade (现代Python语法)
    
    # 命名规范
    "N",   # pep8-naming
    
    # 安全检查
    "S",   # flake8-bandit (安全问题)
    
    # 代码简化
    "SIM", # flake8-simplify
    
    # 调试和开发
    "T20", # flake8-print (print语句检查)
    
    # Ruff特定规则
    "RUF", # Ruff-specific rules
    
    # 可选的额外规则 (根据需要启用)
    # "A",   # flake8-builtins (内置名称冲突)
    # "C90", # mccabe (复杂度检查)
    # "D",   # pydocstyle (文档字符串)
    # "ERA", # eradicate (注释代码)
    # "PL",  # Pylint
    # "PT",  # flake8-pytest-style
    # "Q",   # flake8-quotes
    # "RET", # flake8-return
    # "TCH", # flake8-type-checking
]

# 忽略的规则 (根据项目需要调整)
ignore = [
    # 行长度由line-length控制，不需要E501
    "E501",   # line-too-long
    
    # 开发阶段常用的忽略规则
    "S101",   # 允许使用assert (测试中常用)
    "S311",   # 允许使用random模块 (非加密场景)
    "T201",   # 允许使用print() (开发调试)
    "T203",   # 允许使用pprint()
    
    # 代码风格相关
    "B008",   # 允许函数参数中的可变默认值 (有时是必要的)
    "N806",   # 允许变量名使用大写字母 (常量等)
    "SIM108", # 允许使用if-else而不强制三元运算符
    
    # 根据项目需要可能忽略的规则
    # "S105",   # 硬编码密码检查 (配置文件中可能需要)
    # "S106",   # 硬编码密码检查
    # "B904",   # raise语句中的from检查
    # "RUF012", # 可变类变量注解
]

# 可自动修复的规则 (建议保持ALL)
fixable = ["ALL"]

# 不可自动修复的规则
unfixable = []

# 允许未使用的变量 (以下划线开头的变量)
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# =============================================================================
# 特定文件的规则配置
# =============================================================================
[lint.per-file-ignores]

# 测试文件
"test_*.py" = [
    "S101",    # 允许assert
    "S105",    # 允许硬编码密码
    "S106",    # 允许硬编码密码
    "S108",    # 允许临时文件
    "B011",    # 允许assert False
    "PLR2004", # 允许魔法数字
]
"**/test_*.py" = ["S101", "S105", "S106", "S108", "B011", "PLR2004"]
"**/tests/*.py" = ["S101", "S105", "S106", "S108", "B011", "PLR2004"]
"conftest.py" = ["S101", "S105", "S106"]

# 配置文件
"**/settings.py" = ["S105", "S106"]      # Django settings
"**/settings/*.py" = ["S105", "S106"]    # Django settings目录
"**/config.py" = ["S105", "S106"]        # 配置文件
"**/config/*.py" = ["S105", "S106"]      # 配置目录

# 迁移文件
"**/migrations/*.py" = ["ALL"]            # Django migrations忽略所有规则

# 脚本文件
"scripts/*.py" = ["T201", "S101"]         # 脚本允许print和assert
"manage.py" = ["T201"]                    # Django manage.py

# =============================================================================
# Import 排序配置 (isort)
# =============================================================================
[lint.isort]

# 已知的第一方包 (根据项目结构调整)
known-first-party = [
    # 常见的包名，根据实际项目调整
    "api", "app", "apps", "config", "core", "utils", "models", 
    "services", "views", "serializers", "forms", "admin",
    "tests", "scripts", "celery_app", "common"
]

# 已知的第三方包 (可选，通常自动检测)
# known-third-party = ["django", "fastapi", "flask", "requests"]

# 导入排序配置
force-single-line = false
force-sort-within-sections = false
split-on-trailing-comma = true
combine-as-imports = true

# =============================================================================
# 复杂度检查
# =============================================================================
[lint.mccabe]
# 最大复杂度 (建议: 10-15)
max-complexity = 10

# =============================================================================
# 代码格式化配置
# =============================================================================
[format]

# 引号样式: "double", "single", "preserve"
quote-style = "double"

# 缩进样式: "space", "tab"  
indent-style = "space"

# 行尾符号: "auto", "lf", "crlf", "cr"
line-ending = "auto"

# 跳过魔法尾随逗号
skip-magic-trailing-comma = false

# 文档字符串代码格式化
docstring-code-format = true

# 文档字符串代码行长度: "dynamic", 数字
docstring-code-line-length = "dynamic"

# =============================================================================
# 使用说明
# =============================================================================
# 
# 1. 复制此文件到项目根目录，重命名为 ruff.toml
# 
# 2. 根据项目调整以下配置:
#    - target-version: Python版本
#    - line-length: 行长度限制  
#    - exclude: 排除的目录
#    - known-first-party: 项目的包名
#    - 特定规则的启用/忽略
#
# 3. 常用命令:
#    ruff check .                    # 检查代码
#    ruff check . --fix             # 自动修复
#    ruff format .                  # 格式化代码
#    ruff check . --watch           # 监视模式
#
# 4. 在PyCharm中配置External Tools使用这些命令
#
# =============================================================================
