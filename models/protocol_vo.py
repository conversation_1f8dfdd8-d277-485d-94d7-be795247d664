from pydantic import BaseModel, Field

class FileInfo(BaseModel):
    file_name: str = Field(..., description="文件名，用于本地存储")
    file_key: str = Field(..., description="文件在OSS上的完整路径/Key")


class ProtocolFileRequest(BaseModel):
    protocol_file: FileInfo = Field(..., title="protocol file info")
    extract_keys_only: bool = Field(False, title="仅提取Key",
                                    description="如果为 True，则 title_page, protocol_summary, approval_signature_page 部分只返回 key，value 为空字符串。")