from typing import Any
from pydantic import BaseModel, Field


class OcrRequest(BaseModel):
    file_name: str = Field(None, title="file name")
    file_key: str = Field(None, title="file key")
    cut_point: list = Field(None, title="cut point")
    type: str = Field(None, title="type")


class OcrResponse(BaseModel):
    code: int = Field(...)
    success: bool = Field(False)
    message: str = Field(None)
    result: Any = Field(None)


class RenameZip(BaseModel):
    rename_json: list[str] = Field(None, title="rename json")
