from pydantic import BaseModel, Field
from typing import List
from models.file_info import FileInfo

class RtfFileRequest(BaseModel):
    rtf_file: FileInfo = Field(None, title="rtf file info")

class InTextTFL(BaseModel):
    """ 'in-text TFL' 的数据结构 """
    sectionName: str = Field(..., title="章节名", example="10.1 患者分布")
    inTextName: str = Field(..., title="文中TFL名称", example="患者分布")

class TFL(BaseModel):
    """ 'TFL' 的数据结构 """
    id: int = Field(..., title="TFL的唯一ID", example=1)
    name: str = Field(..., title="TFL名称", example="试验筛选情况（所有筛选患者）")
    code: str = Field(..., title="TFL编码", example="14.1.1.1")

class TFLMatchRequest(BaseModel):
    """ TFL匹配请求的完整请求体 """
    inTextTFLs: List[InTextTFL] = Field(..., title="'in-text TFLs' 列表")
    TFLs: List[TFL] = Field(..., title="'TFLs' 列表")
