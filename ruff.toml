# Ruff 配置文件
# 更多配置选项请参考: https://docs.astral.sh/ruff/configuration/

# 目标 Python 版本
target-version = "py311"

# 每行最大字符数
line-length = 120

# 包含的文件模式
include = ["*.py", "*.pyi", "**/pyproject.toml", "*.ipynb"]

# 排除的文件和目录
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "migrations",  # Django migrations
    "APP-META",    # 项目特定排除
    "nacos-data",  # 项目特定排除
]

[lint]
# 启用的规则集
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # Pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "N",   # pep8-naming
    "S",   # flake8-bandit (security)
    "T20", # flake8-print
    "SIM", # flake8-simplify
    "RUF", # Ruff-specific rules
]

# 忽略的规则
ignore = [
    "E501",   # 行长度由 line-length 控制
    "S101",   # 允许使用 assert
    "S311",   # 允许使用 random 模块
    "T201",   # 允许使用 print() - 可根据需要调整
    "B008",   # 允许在函数参数中使用可变默认值
    "N806",   # 允许变量名使用大写字母
    "SIM108", # 允许使用 if-else 而不是三元运算符
]

# 每个文件允许的自动修复数量
fixable = ["ALL"]
unfixable = []

# 允许未使用的变量（以下划线开头）
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[lint.per-file-ignores]
# 测试文件的特殊规则
"test_*.py" = ["S101", "S105", "S106"]
"**/test_*.py" = ["S101", "S105", "S106"]
"tests/*.py" = ["S101", "S105", "S106"]

# 配置文件的特殊规则
"**/settings.py" = ["S105", "S106"]
"**/config/*.py" = ["S105", "S106"]

[lint.isort]
# isort 配置
known-first-party = ["api", "config", "utils", "models", "logger", "celery_task"]
force-single-line = false
force-sort-within-sections = false
split-on-trailing-comma = true

[lint.mccabe]
# 复杂度检查
max-complexity = 10

[format]
# 格式化配置
quote-style = "double"
indent-style = "space"
line-ending = "auto"

# 字符串格式化
docstring-code-format = true
docstring-code-line-length = "dynamic"
