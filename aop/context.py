from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import Response

from logger.logger import request_id


class ContextMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        request_id_str = request.headers.get("x-request-id", "")
        request_id.set(request_id_str)
        response = await call_next(request)
        request_id.set(None)
        return response
