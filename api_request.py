import time
import uuid
from datetime import datetime

import requests


def send_request():
    # 固定的URL参数
    protocol_url = "https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/oss-20250528032757-protocol_8c59024c-2e32-4634-8328-42a3f833ea39.docx?x-oss-date=20250528T032758Z&x-oss-expires=86400&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tSMtzYQ5GVPCm8njDYp%2F20250528%2Fcn-hangzhou%2Foss%2Faliyun_v4_request&x-oss-signature=98eb2621efb4a50c7dac19f0782eb4d80c60e6b13e643f5604930efda6e4e55e"
    tfl_url = "https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/oss-20250528032757-tfl_8c59024c-2e32-4634-8328-42a3f833ea39.docx?x-oss-date=20250528T032757Z&x-oss-expires=86400&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tSMtzYQ5GVPCm8njDYp%2F20250528%2Fcn-hangzhou%2Foss%2Faliyun_v4_request&x-oss-signature=dedcf4353eed7eec5610b9b5eadb251a13a7fb86e9cbe636170740ce146145e1"

    # 生成新的workflow_run_idç
    workflow_run_id = str(uuid.uuid4())

    # 准备表单数据
    files = {
        'protocol_url': (None, protocol_url),
        'tfl_url': (None, tfl_url),
        'workflow_run_id': (None, workflow_run_id)
    }
    # 发送请求到
    url = "http://dev.bot.yiya-ai.com/extract_and_upload"
    response = requests.post(url, files=files)

    # 打印结果
    print(f"[{datetime.now()}] Request sent with ID: {workflow_run_id}")
    print(f"Response status: {response.status_code}")
    print(f"Response content: {response.text}\n")


# 每分钟发送一次请求
while True:
    send_request()
    time.sleep(60)  # 等待60秒
