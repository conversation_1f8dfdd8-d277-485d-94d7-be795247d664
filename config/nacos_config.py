# config/nacos_config.py
"""
Nacos 配置管理
提供 Nacos 连接的配置参数和环境变量管理
"""

import os
from typing import Dict, Any


class NacosConfig:
    """Nacos 配置类"""
    
    def __init__(self):
        self.server_addresses = os.environ.get(
            'NACOS_SERVER', 
            'mse-450596b0-p.nacos-ans.mse.aliyuncs.com:8848'
        )
        self.namespace = os.environ.get('NACOS_NAMESPACE', 'yy-prod')
        self.ak = "LTAI5tSMtzYQ5GVPCm8njDYp"
        self.sk = "******************************"
        
        # 连接配置
        self.timeout = int(os.environ.get('NACOS_TIMEOUT', '30'))
        self.pulling_timeout = int(os.environ.get('NACOS_PULLING_TIMEOUT', '30'))
        self.pulling_config_size = int(os.environ.get('NACOS_PULLING_CONFIG_SIZE', '3000'))
        self.callback_thread_num = int(os.environ.get('NACOS_CALLBACK_THREAD_NUM', '1'))
        
        # 重试配置
        self.max_retries = int(os.environ.get('NACOS_MAX_RETRIES', '3'))
        self.retry_delay = int(os.environ.get('NACOS_RETRY_DELAY', '2'))
        self.config_retry = int(os.environ.get('NACOS_CONFIG_RETRY', '2'))
        
        # 故障转移配置
        self.enable_failover = os.environ.get('NACOS_ENABLE_FAILOVER', 'true').lower() == 'true'
        self.failover_base = os.environ.get('NACOS_FAILOVER_BASE', os.path.join(os.getcwd(), "nacos-failover"))
        self.snapshot_base = os.environ.get('NACOS_SNAPSHOT_BASE', os.path.join(os.getcwd(), "nacos-snapshot"))
        self.no_snapshot = os.environ.get('NACOS_NO_SNAPSHOT', 'false').lower() == 'true'
        
        # 是否启用配置监听器
        self.enable_watcher = os.environ.get('NACOS_ENABLE_WATCHER', 'true').lower() == 'true'
        
        # 是否在 Celery Worker 中启用 Nacos
        self.enable_in_celery = os.environ.get('NACOS_ENABLE_IN_CELERY', 'true').lower() == 'true'

    def get_client_config(self) -> Dict[str, Any]:
        """获取 Nacos 客户端配置"""
        # nacos-sdk-python==0.1.14 只支持这些基本参数
        config = {
            'server_addresses': self.server_addresses,
            'namespace': self.namespace,
            'ak': self.ak,
            'sk': self.sk,
        }

        # 注意：nacos-sdk-python==0.1.14 不支持以下参数：
        # - timeout, pulling_timeout, pulling_config_size, callback_thread_num
        # - failover_base, snapshot_base, no_snapshot
        # 这些参数在更高版本中才支持

        return config

    def should_enable_nacos(self) -> bool:
        """判断是否应该启用 Nacos"""
        # 在某些环境下可能需要禁用 Nacos
        if os.environ.get('DISABLE_NACOS', 'false').lower() == 'true':
            return False
        return True

    def should_enable_in_celery(self) -> bool:
        """判断是否在 Celery Worker 中启用 Nacos"""
        return self.enable_in_celery and self.should_enable_nacos()


# 全局配置实例
nacos_config = NacosConfig()
