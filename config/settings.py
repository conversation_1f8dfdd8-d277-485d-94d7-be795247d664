from pydantic_settings import BaseSettings
import os

class Settings(BaseSettings):
    REDIS_HOST: str = "***********"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 3  # 使用 db3 作为 Excel 导入专用 broker
    REDIS_TASK_DB:  int = 3  # 使用 db3 作为结果存储
    REDIS_PASSWORD: str = "wtg2024@"

    REDIS_DB_LOCAL: int = 3  # 本地环境也使用 db3
    REDIS_TASK_DB_LOCAL: int = 3


    # 任务配置
    TASK_RESULT_EXPIRE_TIME: int = 60 * 60 * 24  # 24小时
    MAX_CONCURRENT_TASKS: int = 10
    TASK_TIMEOUT: int = 3600
    MAX_RETRY_TIMES: int = 3


    # Excel 导入相关配置
    EXCEL_IMPORT_FOLDER: str = 'excel_import_files'
    if not os.path.exists(EXCEL_IMPORT_FOLDER):
        os.makedirs(EXCEL_IMPORT_FOLDER)

settings = Settings()