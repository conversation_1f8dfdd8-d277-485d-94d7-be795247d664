import redis
from loguru import logger
from config.settings import settings


class RedisManager:
    """Redis连接管理器，提供单例Redis客户端"""

    _instance = None
    _redis_client = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(RedisManager, cls).__new__(cls)
            cls._instance._init_redis_client()
        return cls._instance

    def _init_redis_client(self):
        """初始化Redis客户端连接"""
        try:
            # 构建 Redis URL，使用 db1
            redis_url = f"redis://:{settings.REDIS_PASSWORD}@{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"
            self._redis_client = redis.from_url(redis_url, socket_timeout=10)
            logger.info(f"Redis客户端初始化成功，连接到 db{settings.REDIS_DB}")
        except Exception as e:
            logger.error(f"Redis客户端初始化失败: {str(e)}")
            raise

    @property
    def client(self):
        """获取Redis客户端实例"""
        if self._redis_client is None:
            self._init_redis_client()
        return self._redis_client


# 提供一个便捷的函数来获取Redis客户端
def get_redis_client():
    """获取Redis客户端实例"""
    return RedisManager().client