import os
from pathlib import Path

home_directory = os.environ.get("HOME") or os.environ.get("USERPROFILE")

LOG_HOME = os.getenv('APP_LOG_DIR', home_directory)


def get_project_root() -> Path:
    return Path(__file__).parent.parent


# 创建存储文件的文件夹
UPLOAD_FOLDER = get_project_root().joinpath('uploads')
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

if __name__ == '__main__':
    print(get_project_root())
