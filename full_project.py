import os
import json
import time
import requests
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
# --- 配置部分 ---
EXCEL_DIR = "your_excel_folder_path"  # 替换为你的本地 Excel 文件夹路径
PROJECT_NAME = "食管鳞癌"  # 统一项目名，同时作为 business_id 与 project_name 使用

WORKFLOW_API_URL = "http://f-dev.yiya-ai.com/v1/workflows/run"
RANK_EXPORT_API = "http://dev.bot.yiya-ai.com/patients/rank-and-export"
API_TOKEN = "app-bTyLM2KxcgWmMbdIhfOitJvv"

HEADERS = {
    'Authorization': f'Bearer {API_TOKEN}',
    'Content-Type': 'application/json'
}


# --- Step 1: 提取病人ID ---
def extract_patient_ids_from_excel(directory: str):
    patient_ids = set()
    for filename in os.listdir(directory):
        if filename.endswith(".xls") or filename.endswith(".xlsx"):
            file_path = os.path.join(directory, filename)
            try:
                excel_data = pd.read_excel(file_path, sheet_name=None)
                for sheet_df in excel_data.values():
                    if "BINGRENID" in sheet_df.columns:
                        ids = sheet_df["BINGRENID"].dropna().astype(str).tolist()
                        patient_ids.update(ids)
            except Exception as e:
                print(f"[ERROR] 读取失败：{filename} - {e}")
    return sorted(patient_ids)


# --- Step 2: 请求 Dify Workflow 接口 ---
def call_workflow_api(patient_id: str) -> str:
    try:
        payload = {
            "inputs": {
                "patient_id": patient_id,
                "project_name": PROJECT_NAME
            },
            "response_mode": "blocking",
            "conversation_id": "",
            "user": "local_script",
            "files": []
        }
        response = requests.post(WORKFLOW_API_URL, headers=HEADERS, json=payload)
        response.raise_for_status()
        data = response.json()
        if "data" in data and isinstance(data["data"], dict):
            return json.dumps(data["data"], ensure_ascii=False)
        else:
            print(f"[WARN] 返回格式不正确：{patient_id}")
    except Exception as e:
        print(f"[ERROR] 请求失败：{patient_id} - {e}")
    return None


# --- Step 3: 并发处理所有病人 ---
def run_all_patients(patient_ids, max_workers=10):
    results = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_pid = {executor.submit(call_workflow_api, pid): pid for pid in patient_ids}
        for future in tqdm(as_completed(future_to_pid), total=len(future_to_pid), desc="评估进度"):
            pid = future_to_pid[future]
            try:
                result = future.result()
                if result:
                    results.append(result)
            except Exception as e:
                print(f"[ERROR] 病人 {pid} 执行失败: {e}")
    return results


# --- Step 4: 汇总并调用评分接口 ---
def call_rank_and_export(project_name: str, patient_json_strings: list[str]):
    try:
        payload = {
            "business_id": project_name,
            "patient_json_strings": patient_json_strings
        }
        response = requests.post(RANK_EXPORT_API, json=payload)
        response.raise_for_status()
        print("[OK] 排名与导出成功")
        return response.json()
    except Exception as e:
        print(f"[ERROR] 导出失败: {e}")
        return None


# --- 主函数入口 ---
def main():
    print("🔍 正在提取病人ID...")
    patient_ids = extract_patient_ids_from_excel(EXCEL_DIR)
    print(f"📋 共发现 {len(patient_ids)} 位病人：", patient_ids)

    print("\n🚀 开始并发请求评估...")
    results = run_all_patients(patient_ids)

    print(f"\n📦 收到 {len(results)} 个评估结果，准备提交评分接口...")
    rank_result = call_rank_and_export(PROJECT_NAME, results)

    print("\n✅ 最终结果：")
    print(json.dumps(rank_result, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()