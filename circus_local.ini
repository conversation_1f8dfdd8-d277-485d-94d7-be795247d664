[circus]
check_delay=5
endpoint=tcp://127.0.0.1:5555
pubsub_endpoint=tcp://127.0.0.1:5556
statsd=false
pidfile=./logs/circus.pid

[watcher:yiya-ai-bot]
working_dir=/Users/<USER>/PycharmProjects/yiya-ai-bot
cmd=uvicorn app:app --host 127.0.0.1 --port 7860
stop_signal=QUIT
stdout_stream.class=FileStream
stdout_stream.filename=./logs/application.log
stdout_stream.max_bytes=20971520
stdout_stream.backup_count=30
stderr_stream.class=FileStream
stderr_stream.filename=./logs/error.log
stderr_stream.max_bytes=20971520
stderr_stream.backup_count=30

[watcher:yiya-ai-bot-celery]
working_dir=/Users/<USER>/PycharmProjects/yiya-ai-bot
cmd=celery -A celery_task worker --loglevel=INFO --pool=threads --concurrency=4
numprocesses=1
stop_signal=QUIT
stdout_stream.class=FileStream
stdout_stream.filename=./logs/celery.log
stdout_stream.max_bytes=20971520
stdout_stream.backup_count=30
stderr_stream.class=FileStream
stderr_stream.filename=./logs/celery_error.log
stderr_stream.max_bytes=20971520
stderr_stream.backup_count=30

[env]
PATH=$PATH
PYTHONPATH=/Users/<USER>/PycharmProjects/yiya-ai-bot
LOG_HOME=/Users/<USER>/PycharmProjects/yiya-ai-bot/logs
APP_LOG_DIR=/Users/<USER>/PycharmProjects/yiya-ai-bot/logs
ENV=local
