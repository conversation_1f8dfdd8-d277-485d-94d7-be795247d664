#!/usr/bin/env python3
"""
Celery线程详细分析工具
"""

import psutil
import threading
import time
from datetime import datetime


def analyze_celery_threads():
    """分析Celery进程中的线程"""
    print("Celery线程详细分析")
    print("="*60)
    
    # 查找Celery进程
    celery_proc = None
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            if 'celery' in cmdline.lower() and 'worker' in cmdline.lower():
                celery_proc = proc
                break
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if not celery_proc:
        print("未找到Celery Worker进程")
        return
    
    print(f"找到Celery进程: PID {celery_proc.pid}")
    print(f"总线程数: {celery_proc.num_threads()}")
    print(f"内存使用: {celery_proc.memory_info().rss / 1024 / 1024:.1f}MB")
    
    # 获取线程详情
    try:
        threads = celery_proc.threads()
        print(f"\n线程详情:")
        print(f"{'线程ID':<10} {'用户时间':<10} {'系统时间':<10}")
        print("-" * 35)
        
        for thread in threads:
            print(f"{thread.id:<10} {thread.user_time:<10.2f} {thread.system_time:<10.2f}")
            
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        print("无法获取线程详情")


def explain_celery_thread_types():
    """解释Celery线程类型"""
    print("\n" + "="*60)
    print("Celery线程类型说明")
    print("="*60)
    
    thread_types = {
        "工作线程 (Worker Threads)": {
            "数量": "4个 (--concurrency=4)",
            "作用": "执行实际的任务 (Excel导入)",
            "状态": "大部分时间在I/O等待"
        },
        "主线程 (Main Thread)": {
            "数量": "1个",
            "作用": "Celery Worker主控制线程",
            "状态": "管理其他线程，处理信号"
        },
        "消息消费线程 (Consumer Thread)": {
            "数量": "1-2个",
            "作用": "从Redis队列获取任务消息",
            "状态": "监听队列，分发任务"
        },
        "心跳线程 (Heartbeat Thread)": {
            "数量": "1个",
            "作用": "发送Worker心跳信号",
            "状态": "定期向Broker报告状态"
        },
        "事件线程 (Event Thread)": {
            "数量": "1-2个",
            "作用": "发送任务执行事件",
            "状态": "报告任务状态变化"
        },
        "监控线程 (Monitor Threads)": {
            "数量": "2-3个",
            "作用": "监控Worker状态和性能",
            "状态": "收集统计信息"
        },
        "定时器线程 (Timer Threads)": {
            "数量": "2-3个",
            "作用": "处理定时任务和超时",
            "状态": "管理任务超时和调度"
        },
        "连接池线程 (Connection Pool)": {
            "数量": "2-4个",
            "作用": "管理Redis连接池",
            "状态": "维护网络连接"
        },
        "垃圾回收线程 (GC Threads)": {
            "数量": "1-2个",
            "作用": "Python垃圾回收",
            "状态": "内存管理"
        },
        "信号处理线程 (Signal Threads)": {
            "数量": "1-2个",
            "作用": "处理系统信号",
            "状态": "响应SIGTERM等信号"
        }
    }
    
    total_estimated = 0
    for name, info in thread_types.items():
        print(f"\n{name}:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        # 估算数量
        if "个" in info["数量"]:
            try:
                num_str = info["数量"].split("个")[0]
                if "-" in num_str:
                    nums = num_str.split("-")
                    avg_num = (int(nums[0]) + int(nums[1])) / 2
                else:
                    avg_num = int(num_str)
                total_estimated += avg_num
            except:
                pass
    
    print(f"\n估算总线程数: {total_estimated:.0f}个")
    print(f"实际观察到: 20个")
    print(f"差异说明: 实际线程数会根据系统状态动态变化")


def monitor_thread_activity():
    """监控线程活动"""
    print("\n" + "="*60)
    print("线程活动监控")
    print("="*60)
    
    # 查找Celery进程
    celery_proc = None
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            if 'celery' in cmdline.lower() and 'worker' in cmdline.lower():
                celery_proc = proc
                break
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if not celery_proc:
        print("未找到Celery进程")
        return
    
    print("开始监控线程活动 (按Ctrl+C停止)...")
    
    try:
        prev_threads = None
        while True:
            try:
                current_threads = celery_proc.num_threads()
                cpu_percent = celery_proc.cpu_percent()
                memory_mb = celery_proc.memory_info().rss / 1024 / 1024
                
                status = "稳定"
                if prev_threads and current_threads != prev_threads:
                    status = f"变化 ({prev_threads}→{current_threads})"
                
                print(f"[{datetime.now().strftime('%H:%M:%S')}] "
                      f"线程: {current_threads:2d} | "
                      f"CPU: {cpu_percent:5.1f}% | "
                      f"内存: {memory_mb:5.1f}MB | "
                      f"状态: {status}")
                
                prev_threads = current_threads
                time.sleep(2)
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                print("Celery进程已退出")
                break
                
    except KeyboardInterrupt:
        print("\n监控已停止")


def compare_with_other_pools():
    """对比不同池类型的线程数"""
    print("\n" + "="*60)
    print("不同池类型的线程数对比")
    print("="*60)
    
    pool_comparison = {
        "threads池 (当前)": {
            "工作线程": "4个 (--concurrency=4)",
            "系统线程": "~16个",
            "总线程": "~20个",
            "特点": "线程池管理，适合I/O密集型"
        },
        "prefork池": {
            "工作进程": "4个 (--concurrency=4)",
            "每进程线程": "~5个",
            "总线程": "~20个 (分布在4个进程)",
            "特点": "进程池管理，适合CPU密集型"
        },
        "gevent池": {
            "协程": "4个 (--concurrency=4)",
            "系统线程": "~8个",
            "总线程": "~8个",
            "特点": "协程管理，超高并发"
        }
    }
    
    for pool_type, info in pool_comparison.items():
        print(f"\n{pool_type}:")
        for key, value in info.items():
            print(f"  {key}: {value}")


def analyze_cpu_cores_relationship():
    """分析CPU核数与进程/线程的关系"""
    print("\n" + "="*60)
    print("CPU核数与进程/线程关系分析")
    print("="*60)

    # 获取CPU信息
    cpu_count = psutil.cpu_count(logical=False)  # 物理核心
    logical_cpu_count = psutil.cpu_count(logical=True)  # 逻辑核心(包含超线程)

    print(f"系统CPU信息:")
    print(f"  物理核心数: {cpu_count}")
    print(f"  逻辑核心数: {logical_cpu_count} (包含超线程)")

    # 分析不同架构的CPU利用
    architectures = {
        "多进程架构 (Gunicorn)": {
            "进程数": 4,
            "每进程线程": 8,
            "总线程": 32,
            "CPU利用": f"可以充分利用{min(4, cpu_count)}个物理核心",
            "适用场景": "CPU密集型Web服务",
            "优势": "真正的并行计算，绕过GIL"
        },
        "单进程多线程 (Celery当前)": {
            "进程数": 1,
            "每进程线程": 20,
            "总线程": 20,
            "CPU利用": f"受GIL限制，主要使用1个核心，I/O时可切换",
            "适用场景": "I/O密集型任务",
            "优势": "内存共享，上下文切换快"
        },
        "多进程+多线程 (混合)": {
            "进程数": 2,
            "每进程线程": 10,
            "总线程": 20,
            "CPU利用": f"可以利用2个物理核心",
            "适用场景": "混合型工作负载",
            "优势": "平衡并行性和资源使用"
        }
    }

    for arch_name, info in architectures.items():
        print(f"\n{arch_name}:")
        for key, value in info.items():
            print(f"  {key}: {value}")


def explain_gil_impact():
    """解释GIL对多线程的影响"""
    print("\n" + "="*60)
    print("Python GIL (全局解释器锁) 影响分析")
    print("="*60)

    gil_explanation = {
        "GIL是什么": "Python的全局解释器锁，同一时间只允许一个线程执行Python字节码",
        "对CPU密集型任务": "多线程无法真正并行，受限于单核性能",
        "对I/O密集型任务": "线程在等待I/O时会释放GIL，其他线程可以执行",
        "你的Excel任务": "主要是I/O操作，GIL影响很小"
    }

    for concept, explanation in gil_explanation.items():
        print(f"\n{concept}:")
        print(f"  {explanation}")

    print(f"\n实际场景分析:")
    scenarios = {
        "网络下载Excel": "线程阻塞在网络I/O → 释放GIL → 其他线程可执行",
        "读取Excel文件": "线程阻塞在磁盘I/O → 释放GIL → 其他线程可执行",
        "数据库写入": "线程阻塞在数据库I/O → 释放GIL → 其他线程可执行",
        "数据处理": "少量CPU计算 → 持有GIL → 但时间很短"
    }

    for scenario, behavior in scenarios.items():
        print(f"  {scenario}: {behavior}")


def recommend_optimal_config():
    """推荐最优配置"""
    print("\n" + "="*60)
    print("基于CPU核数的配置建议")
    print("="*60)

    cpu_count = psutil.cpu_count(logical=False)
    logical_cpu_count = psutil.cpu_count(logical=True)

    print(f"你的系统: {cpu_count}核心 ({logical_cpu_count}逻辑核心)")

    recommendations = {
        "当前配置 (推荐)": {
            "配置": "1进程 × 4线程",
            "原因": "I/O密集型任务，线程足够",
            "CPU利用": "高效利用I/O等待时间",
            "内存": "最少 (~80MB)"
        },
        "如需更高并发": {
            "配置": f"1进程 × {min(8, logical_cpu_count)}线程",
            "原因": "增加线程数，但不超过逻辑核心数",
            "CPU利用": "更好的I/O并发",
            "内存": "略增 (~100MB)"
        },
        "如果是CPU密集型": {
            "配置": f"{min(cpu_count, 4)}进程 × 1线程",
            "原因": "绕过GIL，真正并行",
            "CPU利用": "充分利用多核",
            "内存": "较多 (~200MB)"
        }
    }

    for config_name, details in recommendations.items():
        print(f"\n{config_name}:")
        for key, value in details.items():
            print(f"  {key}: {value}")


def main():
    """主函数"""
    print("Celery线程分析工具")
    print("="*50)

    # 分析当前线程
    analyze_celery_threads()

    # 解释线程类型
    explain_celery_thread_types()

    # 对比不同池类型
    compare_with_other_pools()

    # 分析CPU核数关系
    analyze_cpu_cores_relationship()

    # 解释GIL影响
    explain_gil_impact()

    # 推荐配置
    recommend_optimal_config()

    print("\n" + "="*60)
    print("总结")
    print("="*60)
    print("✅ 20个线程是正常的")
    print("✅ 只有4个是工作线程，其他16个是系统管理线程")
    print("✅ 系统线程负责消息处理、心跳、监控等功能")
    print("✅ 这些线程大部分时间处于等待状态，不消耗CPU")
    print("✅ 你的配置 --concurrency=4 指的是工作线程数")
    print("✅ 对于I/O密集型任务，线程数可以超过CPU核数")

    # 询问是否监控
    response = input("\n是否开始实时监控线程活动? [y/N]: ")
    if response.lower() in ['y', 'yes']:
        monitor_thread_activity()


if __name__ == "__main__":
    main()
