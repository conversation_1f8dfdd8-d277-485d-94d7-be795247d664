#!/usr/bin/env python3
"""
测试规则7修改后的开始日期处理逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.monitor_report_utils import process_rule7_data

def test_rule7_with_start_date():
    """测试包含开始日期的规则7数据"""
    test_data = [
        {
            "受试者编号": "11008",
            "不良事件名称_aeterm": "社区获得性肺炎",
            "开始日期_aestdat": "2024-06-08",  # 添加开始日期
            "是否有药物或非药物治疗_aetrea": "合并药物治疗",
            "治疗手段": "药物名称：维生素B1片 用药开始时间：2024-06-09 给药原因：不良事件",
            "ctcae分级_aectcae": "2级",
            "当前治疗是否合理": "不合理",
            "判断依据": "系统显示当前不良事件为社区获得性肺炎，CTCAE分级为2级。根据知识库1中'感染和传染性疾病-肺感染'的分级描述，2级肺部感染需'需要口服药物治疗（抗生素，抗真菌或抗病毒药物）'；知识库2中'肺炎'2级描述为'有症状；需要治疗'，进一步支持应进行药物治疗。因此，该2级肺炎事件应接受规范的抗感染药物治疗。然而，当前实际使用的药物为维生素B1片，属于维生素类营养补充剂，不具有抗感染作用，与社区获得性肺炎的治疗原则无关，不能替代抗生素或其他抗感染治疗。因此，尽管患者已记录'合并药物治疗'，所用药物不符合该AE的推荐治疗方案。"
        }
    ]
    
    result = process_rule7_data(test_data)
    print("测试1 - 包含开始日期的数据:")
    print(f"开始日期: {result[0]['开始日期']}")
    print(f"描述: {result[0]['描述'][:100]}...")
    print()

def test_rule7_without_start_date():
    """测试不包含开始日期的规则7数据（原始情况）"""
    test_data = [
        {
            "受试者编号": "38018",
            "不良事件名称_aeterm": "肝性脑病",
            "是否有药物或非药物治疗_aetrea": "否",
            "治疗手段": "治疗名称：无 治疗原因：无",
            "ctcae分级_aectcae": "2级",
            "当前治疗是否合理": "不合理",
            "判断依据": "根据提供的知识库信息，肝性脑病2级的描述为'轻度肝性脑病；影响自理性日常生活活动'，但未明确提及是否需要非药物治疗。结合通用参考标准，2级不良事件可能需要小型、局部、非侵入性治疗。肝性脑病通常需要限制蛋白质摄入、使用乳果糖等非药物措施来降低血氨水平，但当前记录为'无'非药物治疗。"
        }
    ]
    
    result = process_rule7_data(test_data)
    print("测试2 - 不包含开始日期的数据:")
    print(f"开始日期: {result[0]['开始日期']}")
    print(f"描述: {result[0]['描述'][:100]}...")
    print()

def test_rule7_with_treatment_start_date():
    """测试只有治疗开始时间的规则7数据"""
    test_data = [
        {
            "受试者编号": "12345",
            "不良事件名称_aeterm": "头痛",
            "是否有药物或非药物治疗_aetrea": "合并药物治疗",
            "治疗手段": "药物名称：阿司匹林 用药开始时间：2024-07-20 给药原因：不良事件",
            "ctcae分级_aectcae": "1级",
            "当前治疗是否合理": "不合理",
            "判断依据": "测试判断依据"
        }
    ]
    
    result = process_rule7_data(test_data)
    print("测试3 - 只有治疗开始时间的数据:")
    print(f"开始日期: {result[0]['开始日期']}")
    print(f"描述: {result[0]['描述'][:100]}...")
    print()

if __name__ == "__main__":
    print("测试规则7开始日期处理逻辑")
    print("=" * 50)
    
    test_rule7_with_start_date()
    test_rule7_without_start_date()
    test_rule7_with_treatment_start_date()
    
    print("测试完成！")
