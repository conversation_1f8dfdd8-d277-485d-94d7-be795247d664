FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/ubuntu:22.04

COPY deb-sources.list /etc/apt/sources.list

RUN apt update
RUN apt install -y curl
RUN apt install -y wget
RUN apt install -y iputils-ping
RUN apt install -y telnet
RUN apt install -y unzip
RUN apt install -y vim
RUN apt install -y zip
RUN apt install -y build-essential libssl-dev zlib1g-dev
RUN apt install -y libncurses5-dev libsqlite3-dev
RUN apt install -y libgdbm-dev libdb5.3-dev
RUN apt install -y libreadline-dev libffi-dev libbz2-dev liblzma-dev
RUN apt install -y lsof


# 安装Python
ARG PYTHON_VER=3.11.0
ARG PYTHON_MAIN_VER=3.11
RUN wget -c "https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/drivers/Python-${PYTHON_VER}.tgz" -O /tmp/Python-$PYTHON_VER.tgz && \
	cd /tmp && tar xvf Python-$PYTHON_VER.tgz && cd Python-$PYTHON_VER && \
	./configure --prefix=/usr/local/python${PYTHON_MAIN_VER} --enable-optimizations &&  make -j$(nproc) && make altinstall
RUN echo 'export PATH=/usr/local/python${PYTHON_MAIN_VER}/bin:$PATH' >> ~/.bashrc && \
    ln -s /usr/local/python${PYTHON_MAIN_VER}/bin/python${PYTHON_MAIN_VER} /bin/python && \
    ln -s /usr/local/python${PYTHON_MAIN_VER}/bin/pip${PYTHON_MAIN_VER} /bin/pip && \
	rm -rf /tmp/Python-$PYTHON_VER /tmp/Python-$PYTHON_VER.tgz

# 安装中文字体
RUN apt-get update && \
    apt-get install -y language-pack-zh-hans xfonts-utils fontconfig && \
    locale-gen zh_CN.UTF-8 && \
    locale-gen && \
    wget https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/drivers/fonts.zip && unzip fonts.zip && cp -r fonts/* /usr/share/fonts/ && \
    rm -f fonts.zip && \
    cd /usr/share/fonts/ && mkfontscale && mkfontdir && fc-cache && fc-list :lang=zh

# 设置PATH环境变量 \
ENV PYTHON_MAIN_VER=$PYTHON_MAIN_VER
ENV PATH=/usr/local/python${PYTHON_MAIN_VER}/bin:$PATH