FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot:latest

ENV APP_NAME yiya-ai-bot
ENV APP_HOME /root

WORKDIR $APP_HOME/$APP_NAME

# 安装依赖
RUN pip install \
        pdf2image==1.17.0 \
        orjson==3.10.18 \
        pymysql==1.1.1 \
        celery==5.5.3 \
        redis==6.2.0 \
        pydantic-settings==2.10.1 && \
    apt update && apt-get install -y poppler-utils unrtf

COPY $APP_NAME.tgz $APP_HOME/$APP_NAME/target/

# 复制最新的配置文件，覆盖基础镜像中的旧配置
COPY environment/common/app/conf/circus.ini $APP_HOME/$APP_NAME/conf/circus.ini

# 复制修改后的appctl.sh，覆盖基础镜像中的旧版本
COPY environment/common/app/bin/appctl.sh $APP_HOME/$APP_NAME/bin/appctl.sh

# 复制 Celery 实时监控脚本
COPY environment/common/app/bin/celery_realtime_monitor.sh $APP_HOME/$APP_NAME/bin/celery_realtime_monitor.sh

# 设置执行权限
RUN chmod +x $APP_HOME/$APP_NAME/bin/appctl.sh && \
    chmod +x $APP_HOME/$APP_NAME/bin/celery_realtime_monitor.sh

# 启动应用
CMD ["/bin/bash", "-c", "/root/start.sh"]