# From ubuntu:22.04
FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/open/python:3.11.0_202501072003

ENV APP_NAME yiya-ai-bot
ENV APP_HOME /root

COPY deb-sources.list /etc/apt/sources.list

# 构建时要做的事，一般是执行shell命令，例如用来安装必要软件，创建文件（夹），修改文件

RUN mkdir -p $APP_HOME/$APP_NAME/bin && \
    mkdir -p $APP_HOME/$APP_NAME/conf && \
    mkdir -p $APP_HOME/$APP_NAME/target && \
    mkdir -p $APP_HOME/logs/app && \
    mkdir -p $APP_HOME/logs/supervisord

#预装OpenGL库
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y unar && \
    apt-get install -y libgl1-mesa-glx && \
    apt-get install -y libglib2.0-0 && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y unar && \
    rm -rf /var/lib/apt/lists/*




ENV LANG=zh_CN.UTF-8
ENV LANGUAGE=zh_CN:zh

# 将应用启动脚本、健康检查脚本、nginx配置文件复制到镜像中
COPY environment/common/app/conf/requirements.txt /home/<USER>/$APP_NAME/requirements.txt

# 安装依赖
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --upgrade pip && pip install -r /home/<USER>/$APP_NAME/requirements.txt
#RUN pip install --pre torch --index-url https://download.pytorch.org/whl/nightly/cu124

RUN pip install circus

RUN pip install opencv-python
RUN pip install markdown-it-py==3.0.0 markdownify==0.14.1 markitdown==0.0.1a3
RUN pip install python-docx
RUN pip install PyMuPDF
RUN pip install patool


ENV PATH "$PATH:$APP_HOME/.local/bin:/usr/local/python${PYTHON_MAIN_VER}/bin"

COPY environment/common/app/ $APP_HOME/$APP_NAME/
COPY environment/common/app/conf/circus.ini $APP_HOME/$APP_NAME/conf/circus.ini

RUN echo "$APP_HOME/$APP_NAME/bin/appctl.sh start" >> $APP_HOME/start.sh && \
    echo "$APP_HOME/$APP_NAME/bin/preload.sh" >> $APP_HOME/health.sh

# 设置文件操作权限
RUN chmod -R a+x ${APP_HOME}/$APP_NAME/bin/ && \
chmod +x ${APP_HOME}/*.sh

WORKDIR $APP_HOME/$APP_NAME

COPY $APP_NAME.tgz $APP_HOME/$APP_NAME/target/

# 启动应用
CMD ["/bin/bash", "-c", "/root/start.sh"]

