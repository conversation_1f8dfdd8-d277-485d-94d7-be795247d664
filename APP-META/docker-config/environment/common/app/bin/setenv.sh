# app
# set ${APP_NAME}, if empty $(basename "${APP_HOME}") will be used.
APP_NAME=

# os env
export LANG=zh_CN.UTF-8
export NLS_LANG=AMERICAN_AMERICA.ZHS16GBK
export PATH=${APP_HOME}/bin:$PATH
CPU_COUNT=$SIGMA_MAX_PROCESSORS_LIMIT
if [ ! -n "$CPU_COUNT" ];then
    CPU_COUNT=$(grep -c 'cpu[0-9][0-9]*' /proc/stat);
fi
export CPU_COUNT
ulimit -c unlimited

# if set to "1", skip start nginx.
test -z "$NGINX_SKIP" && NGINX_SKIP=0

STATUS_PORT=80

# env check and calculate
if [ -z "$APP_NAME" ]; then
        APP_NAME=$(basename "${APP_HOME}")
fi

export PYTHONPATH=${APP_HOME}/.venv/lib/python3.11/site-packages:${APP_HOME}/target/${APP_NAME}/src:$PYTHONPATH

CIRCUS_LOG="${APP_HOME}/logs/circus.log"

CIRCUS_PID="${APP_HOME}/logs/circus.pid"
