#!/bin/bash

# Celery 实时监控脚本 - 轻量级版本
# 专门解决 Redis 连接断开和 Worker 无响应问题

APP_HOME="/root/yiya-ai-bot"
MONITOR_LOG="${APP_HOME}/logs/celery_realtime_monitor.log"
CHECK_INTERVAL=30  # 30秒检查一次，快速发现问题
FAILURE_THRESHOLD=2  # 连续失败2次就重启（1分钟内发现问题）
FAILURE_COUNT=0

# 创建日志目录
mkdir -p "${APP_HOME}/logs"

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "${MONITOR_LOG}"
}

# 核心检查：Worker是否能响应
check_worker_responsive() {
    log_message "🔍 检查 Worker 响应性..."
    
    # 快速检查：是否有注册的workers
    local workers=$(timeout 8 bash -c "cd ${APP_HOME}/target/yiya-ai-bot && python3.11 -c 'from celery_task import celery_app; workers = celery_app.control.inspect().registered(); print(\"OK\" if workers and workers != {} else \"FAIL\")'" 2>/dev/null)
    
    if [ "$workers" = "OK" ]; then
        log_message "✅ Worker 响应正常"
        return 0
    else
        log_message "❌ Worker 无响应或未注册"
        return 1
    fi
}

# 快速重启 Celery Worker
restart_celery_worker() {
    log_message "🔄 快速重启 Celery Worker..."
    
    # 使用 circusctl 重启
    if command -v circusctl >/dev/null 2>&1; then
        circusctl restart yiya-ai-bot-celery
        if [ $? -eq 0 ]; then
            log_message "✅ Worker 重启成功"
            FAILURE_COUNT=0
            return 0
        else
            log_message "❌ circusctl 重启失败"
        fi
    fi
    
    # 备用方案：手动重启
    log_message "⚠️ 使用备用重启方案..."
    pkill -f "celery.*worker"
    sleep 3
    log_message "✅ 已发送重启信号，等待 Circus 自动拉起"
    FAILURE_COUNT=0
    return 0
}

# 主监控循环
monitor_loop() {
    log_message "=== Celery 实时监控启动 (每${CHECK_INTERVAL}秒检查) ==="
    
    while true; do
        # 检查 Worker 响应性
        if check_worker_responsive; then
            # 检查通过，重置失败计数
            if [ $FAILURE_COUNT -gt 0 ]; then
                log_message "🎉 服务恢复正常，重置失败计数"
                FAILURE_COUNT=0
            fi
        else
            # 检查失败，增加失败计数
            FAILURE_COUNT=$((FAILURE_COUNT + 1))
            log_message "⚠️ 检查失败 ($FAILURE_COUNT/$FAILURE_THRESHOLD)"
            
            # 达到阈值，立即重启
            if [ $FAILURE_COUNT -ge $FAILURE_THRESHOLD ]; then
                log_message "🚨 连续失败 $FAILURE_COUNT 次，立即重启 Worker"
                restart_celery_worker
                
                # 重启后等待稍长时间让服务稳定
                sleep 30
                continue
            fi
        fi
        
        # 等待下次检查
        sleep $CHECK_INTERVAL
    done
}

# 单次检查模式（用于调试）
single_check() {
    log_message "🔍 执行单次检查..."
    check_worker_responsive
    local result=$?
    if [ $result -eq 0 ]; then
        log_message "✅ 单次检查通过"
    else
        log_message "❌ 单次检查失败"
    fi
    exit $result
}

# 主函数
main() {
    case "${1:-monitor}" in
        "check")
            single_check
            ;;
        "monitor"|*)
            # 检查是否在容器环境
            if [ -f /.dockerenv ] || [ -n "$KUBERNETES_SERVICE_HOST" ]; then
                log_message "🐳 容器环境，启动实时监控..."
                monitor_loop
            else
                log_message "💻 本地环境，执行单次检查..."
                single_check
            fi
            ;;
    esac
}

# 信号处理
trap 'log_message "收到停止信号，退出监控..."; exit 0' TERM INT

# 执行主函数
main "$@"
