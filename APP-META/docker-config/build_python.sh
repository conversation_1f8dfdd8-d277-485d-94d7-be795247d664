
NAMESPACE=open
APP_NAME=python
# 压缩文件
# 切换到上上层目录，并保存目录到变量
build_dir=$(cd $(dirname $0); pwd)
app_dir=$(cd $(dirname $0)/../..; pwd)
app=${APP_NAME}
version=$(date "+%Y%m%d%H%M")
dockerreg=yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com

cd ${build_dir}

# Docker构建
docker build --platform=linux/amd64 -f Dockerfile_python -t ${dockerreg}/${NAMESPACE}/${app}:${version} .

# 上传镜像
docker push ${dockerreg}/${NAMESPACE}/${app}:${version}
