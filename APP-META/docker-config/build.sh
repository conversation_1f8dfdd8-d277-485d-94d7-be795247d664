
APP_NAME=yiya-ai-bot
# 压缩文件
# 切换到上上层目录，并保存目录到变量
build_dir=$(cd $(dirname $0); pwd)
app_dir=$(cd $(dirname $0)/../..; pwd)
app=${APP_NAME}
version=$(date "+%Y%m%d%H%M")
dockerreg=yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com

cd $app_dir

tar -zcvf ${build_dir}/${APP_NAME}.tgz \
  --exclude='APP-META' \
  --exclude='logs' \
  --exclude='.venv' \
  --exclude='model' \
  --exclude='nacos-data' \
  --exclude='.git' \
  --exclude='.idea' \
  --exclude='.env' \
  *

cd ${build_dir}

# Docker构建
docker build --platform=linux/amd64 -f Dockerfile_base -t ${dockerreg}/yiya/${app}:${version} .

# 上传镜像
docker push ${dockerreg}/yiya/${app}:${version}

rm -rf ${build_dir}/${APP_NAME}.tgz
