# Celery Worker 可靠性优化说明

## 🎯 优化目标

解决 Celery Worker 在 K8s 环境中偶尔停止工作的问题，提高服务可用性。

## 🔧 优化内容

### 1. Circus 进程管理优化

**文件**: `APP-META/docker-config/environment/common/app/conf/circus.ini`

**优化点**:
- ✅ **自动重启**: `autorestart=true` - Worker 异常退出时自动重启
- ✅ **重启延迟**: `restart_delay=15` - 避免频繁重启
- ✅ **进程老化**: `max_age=7200` - 每2小时自动重启，防止内存泄漏
- ✅ **抖动检测**: `flapping_attempts=3` - 防止无限重启循环
- ✅ **任务限制**: `--max-tasks-per-child=1000` - 处理1000个任务后重启worker
- ✅ **超时控制**: `--time-limit=300` - 单个任务最大执行时间5分钟

### 2. 健康监控系统

**文件**: `APP-META/docker-config/environment/common/app/bin/celery_health_monitor.sh`

**功能**:
- 🔍 **Redis连接检查**: 每60秒检查Redis连接状态
- 🔍 **Worker状态检查**: 检查是否有注册的worker
- 🧪 **功能测试**: 发送测试任务验证worker响应性
- 🔄 **自动恢复**: 连续失败3次后自动重启worker
- 📝 **详细日志**: 记录所有检查和恢复操作

### 3. 日志监控增强

**文件**: `APP-META/docker-config/environment/common/app/bin/appctl.sh`

**改进**:
- 📊 **多文件监控**: 同时监控 application.log, error.log, celery.log, celery_error.log
- 🕒 **时间戳**: 为每行日志添加时间戳
- 🐳 **容器友好**: 输出到 stdout，便于 kubectl logs 查看

## 🚀 部署方式

由于项目使用阿里云 Codeup CI/CD 流水线自动部署，优化会在下次代码提交时自动生效：

```bash
git add .
git commit -m "feat: 优化 Celery Worker 可靠性"
git push origin main
```

流水线会自动：
1. 构建包含优化配置的新镜像
2. 部署到 K8s 集群
3. 启动增强的监控和自动恢复机制

## 📊 监控和排查

### 查看 Celery 健康状态
```bash
# 查看健康监控日志
kubectl exec -it <pod-name> -- tail -f /root/yiya-ai-bot/logs/celery_health.log

# 查看 Celery Worker 日志
kubectl exec -it <pod-name> -- tail -f /root/yiya-ai-bot/logs/celery.log

# 检查进程状态
kubectl exec -it <pod-name> -- circusctl status
```

### 手动重启 Celery Worker
```bash
# 进入容器
kubectl exec -it <pod-name> -- bash

# 重启 Celery Worker
circusctl restart yiya-ai-bot-celery

# 查看重启后状态
circusctl status
```

### 验证 Worker 功能
```bash
# 进入容器并测试
kubectl exec -it <pod-name> -- bash
cd /root/yiya-ai-bot/target/yiya-ai-bot

# 检查注册的 workers
python3.11 -c "from celery_task import celery_app; print(celery_app.control.inspect().registered())"

# 检查活跃的队列
python3.11 -c "from celery_task import celery_app; print(celery_app.control.inspect().active_queues())"
```

## 🔍 故障排查指南

### 问题1: Worker 无响应
**症状**: 健康检查显示 "Celery Workers 状态异常或无响应"
**解决**: 健康监控会自动重启，也可手动执行 `circusctl restart yiya-ai-bot-celery`

### 问题2: Redis 连接失败
**症状**: 健康检查显示 "Redis 连接失败"
**排查**: 
1. 检查网络连接
2. 验证 Redis 服务状态
3. 确认密码和端口配置

### 问题3: 任务堆积
**症状**: 任务发送成功但长时间 PENDING
**排查**:
1. 检查 worker 是否在正确的队列上监听
2. 确认任务路由配置
3. 查看 worker 并发数是否足够

## 📈 预期效果

1. **自动恢复**: Worker 异常时自动重启，无需人工干预
2. **预防性维护**: 定期重启防止内存泄漏和连接问题
3. **快速发现**: 60秒内发现并开始处理问题
4. **详细监控**: 完整的日志记录便于问题追踪
5. **提高可用性**: 从偶尔故障到持续稳定运行

## ⚠️ 注意事项

1. **资源消耗**: 健康监控会增加少量 CPU 和内存使用
2. **重启影响**: 自动重启可能中断正在执行的长任务
3. **日志大小**: 增强的日志记录会占用更多磁盘空间
4. **测试任务**: 健康检查会定期发送测试任务，这是正常行为

## 🎉 总结

通过这些优化，Celery Worker 的可靠性将大大提升，你不再需要手动检查和重启 worker。系统会自动监控、诊断和恢复，确保 Excel 导入等异步任务的稳定运行。
