apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: paddle-ocr
  name: paddle-ocr
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: paddle-ocr
  template:
    metadata:
      labels:
        app: paddle-ocr
    spec:
      containers:
        - env:
            - name: CUDA_VISIBLE_DEVICES
              value: '0'
          image: yiya-acr-registry-vpc.cn-hangzhou.cr.aliyuncs.com/open/yiya-ocr-base:202412231725
          imagePullPolicy: IfNotPresent
          name: paddle-ocr
          ports:
            - containerPort: 8866
              protocol: TCP
          resources:
            limits:
              cpu: '4'
              memory: 6000Mi
              aliyun.com/gpu-mem: "6"
            requests:
              cpu: '2'
              memory: 4000Mi
          volumeMounts:
            - mountPath: /root/.paddlehub
              name: volume-1734938225090
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: yiya-acr-key
      restartPolicy: Always
      volumes:
        - name: volume-1734938225090
          persistentVolumeClaim:
            claimName: yiya-ai-bot-paddlehub-pvc
