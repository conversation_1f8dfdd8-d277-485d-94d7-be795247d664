---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: yiya-ai-bot
  name: yiya-ai-bot
  namespace: yiya-app
spec:
  replicas: 2
  # 滚动更新策略 - 解决端口冲突问题
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1  # 最多1个Pod不可用
      maxSurge: 0        # 不允许超出期望副本数，避免端口冲突
  selector:
    matchLabels:
      app: yiya-ai-bot
  template:
    metadata:
      labels:
        app: yiya-ai-bot
    spec:
      # 优雅停止时间
      terminationGracePeriodSeconds: 60
      
      containers:
        - name: yiya-ai-bot
          image: yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot:latest
          imagePullPolicy: IfNotPresent
          
          ports:
            - containerPort: 7860
              protocol: TCP
              name: http
          
          # 环境变量
          env:
            - name: PORT
              value: "7860"
            - name: WORKERS
              value: "4"
          
          # 资源限制
          resources:
            limits:
              cpu: '4'
              memory: 8000Mi
            requests:
              cpu: '2'
              memory: 4000Mi
          
          # 健康检查 - 解决启动时端口冲突问题
          livenessProbe:
            httpGet:
              path: /health
              port: 7860
            initialDelaySeconds: 60    # 给足够时间启动
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
            successThreshold: 1
          
          readinessProbe:
            httpGet:
              path: /health
              port: 7860
            initialDelaySeconds: 30    # 启动后30秒开始检查
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
            successThreshold: 1
          
          # 启动探针 - 给应用更多时间启动
          startupProbe:
            httpGet:
              path: /health
              port: 7860
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 12       # 最多等待2分钟启动
            successThreshold: 1
          
          # 优雅停止
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - -c
                  - |
                    echo "开始优雅停止..."
                    # 停止circus管理的进程
                    circusctl quit 2>/dev/null || true
                    # 等待进程停止
                    sleep 5
                    # 强制清理可能残留的进程
                    pkill -f "gunicorn.*:7860" 2>/dev/null || true
                    pkill -f "uvicorn.*:7860" 2>/dev/null || true
                    echo "优雅停止完成"
          
          # 卷挂载
          volumeMounts:
            - name: logs
              mountPath: /root/yiya-ai-bot/logs
            - name: tmp
              mountPath: /tmp
      
      # 卷定义
      volumes:
        - name: logs
          emptyDir: {}
        - name: tmp
          emptyDir: {}
      
      # DNS策略
      dnsPolicy: ClusterFirst
      
      # 镜像拉取密钥
      imagePullSecrets:
        - name: yiya-acr-pub-key
      
      # 重启策略
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: yiya-ai-bot
  namespace: yiya-app
spec:
  type: ClusterIP
  ports:
  - port: 7860
    protocol: TCP
    targetPort: 7860
    name: http
  selector:
    app: yiya-ai-bot

---
# 可选：Pod反亲和性，避免多个Pod调度到同一节点
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yiya-ai-bot-anti-affinity
  namespace: yiya-app
spec:
  template:
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - yiya-ai-bot
              topologyKey: kubernetes.io/hostname
