FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/open/python:202412221902


RUN apt-get install -y libglib2.0-dev libgl1-mesa-glx

ARG PYTHON_VER=3.11.0
ARG PYTHON_MAIN_VER=3.11
ENV PATH="/usr/local/python${PYTHON_MAIN_VER}/bin:${PATH}"

#RUN hub install chinese_ocr_db_crnn_server
#CMD ["hub", "serving", "start", "-m", "chinese_ocr_db_crnn_server"]

CMD ["sh", "-c", "hub install chinese_ocr_db_crnn_server && hub serving start -m chinese_ocr_db_crnn_server"]