#!/usr/bin/env python3
"""
测试完整场景：模拟你的实际数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.monitor_report_utils import fill_medical_report_excel

def test_complete_scenario():
    """测试完整场景"""
    
    # 模拟你的实际输入数据
    report_data = {
        "rule1_no_matching_data": [
            {
                "受试者编号": 1071,
                "不良事件名称_aeterm": "消化道出血",
                "开始日期_aestdat": "2024-04-16"
            },
            {
                "受试者编号": 10012,
                "不良事件名称_aeterm": "发热",
                "开始日期_aestdat": "2024-08-24"
            },
            {
                "受试者编号": 11008,
                "不良事件名称_aeterm": "社区获得性肺炎",
                "开始日期_aestdat": "2024-05-27"
            },
            {
                "受试者编号": 38018,
                "不良事件名称_aeterm": "肝性脑病",
                "开始日期_aestdat": "2024-04-09"
            },
            {
                "受试者编号": 47006,
                "不良事件名称_aeterm": "重症自发腹膜炎",
                "开始日期_aestdat": "2024-04-12"
            },
            {
                "受试者编号": 47006,
                "不良事件名称_aeterm": "自发腹膜炎",
                "开始日期_aestdat": "2024-04-02"
            }
        ],
        "rule2_duplicate_records": [],
        "rule3_invalid_dates": [],
        "rule5_non_compliant_aeout": [],
        "rule7_unreasonable_treatments": [
            {
                "受试者编号": "11008",
                "不良事件名称_aeterm": "社区获得性肺炎",
                "开始日期_aestdat": "2024-05-27",  # 添加这个字段
                "是否有药物或非药物治疗_aetrea": "合并药物治疗",
                "治疗手段": "药物名称：维生素B1片 用药开始时间：2024-06-09 给药原因：不良事件",
                "ctcae分级_aectcae": "2级",
                "当前治疗是否合理": "不合理",
                "判断依据": "系统显示当前使用的药物为维生素B1片，自2024-06-09开始用于治疗社区获得性肺炎。根据知识库1中'感染和传染性疾病-肺感染'的CTCAE分级描述，2级社区获得性肺炎属于中度症状，需要口服药物治疗，且明确推荐使用抗生素、抗真菌或抗病毒药物。知识库2中'肺炎'的分级描述也指出2级需要治疗。因此，该不良事件在CTCAE 2级情况下应当进行药物治疗。然而，维生素B1属于维生素类营养素，主要用于营养支持或代谢辅助，并不具备抗感染作用，不属于肺炎的推荐治疗药物，无法针对病原体或控制肺部感染。因此，尽管患者接受了'药物治疗'，但所用药物与社区获得性肺炎的规范治疗方案不符。"
            }
        ],
        "rule8_invalid_sae": []
    }
    
    print("=" * 80)
    print("测试完整场景 - 包含开始日期的规则7数据")
    print("=" * 80)
    
    # 测试处理规则7数据
    from utils.monitor_report_utils import process_rule7_data
    rule7_result = process_rule7_data(report_data["rule7_unreasonable_treatments"])
    
    print("规则7处理结果:")
    for item in rule7_result:
        print(f"受试者编号: {item['受试者编号']}")
        print(f"不良事件名称: {item['不良事件名称']}")
        print(f"开始日期: {item['开始日期']}")
        print(f"描述: {item['描述'][:100]}...")
        print()
    
    print("=" * 80)
    print("测试没有开始日期的规则7数据")
    print("=" * 80)
    
    # 测试没有开始日期的情况
    rule7_data_no_date = [
        {
            "受试者编号": "11008",
            "不良事件名称_aeterm": "社区获得性肺炎",
            # 注意：这里没有 "开始日期_aestdat" 字段
            "是否有药物或非药物治疗_aetrea": "合并药物治疗",
            "治疗手段": "药物名称：维生素B1片 用药开始时间：2024-06-09 给药原因：不良事件",
            "ctcae分级_aectcae": "2级",
            "当前治疗是否合理": "不合理",
            "判断依据": "系统显示当前使用的药物为维生素B1片，自2024-06-09开始用于治疗社区获得性肺炎。"
        }
    ]
    
    rule7_result_no_date = process_rule7_data(rule7_data_no_date)
    
    print("规则7处理结果（没有开始日期）:")
    for item in rule7_result_no_date:
        print(f"受试者编号: {item['受试者编号']}")
        print(f"不良事件名称: {item['不良事件名称']}")
        print(f"开始日期: {item['开始日期']}")
        print(f"描述: {item['描述'][:100]}...")
        print()

if __name__ == "__main__":
    test_complete_scenario()
