# celery_task/excel_import_task.py

import io
import os
import time
import requests
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any
from celery import Task

from celery_task.celery import celery_app
from logger.logger import app_logger
from utils.monitor_excel2sql_utils import get_mysql_config, excel_to_mysql_parallel
from config.settings import settings


def safe_update_state(task_instance, state, meta, max_retries=3):
    """
    安全的状态更新函数，带重试机制
    """
    for attempt in range(max_retries):
        try:
            task_instance.update_state(state=state, meta=meta)
            return True
        except Exception as e:
            app_logger.warning(f"状态更新失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                app_logger.error(f"状态更新最终失败，跳过此次更新")
                return False
            time.sleep(1)  # 等待1秒后重试
    return False


@celery_app.task(
    name="import_excel_task",
    bind=True,
    autoretry_for=(ConnectionError, TimeoutError),
    retry_kwargs={'max_retries': 2, 'countdown': 60}
)
def import_excel_task(self: Task, excel_url: str, sheets_to_import: List[str]) -> Dict[str, Any]:
    """
    Excel 导入任务：
    1. 下载 Excel 文件
    2. 导入到 MySQL 数据库
    3. 返回导入结果
    """
    task_id = self.request.id
    app_logger.info(f"开始执行 Excel 导入任务，Task ID: {task_id}")
    
    start_time = time.time()
    
    # 初始化任务状态
    task_info = {
        "task_id": task_id,
        "excel_url": excel_url,
        "sheets_to_import": sheets_to_import,
        "current_stage": "初始化",
        "progress": 0,
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    try:
        # 更新状态：开始下载
        task_info.update({
            "current_stage": "下载 Excel 文件",
            "progress": 10,
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        safe_update_state(self, "PROGRESS", task_info)
        
        # 1. 下载 Excel 文件
        app_logger.info(f"正在从URL下载文件: {excel_url}")
        response = requests.get(excel_url, timeout=300)
        response.raise_for_status()
        excel_stream = io.BytesIO(response.content)
        app_logger.info("Excel 文件下载成功")
        
        # 更新状态：获取数据库配置
        task_info.update({
            "current_stage": "获取数据库配置",
            "progress": 20,
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        safe_update_state(self, "PROGRESS", task_info)
        
        # 2. 获取数据库配置
        mysql_config = get_mysql_config()
        app_logger.info(f"获取到数据库配置: {mysql_config['database']}")
        
        # 更新状态：导入数据到数据库
        task_info.update({
            "current_stage": "导入数据到数据库",
            "progress": 50,
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        safe_update_state(self, "PROGRESS", task_info)
        
        # 3. 执行数据库导入
        app_logger.info(f"开始导入 {len(sheets_to_import)} 个 Sheet 到数据库")
        import_results = excel_to_mysql_parallel(
            excel_stream,
            mysql_config,
            sheets_to_import=sheets_to_import,
            max_workers=2
        )
        
        # 统计导入结果
        successful_imports = [r for r in import_results if r.get('status') == 'success']
        failed_imports = [r for r in import_results if r.get('status') == 'error']

        app_logger.info(f"数据库导入完成，成功: {len(successful_imports)}, 失败: {len(failed_imports)}")
        
        # 计算总耗时
        total_time = time.time() - start_time
        
        # 最终结果
        final_result = {
            "task_id": task_id,
            "status": "success",
            "progress": 100,
            "current_stage": "完成",
            "excel_url": excel_url,
            "sheets_to_import": sheets_to_import,
            "import_results": import_results,
            "successful_imports": len(successful_imports),
            "failed_imports": len(failed_imports),
            "total_time_seconds": round(total_time, 2),
            "created_at": task_info["created_at"],
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "completed_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        app_logger.info(f"Excel 导入任务完成，Task ID: {task_id}, 耗时: {total_time:.2f}秒")
        return final_result
        
    except requests.exceptions.RequestException as e:
        error_msg = f"下载 Excel 文件失败: {str(e)}"
        app_logger.error(error_msg)
        raise Exception(error_msg)
        
    except ValueError as e:
        error_msg = f"Excel 文件处理失败 (可能是 Sheet 不存在): {str(e)}"
        app_logger.error(error_msg)
        raise Exception(error_msg)
        
    except Exception as e:
        error_msg = f"Excel 导入任务执行失败: {str(e)}"
        app_logger.error(error_msg, exc_info=True)
        raise Exception(error_msg)
