from celery import Celery
from celery.signals import celeryd_init
import os

# 创建 Celery 应用实例
# 只包含 Excel 导入任务
celery_app = Celery("excel-import-app", include=['celery_task.excel_import_task'])

# 统一使用同一个配置文件（本地和生产环境都用 Redis db1）
celery_app.config_from_object("celery_task.celeryconfig")
print("加载 Celery 配置")


# 信号处理器：在 Worker 启动后加载 Nacos 配置
@celeryd_init.connect
def init_worker(**kwargs):
    import time
    from logger.logger import app_logger
    from config.nacos_config import nacos_config

    # 检查是否应该在 Celery Worker 中启用 Nacos
    if not nacos_config.should_enable_in_celery():
        app_logger.info("Nacos 在 Celery Worker 中已被禁用，跳过加载")
        return

    max_retries = nacos_config.max_retries
    retry_delay = nacos_config.retry_delay

    for attempt in range(max_retries):
        try:
            from configurer.yy_nacos import Nacos

            app_logger.info(f"开始加载 Nacos 配置 (尝试 {attempt + 1}/{max_retries})")
            Nacos()
            app_logger.info("Nacos 配置加载完毕")
            return  # 成功加载，退出重试循环

        except Exception as e:
            app_logger.error(f"Nacos 配置加载失败 (尝试 {attempt + 1}/{max_retries}): {e}")

            if attempt < max_retries - 1:
                app_logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                app_logger.warning("Nacos 配置加载最终失败，Worker 将在没有 Nacos 配置的情况下启动")
                app_logger.warning("某些依赖 Nacos 配置的功能可能不可用")
