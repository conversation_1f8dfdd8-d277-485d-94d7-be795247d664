import time
from enum import Enum
import contextvars
import json
from typing import Any, Callable
from functools import wraps
from asyncio.coroutines import iscoroutinefunction
from fastapi.encoders import jsonable_encoder

from .logger import biz_logger


class BizAction(Enum):
    mt = 'mt'


biz_log_context = contextvars.ContextVar('biz_log_context', default={})


def update_biz_log_context(**kwargs):
    biz_log_context.get().update(kwargs)


def clear_biz_log_context():
    biz_log_context.set({})


def to_json_str(obj: Any, indent=None):
    return json.dumps(jsonable_encoder(obj), indent=indent, ensure_ascii=False)


def biz_log(action: BizAction):
    def decorator(func: Callable):
        @wraps(func)
        def time_log_proxy(*args, **kwargs):
            # biz_log_context.set({})
            start = time.time()
            biz_log_context.get()['start_time'] = start
            result = None
            exception = None
            try:
                result = func(*args, **kwargs)
            except Exception as e:
                exception = e
                raise e
            finally:
                end = time.time()
                cost_time = end - start
                log(cost_time, action, args, kwargs, result, exception)
                clear_biz_log_context()
            return result

        @wraps(func)
        async def async_time_log_proxy(*args, **kwargs):
            clear_biz_log_context()
            start = time.time()
            biz_log_context.get()['start_time'] = start
            result = None
            exception = None
            try:
                result = func(*args, **kwargs)
            except Exception as e:
                exception = e
                raise e
            finally:
                end = time.time()
                cost_time = end - start
                log(cost_time, action, args, kwargs, result, exception)
            return result

        if iscoroutinefunction(func):
            return async_time_log_proxy
        return time_log_proxy

    return decorator


def log(cost_time: float, action: BizAction, args, kwargs, result, e: Exception | None = None):
    context = biz_log_context.get()
    _input = context.get('_input')
    if not _input:
        if kwargs:
            _input = kwargs
        else:
            _input = args

    if not isinstance(_input, str):
        _input = to_json_str(_input)

    output = context.get('output')
    if not output:
        output = result
    if not isinstance(output, str):
        output = to_json_str(output)
    else:
        output = to_json_str({
            "output": output
        })

    success = False if e else context.get("success", True)

    err = context.get('err')

    if not err:
        if e:
            err = str(e)

    message = "\u0001".join(
        [
            "{action}",
            "{success}",
            "{cost_time}",
            "{input}",
            "{output}",
            "{err}"
        ]
    )

    if isinstance(result, str):
        result = json.loads(result)

    if result is not None:
        result_dict = result if isinstance(result, dict) else result.__dict__
        success = get_attr('success', result_dict)

    if e:
        success = False

    biz_logger.info(
        message,
        action=action.value,
        cost_time=cost_time,
        success=success,
        input=_input,
        output=output,
        err=err,
    )

    clear_biz_log_context()


def get_attr(attr_name, obj):
    for k, v in obj.items():
        if k == attr_name:
            return v
        if isinstance(v, dict):
            return get_attr(attr_name, v)
    return None


if __name__ == '__main__':
    log(3000, BizAction.mt, {'src_lang': 'zh', 'tgt_lang': 'en', 'src_texts': ['你好']}, None, {'result': ['Hello']},
        None)
