import os
import contextvars
from loguru import logger

from config.env_config import LOG_HOME

request_id = contextvars.ContextVar('request_id', default="")


def get_request_id():
    return request_id.get()


def conf_logger():
    logger.info(os.environ)

    logger.add(
        os.path.join(LOG_HOME, f'app.log'),
        format='{time:YYYY-MM-DD HH:mm:ss} | {level} | {extra[request_id]} | {message}',
        level='INFO',
        rotation='00:00',
        retention='30 days',
        enqueue=True,
        encoding='utf-8',
        filter=lambda record: record['extra']['log_tag'] == 'app'
    )

    logger.add(
        os.path.join(LOG_HOME, f'biz.log'),
        format='\u0001'.join(
            [
                "{time:YYYY-MM-DD HH:mm:ss}",
                "{level}",
                "{extra[request_id]}",
                "{message}"
            ]
        ),
        level='INFO',
        rotation='00:00',
        retention='30 days',
        enqueue=True,
        encoding='utf-8',
        filter=lambda record: record['extra']['log_tag'] == 'biz'
    )

    app_logger = logger.bind(log_tag='app').patch(lambda record: record['extra'].update(request_id=get_request_id()))
    mt_logger = logger.bind(log_tag='biz').patch(lambda record: record['extra'].update(request_id=get_request_id()))
    return app_logger, mt_logger


app_logger, biz_logger = conf_logger()

if __name__ == '__main__':
    app_logger.info("Hello App")
    biz_logger.info("Hello MT")
