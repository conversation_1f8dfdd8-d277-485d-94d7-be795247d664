#!/usr/bin/env python3
"""
CPU核数与进程/线程关系分析
"""

import psutil
import os
import threading
import multiprocessing
import time
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor


def get_system_info():
    """获取系统CPU信息"""
    print("系统CPU信息分析")
    print("="*50)
    
    cpu_info = {
        "物理核心数": psutil.cpu_count(logical=False),
        "逻辑核心数": psutil.cpu_count(logical=True),
        "CPU频率": f"{psutil.cpu_freq().current:.0f}MHz" if psutil.cpu_freq() else "未知",
        "CPU使用率": f"{psutil.cpu_percent(interval=1):.1f}%"
    }
    
    for key, value in cpu_info.items():
        print(f"  {key}: {value}")
    
    # 超线程检测
    physical = cpu_info["物理核心数"]
    logical = cpu_info["逻辑核心数"]
    
    if logical > physical:
        print(f"  超线程: 启用 ({logical//physical}x)")
    else:
        print(f"  超线程: 未启用")
    
    return cpu_info


def explain_process_vs_thread_cpu_usage():
    """解释进程vs线程的CPU使用"""
    print("\n" + "="*60)
    print("进程 vs 线程的CPU利用分析")
    print("="*60)
    
    cpu_count = psutil.cpu_count(logical=False)
    logical_count = psutil.cpu_count(logical=True)
    
    scenarios = {
        "CPU密集型任务": {
            "多进程": {
                "描述": f"可以真正并行使用{cpu_count}个物理核心",
                "示例": "图像处理、数学计算、机器学习训练",
                "最佳配置": f"{cpu_count}个进程",
                "CPU利用率": "可达100% × 核心数"
            },
            "多线程": {
                "描述": "受GIL限制，只能使用1个核心",
                "示例": "同样的计算任务",
                "最佳配置": "1个线程（多线程无意义）",
                "CPU利用率": "最多100% × 1核心"
            }
        },
        "I/O密集型任务": {
            "多进程": {
                "描述": f"可以并行，但开销大",
                "示例": "文件读写、网络请求、数据库操作",
                "最佳配置": f"2-4个进程",
                "CPU利用率": "通常很低，大部分时间等待I/O"
            },
            "多线程": {
                "描述": "I/O等待时释放GIL，效率高",
                "示例": "Excel下载、数据库写入",
                "最佳配置": f"{logical_count * 2}个线程（可超过核心数）",
                "CPU利用率": "低，但吞吐量高"
            }
        }
    }
    
    for task_type, approaches in scenarios.items():
        print(f"\n{task_type}:")
        for approach, details in approaches.items():
            print(f"  {approach}:")
            for key, value in details.items():
                print(f"    {key}: {value}")


def demonstrate_cpu_usage():
    """演示不同配置的CPU使用"""
    print("\n" + "="*60)
    print("CPU使用演示")
    print("="*60)
    
    def cpu_intensive_task(duration=2):
        """CPU密集型任务"""
        end_time = time.time() + duration
        result = 0
        while time.time() < end_time:
            result += 1
        return result
    
    def io_intensive_task(duration=2):
        """I/O密集型任务"""
        time.sleep(duration)  # 模拟I/O等待
        return "I/O完成"
    
    print("选择演示类型:")
    print("1. CPU密集型任务对比")
    print("2. I/O密集型任务对比")
    print("3. 跳过演示")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        demonstrate_cpu_intensive()
    elif choice == "2":
        demonstrate_io_intensive()
    else:
        print("跳过演示")


def demonstrate_cpu_intensive():
    """演示CPU密集型任务"""
    print("\nCPU密集型任务演示...")
    
    def cpu_task():
        result = 0
        for i in range(1000000):
            result += i ** 0.5
        return result
    
    # 单线程
    print("测试单线程...")
    start_time = time.time()
    cpu_task()
    single_thread_time = time.time() - start_time
    
    # 多线程
    print("测试多线程...")
    start_time = time.time()
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(cpu_task) for _ in range(4)]
        for future in futures:
            future.result()
    multi_thread_time = time.time() - start_time
    
    # 多进程
    print("测试多进程...")
    start_time = time.time()
    with ProcessPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(cpu_task) for _ in range(4)]
        for future in futures:
            future.result()
    multi_process_time = time.time() - start_time
    
    print(f"\n结果对比:")
    print(f"  单线程: {single_thread_time:.2f}秒")
    print(f"  多线程: {multi_thread_time:.2f}秒 (加速比: {single_thread_time/multi_thread_time:.2f}x)")
    print(f"  多进程: {multi_process_time:.2f}秒 (加速比: {single_thread_time/multi_process_time:.2f}x)")


def demonstrate_io_intensive():
    """演示I/O密集型任务"""
    print("\nI/O密集型任务演示...")
    
    def io_task():
        time.sleep(0.5)  # 模拟I/O等待
        return "完成"
    
    # 单线程
    print("测试单线程...")
    start_time = time.time()
    for _ in range(4):
        io_task()
    single_thread_time = time.time() - start_time
    
    # 多线程
    print("测试多线程...")
    start_time = time.time()
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(io_task) for _ in range(4)]
        for future in futures:
            future.result()
    multi_thread_time = time.time() - start_time
    
    print(f"\n结果对比:")
    print(f"  单线程: {single_thread_time:.2f}秒")
    print(f"  多线程: {multi_thread_time:.2f}秒 (加速比: {single_thread_time/multi_thread_time:.2f}x)")


def analyze_celery_configuration():
    """分析Celery配置与CPU的关系"""
    print("\n" + "="*60)
    print("Celery配置与CPU关系分析")
    print("="*60)
    
    cpu_count = psutil.cpu_count(logical=False)
    logical_count = psutil.cpu_count(logical=True)
    
    print(f"你的系统: {cpu_count}物理核心, {logical_count}逻辑核心")
    
    configurations = {
        "当前配置": {
            "设置": "--pool=threads --concurrency=4",
            "进程数": 1,
            "工作线程数": 4,
            "适用": "I/O密集型Excel导入",
            "CPU利用": f"主要使用1核心，I/O时可切换到其他核心",
            "内存": "~80MB",
            "推荐": "✅ 最适合你的任务"
        },
        "高并发线程": {
            "设置": f"--pool=threads --concurrency={logical_count * 2}",
            "进程数": 1,
            "工作线程数": logical_count * 2,
            "适用": "更多I/O密集型任务",
            "CPU利用": f"可以更好利用{logical_count}个逻辑核心",
            "内存": "~120MB",
            "推荐": "⚠️ 如果需要更高并发可考虑"
        },
        "多进程配置": {
            "设置": f"--pool=prefork --concurrency={cpu_count}",
            "进程数": cpu_count,
            "工作线程数": cpu_count,
            "适用": "CPU密集型任务",
            "CPU利用": f"可以充分利用{cpu_count}个物理核心",
            "内存": f"~{cpu_count * 60}MB",
            "推荐": "❌ 不适合你的I/O密集型任务"
        }
    }
    
    for config_name, details in configurations.items():
        print(f"\n{config_name}:")
        for key, value in details.items():
            print(f"  {key}: {value}")


def main():
    """主函数"""
    print("CPU核数与进程/线程关系分析工具")
    print("="*60)
    
    # 获取系统信息
    cpu_info = get_system_info()
    
    # 解释进程vs线程的CPU使用
    explain_process_vs_thread_cpu_usage()
    
    # 分析Celery配置
    analyze_celery_configuration()
    
    # 演示CPU使用
    demonstrate_cpu_usage()
    
    print("\n" + "="*60)
    print("关键结论")
    print("="*60)
    print("1. 进程数 ≤ CPU物理核心数 (CPU密集型)")
    print("2. 线程数可以 > CPU核心数 (I/O密集型)")
    print("3. 你的Excel任务是I/O密集型，当前配置最优")
    print("4. 4个工作线程 + 16个系统线程 = 20个总线程是正常的")
    print("5. 系统线程大部分时间在等待，不消耗CPU")


if __name__ == "__main__":
    main()
