#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSONL转TXT格式工具
将缩略语JSONL文件转换为易读的TXT格式
"""

import json
import argparse
from typing import List, Dict


def load_jsonl(file_path: str) -> List[Dict]:
    """
    加载JSONL文件
    
    Args:
        file_path: JSONL文件路径
        
    Returns:
        包含所有条目的列表
    """
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:
                    try:
                        item = json.loads(line)
                        data.append(item)
                    except json.JSONDecodeError as e:
                        print(f"警告: 第{line_num}行JSON解析错误: {e}")
                        continue
    except FileNotFoundError:
        print(f"错误: 文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误: 读取文件时发生错误: {e}")
        return []
    
    return data


def convert_to_txt_format(data: List[Dict]) -> str:
    """
    将JSONL数据转换为TXT格式
    
    Args:
        data: JSONL数据列表
        
    Returns:
        格式化的TXT字符串
    """
    txt_lines = []
    
    for i, item in enumerate(data):
        abbreviation = item.get('abbreviation', '').strip()
        full_form = item.get('full_form', '').strip()
        definition_cn = item.get('definition_cn', '').strip()
        
        # 格式化每一行
        line = f"简称：{abbreviation} 全称：{full_form} 中文全称：{definition_cn}"
        txt_lines.append(line)
        
        # 除了最后一行，每行后面都加上分隔符
        if i < len(data) - 1:
            txt_lines.append("***")
    
    return '\n'.join(txt_lines)


def save_txt(content: str, file_path: str) -> bool:
    """
    保存TXT内容到文件
    
    Args:
        content: 要保存的文本内容
        file_path: 输出文件路径
        
    Returns:
        是否保存成功
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        print(f"错误: 保存文件时发生错误: {e}")
        return False


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='JSONL转TXT格式工具')
    parser.add_argument('-i', '--input', default='缩略语_去重.jsonl', 
                       help='输入的JSONL文件路径 (默认: 缩略语_去重.jsonl)')
    parser.add_argument('-o', '--output', default='缩略语.txt', 
                       help='输出的TXT文件路径 (默认: 缩略语.txt)')
    
    args = parser.parse_args()
    
    print(f"正在加载文件: {args.input}")
    data = load_jsonl(args.input)
    
    if not data:
        print("没有数据可处理")
        return
    
    print(f"加载了 {len(data)} 条记录")
    print("正在转换格式...")
    
    # 转换为TXT格式
    txt_content = convert_to_txt_format(data)
    
    # 保存TXT文件
    print(f"正在保存到: {args.output}")
    if save_txt(txt_content, args.output):
        print("转换完成！")
        print(f"已生成 {args.output} 文件")
        
        # 显示前几行作为预览
        lines = txt_content.split('\n')
        preview_lines = lines[:6] if len(lines) >= 6 else lines
        print("\n文件预览:")
        print("-" * 50)
        for line in preview_lines:
            print(line)
        if len(lines) > 6:
            print("...")
        print("-" * 50)
    else:
        print("转换失败!")


if __name__ == "__main__":
    main()
