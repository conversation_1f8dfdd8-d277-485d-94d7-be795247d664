#!/usr/bin/env python3
"""
监控Gunicorn和Celery的进程/线程状态
"""

import psutil
import time
from datetime import datetime


def get_process_tree():
    """获取进程树结构"""
    processes = {}
    
    for proc in psutil.process_iter(['pid', 'ppid', 'name', 'cmdline', 'memory_info', 'num_threads']):
        try:
            info = proc.info
            cmdline = ' '.join(info['cmdline']) if info['cmdline'] else ''
            
            # 识别相关进程
            if any(keyword in cmdline.lower() for keyword in ['gunicorn', 'celery', 'uvicorn']):
                processes[info['pid']] = {
                    'pid': info['pid'],
                    'ppid': info['ppid'],
                    'name': info['name'],
                    'cmdline': cmdline,
                    'memory_mb': info['memory_info'].rss / 1024 / 1024,
                    'num_threads': info['num_threads'],
                    'children': []
                }
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    # 构建父子关系
    for pid, proc_info in processes.items():
        ppid = proc_info['ppid']
        if ppid in processes:
            processes[ppid]['children'].append(pid)
    
    return processes


def print_process_tree(processes, pid=None, level=0, visited=None):
    """打印进程树"""
    if visited is None:
        visited = set()
    
    if pid is None:
        # 找到根进程
        root_pids = [p for p in processes.keys() 
                    if p not in visited and processes[p]['ppid'] not in processes]
        for root_pid in root_pids:
            print_process_tree(processes, root_pid, level, visited)
        return
    
    if pid in visited:
        return
    
    visited.add(pid)
    proc = processes[pid]
    
    indent = "  " * level
    process_type = identify_process_type(proc['cmdline'])
    
    print(f"{indent}├── PID {proc['pid']} - {process_type}")
    print(f"{indent}    内存: {proc['memory_mb']:.1f}MB | 线程数: {proc['num_threads']}")
    print(f"{indent}    命令: {proc['cmdline'][:80]}...")
    
    # 打印子进程
    for child_pid in proc['children']:
        if child_pid in processes:
            print_process_tree(processes, child_pid, level + 1, visited)


def identify_process_type(cmdline):
    """识别进程类型"""
    cmdline_lower = cmdline.lower()
    
    if 'gunicorn' in cmdline_lower and 'master' in cmdline_lower:
        return "Gunicorn Master"
    elif 'gunicorn' in cmdline_lower:
        return "Gunicorn Worker (FastAPI)"
    elif 'celery' in cmdline_lower and 'worker' in cmdline_lower:
        return "Celery Worker"
    elif 'uvicorn' in cmdline_lower:
        return "Uvicorn Worker"
    else:
        return "其他进程"


def monitor_performance():
    """监控性能指标"""
    print("\n" + "="*80)
    print("实时性能监控")
    print("="*80)
    
    while True:
        try:
            processes = get_process_tree()
            
            # 统计信息
            gunicorn_workers = []
            celery_workers = []
            total_memory = 0
            
            for pid, proc in processes.items():
                cmdline = proc['cmdline'].lower()
                total_memory += proc['memory_mb']
                
                if 'gunicorn' in cmdline and 'master' not in cmdline:
                    gunicorn_workers.append(proc)
                elif 'celery' in cmdline and 'worker' in cmdline:
                    celery_workers.append(proc)
            
            # 显示统计
            print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 系统状态:")
            print(f"  Gunicorn Workers: {len(gunicorn_workers)} 个进程")
            print(f"  Celery Workers: {len(celery_workers)} 个进程")
            print(f"  总内存使用: {total_memory:.1f}MB")
            
            if gunicorn_workers:
                avg_memory = sum(p['memory_mb'] for p in gunicorn_workers) / len(gunicorn_workers)
                total_threads = sum(p['num_threads'] for p in gunicorn_workers)
                print(f"  Gunicorn平均内存: {avg_memory:.1f}MB/进程")
                print(f"  Gunicorn总线程数: {total_threads}")
            
            if celery_workers:
                for worker in celery_workers:
                    print(f"  Celery Worker: {worker['memory_mb']:.1f}MB, {worker['num_threads']} 线程")
            
            time.sleep(5)
            
        except KeyboardInterrupt:
            print("\n监控已停止")
            break
        except Exception as e:
            print(f"监控出错: {e}")
            time.sleep(5)


def main():
    """主函数"""
    print("Gunicorn vs Celery 进程/线程监控工具")
    print("="*60)
    
    # 获取当前进程状态
    processes = get_process_tree()
    
    if not processes:
        print("未找到相关进程，请确保Gunicorn和Celery正在运行")
        return
    
    print("\n当前进程树结构:")
    print("-"*60)
    print_process_tree(processes)
    
    # 详细分析
    print("\n" + "="*60)
    print("架构分析")
    print("="*60)
    
    gunicorn_count = sum(1 for p in processes.values() 
                        if 'gunicorn' in p['cmdline'].lower() and 'master' not in p['cmdline'].lower())
    celery_count = sum(1 for p in processes.values() 
                      if 'celery' in p['cmdline'].lower() and 'worker' in p['cmdline'].lower())
    
    print(f"Gunicorn架构: {gunicorn_count} 个Worker进程 (多进程)")
    print(f"Celery架构: {celery_count} 个Worker进程 (单进程多线程)")
    
    if celery_count > 0:
        celery_proc = next(p for p in processes.values() 
                          if 'celery' in p['cmdline'].lower() and 'worker' in p['cmdline'].lower())
        print(f"Celery线程数: {celery_proc['num_threads']} 个线程")
    
    print("\n设计原因:")
    print("• Gunicorn多进程: Web服务需要高并发、故障隔离")
    print("• Celery单进程: I/O密集型任务，线程更高效")
    
    # 询问是否开始实时监控
    response = input("\n是否开始实时监控? [y/N]: ")
    if response.lower() in ['y', 'yes']:
        monitor_performance()


if __name__ == "__main__":
    main()
