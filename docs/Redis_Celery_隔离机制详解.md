# Redis + Celery 隔离机制详解

## 📚 基础概念

### Redis 数据库分区
Redis 默认提供 **16 个逻辑数据库**（db0 到 db15），它们：
- **共享同一个 Redis 实例** - 相同的内存、CPU、网络资源
- **逻辑隔离** - 不同 DB 之间的数据完全独立
- **相同配置** - 密码、超时等配置对所有 DB 生效

```bash
# 连接不同的数据库
redis-cli -h host -p 6379 -n 0  # 连接 db0
redis-cli -h host -p 6379 -n 1  # 连接 db1
redis-cli -h host -p 6379 -n 3  # 连接 db3
```

### Celery 架构
```
客户端 → Redis Broker → Celery Worker → Redis Result Backend
         (任务队列)      (任务处理)     (结果存储)
```

## 🗂️ 数据库隔离 (Database Isolation)

### 什么是数据库隔离？
将不同的应用或模块分配到不同的 Redis 数据库中，实现数据的物理隔离。

### 数据库用途分配

#### 约定俗成的分配方式：
```
db0  - 默认数据库，通常用于缓存
db1  - 会话存储 (Session)
db2  - 队列系统 (消息队列)
db3  - 任务结果存储
db4  - 临时数据
db5+ - 业务模块专用
```

#### 你的项目分配：
```
db0  - 未使用
db1  - 其他同事的项目 (有 Celery 数据)
db2  - 未使用
db3  - Excel 导入系统专用 ✅
db4+ - 预留给其他模块
```

### 配置示例：
```python
# config/settings.py
REDIS_DB: int = 3          # Broker 使用 db3
REDIS_TASK_DB: int = 3     # 结果存储也使用 db3

# 连接字符串
broker_url = "redis://:password@host:6379/3"
result_backend = "redis://:password@host:6379/3"
```

### 优点：
- ✅ **完全隔离** - 数据不会相互影响
- ✅ **独立清理** - 可以单独清空某个 DB
- ✅ **便于调试** - 数据边界清晰
- ✅ **安全性高** - 误操作影响范围有限

### 缺点：
- ❌ **资源共享** - 所有 DB 共享内存和 CPU
- ❌ **连接开销** - 切换 DB 有微小性能损失

## 🚦 队列隔离 (Queue Isolation)

### 什么是队列隔离？
在同一个 Redis 数据库中，使用不同的队列名称来隔离不同的任务类型。

### 队列的作用：
```
Redis DB
├── excel_import_queue     # Excel 导入任务队列
├── email_queue           # 邮件发送任务队列  
├── image_process_queue   # 图片处理任务队列
└── celery               # 默认队列
```

### 配置示例：
```python
# celery_task/celeryconfig.py
task_default_queue = 'excel_import_queue'
task_routes = {
    'import_excel_task': {'queue': 'excel_import_queue'},
    'send_email_task': {'queue': 'email_queue'},
}
```

### Worker 启动方式：
```bash
# 处理所有队列
celery -A celery_task worker

# 只处理特定队列
celery -A celery_task worker --queues=excel_import_queue

# 处理多个队列
celery -A celery_task worker --queues=excel_import_queue,email_queue
```

### 优点：
- ✅ **任务分类** - 不同类型任务分开处理
- ✅ **优先级控制** - 可以为不同队列分配不同的 Worker
- ✅ **扩展性好** - 可以独立扩展某类任务的处理能力

### 缺点：
- ❌ **配置复杂** - 需要管理多个队列
- ❌ **资源分配** - 需要合理分配 Worker 资源

## 🎯 任务隔离 (Task Isolation)

### 什么是任务隔离？
确保不同的任务类型有唯一的名称，避免任务被错误的 Worker 处理。

### 任务命名规范：
```python
# 好的命名方式
@celery_app.task(name="excel_import_task")
def import_excel_task():
    pass

@celery_app.task(name="email_send_task") 
def send_email_task():
    pass

# 避免的命名方式
@celery_app.task(name="task")  # 太通用
@celery_app.task(name="process")  # 容易冲突
```

### 任务路由：
```python
task_routes = {
    'excel_import_task': {
        'queue': 'excel_import_queue',
        'routing_key': 'excel.import',
    },
    'email_send_task': {
        'queue': 'email_queue', 
        'routing_key': 'email.send',
    },
}
```

### 优点：
- ✅ **避免冲突** - 任务不会被错误处理
- ✅ **清晰分工** - 每个 Worker 职责明确
- ✅ **便于监控** - 可以单独监控某类任务

## 💾 结果隔离 (Result Isolation)

### 什么是结果隔离？
将不同任务的执行结果存储在不同的位置或使用不同的前缀。

### 结果存储结构：
```
Redis DB3
├── celery-task-meta-{task_id}     # 任务结果
├── celery-taskset-meta-{group_id} # 任务组结果
└── _kombu.binding.{queue_name}    # 队列绑定信息
```

### 配置示例：
```python
# 基本配置
result_backend = "redis://:password@host:6379/3"

# 添加前缀隔离
result_backend_transport_options = {
    'global_keyprefix': 'excel_import_',  # 结果前缀
}

# 结果过期时间
result_expires = 3600  # 1小时后过期
```

### 结果 Key 示例：
```
# 无前缀
celery-task-meta-abc123-def456

# 有前缀
excel_import_celery-task-meta-abc123-def456
```

## ⏰ 结果存储时间管理

### 默认配置：
```python
# celery_task/celeryconfig.py
result_expires = 3600  # 结果保存 1 小时（秒）
```

### 不同场景的建议：
```python
# 开发环境 - 短期保存
result_expires = 1800  # 30 分钟

# 生产环境 - 中期保存  
result_expires = 3600  # 1 小时

# 重要任务 - 长期保存
result_expires = 86400  # 24 小时

# 永久保存（不推荐）
result_expires = None
```

### 手动清理：
```bash
# 清理过期结果
celery -A celery_task purge

# 清理特定任务结果
redis-cli DEL celery-task-meta-{task_id}
```

## 🏗️ 你的项目配置分析

### 当前配置：
```python
# 数据库隔离
REDIS_DB: int = 3  # 使用 db3，完全独立

# 队列隔离  
task_default_queue = 'excel_import_queue'
task_routes = {
    'import_excel_task': {'queue': 'excel_import_queue'},
}

# 任务隔离
@celery_app.task(name="import_excel_task", bind=True)
def import_excel_task():
    pass

# 结果隔离
result_backend = "redis://:password@47.98.45.40:6379/3"
result_expires = 3600  # 1小时过期
```

### 隔离级别：
```
🔒 数据库隔离: ✅ 完全隔离 (db3)
🔒 队列隔离:   ✅ 专用队列 (excel_import_queue)  
🔒 任务隔离:   ✅ 唯一任务名 (import_excel_task)
🔒 结果隔离:   ✅ 独立存储 (db3)
```

## 🎯 最佳实践建议

### 1. 数据库分配策略
```python
# 推荐的分配方式
db0: 应用缓存
db1: 用户会话
db2: 消息队列
db3: Excel 导入系统  # 你的项目
db4: 图片处理系统
db5: 邮件系统
...
```

### 2. 命名规范
```python
# 队列命名
{模块名}_{功能}_queue
excel_import_queue
email_send_queue
image_process_queue

# 任务命名  
{模块名}_{功能}_task
import_excel_task
send_email_task
process_image_task
```

### 3. 监控和调试
```bash
# 查看队列状态
celery -A celery_task inspect active_queues

# 查看任务状态
celery -A celery_task inspect active

# 查看 Redis 数据
redis-cli -n 3 KEYS "*"
redis-cli -n 3 LLEN excel_import_queue
```

## ⚠️ 注意事项

### 1. 资源限制
- Redis 默认最多 16 个数据库
- 所有 DB 共享内存限制
- 连接数限制影响所有 DB

### 2. 备份和恢复
- 备份时包含所有 DB 的数据
- 恢复时需要注意 DB 分配

### 3. 监控告警
- 分别监控不同 DB 的使用情况
- 设置队列长度告警
- 监控任务执行时间

## 📋 总结

你的项目采用了**四重隔离**策略：
1. **数据库隔离** (db3) - 最高级别的物理隔离
2. **队列隔离** (excel_import_queue) - 任务分类隔离  
3. **任务隔离** (import_excel_task) - 任务名称隔离
4. **结果隔离** (db3 + 过期策略) - 结果存储隔离

这种配置提供了**最高级别的安全性**，确保与其他项目完全不会冲突！

## 🛠️ 实际操作示例

### 查看当前配置
```bash
# 检查 Redis 连接
redis-cli -h 47.98.45.40 -p 6379 -a "wtg2024@" -n 3 ping

# 查看 db3 中的数据
redis-cli -h 47.98.45.40 -p 6379 -a "wtg2024@" -n 3 keys "*"

# 查看队列长度
redis-cli -h 47.98.45.40 -p 6379 -a "wtg2024@" -n 3 llen excel_import_queue
```

### 监控任务执行
```bash
# 查看活跃任务
celery -A celery_task inspect active

# 查看任务统计
celery -A celery_task inspect stats

# 查看队列状态
celery -A celery_task inspect active_queues
```

### 清理和维护
```bash
# 清理过期的任务结果
celery -A celery_task purge

# 清理特定队列
redis-cli -h 47.98.45.40 -p 6379 -a "wtg2024@" -n 3 del excel_import_queue

# 查看内存使用
redis-cli -h 47.98.45.40 -p 6379 -a "wtg2024@" info memory
```

## 🔍 故障排查

### 常见问题诊断
```python
# 检查配置是否正确
from celery_task.celery import celery_app
print(f"Broker URL: {celery_app.conf.broker_url}")
print(f"Result Backend: {celery_app.conf.result_backend}")
print(f"Default Queue: {celery_app.conf.task_default_queue}")
```

### 数据库使用情况
```bash
# 查看各个数据库的 key 数量
for i in {0..15}; do
  count=$(redis-cli -h 47.98.45.40 -p 6379 -a "wtg2024@" -n $i eval "return #redis.call('keys', '*')" 0)
  echo "db$i: $count keys"
done
```

这个文档涵盖了你需要了解的所有概念，现在你对 Redis + Celery 的隔离机制应该有了全面的理解！

---

# 📋 Celery + Redis 任务执行流程详解

## 🔄 完整工作流程图

```
[客户端] → [FastAPI] → [Redis队列] → [Celery Worker] → [Redis结果] → [客户端轮询]
    ↓         ↓           ↓             ↓              ↓           ↓
  提交请求   创建任务    任务入队      任务执行        结果存储     获取结果
```

## 📝 详细执行步骤

### 步骤 1: 客户端提交任务
```http
POST /importer/refresh-from-url-async
{
    "excel_url": "https://example.com/data.xlsx",
    "sheets_to_import": ["Sheet1", "Sheet2"]
}
```

**发生了什么：**
- 客户端发送 HTTP 请求到 FastAPI 应用
- 请求包含 Excel 文件 URL 和要导入的 Sheet 名称

### 步骤 2: FastAPI 创建 Celery 任务
```python
# api/monitor_excel2sql_api.py
from celery_task.excel_import_task import import_excel_task

# 创建异步任务
task = import_excel_task.delay(excel_url, sheets_to_import)
task_id = task.id  # 生成唯一的任务 ID
```

**发生了什么：**
- FastAPI 调用 `import_excel_task.delay()` 方法
- Celery 生成唯一的任务 ID (UUID4 格式)
- 任务被序列化为 JSON 格式

### 步骤 3: 任务进入 Redis 队列
```python
# Redis db3 中发生的操作
LPUSH excel_import_queue '{"id": "abc123-def456", "task": "import_excel_task", "args": [...], "kwargs": {...}}'
```

**Redis 中的数据结构：**
```
db3:
├── excel_import_queue (list)
│   ├── 任务1: {"id": "abc123", "task": "import_excel_task", ...}
│   ├── 任务2: {"id": "def456", "task": "import_excel_task", ...}
│   └── 任务3: {"id": "ghi789", "task": "import_excel_task", ...}
└── _kombu.binding.excel_import_queue (set)
    └── 队列绑定信息
```

### 步骤 4: FastAPI 立即返回任务 ID
```json
{
    "code": 200,
    "data": {
        "task_id": "abc123-def456-ghi789",
        "message": "数据库刷新任务已成功提交，正在后台处理中。"
    }
}
```

**发生了什么：**
- FastAPI 立即返回响应，不等待任务完成
- 客户端获得任务 ID，可以用来查询状态

### 步骤 5: Celery Worker 监听队列
```python
# Celery Worker 持续运行
while True:
    # 从 Redis 队列中获取任务
    task_data = redis.brpop('excel_import_queue', timeout=1)
    if task_data:
        # 反序列化任务
        task = deserialize(task_data)
        # 执行任务
        execute_task(task)
```

**发生了什么：**
- Celery Worker 使用 `BRPOP` 命令阻塞式监听队列
- 一旦有任务，立即取出并开始处理
- 支持多个 Worker 同时监听同一个队列

### 步骤 6: 任务执行过程
```python
@celery_app.task(name="import_excel_task", bind=True)
def import_excel_task(self, excel_url, sheets_to_import):
    task_id = self.request.id

    # 更新任务状态为 PROGRESS
    self.update_state(
        state="PROGRESS",
        meta={"current_stage": "下载 Excel 文件", "progress": 10}
    )

    # 执行具体业务逻辑
    # 1. 下载 Excel 文件
    # 2. 导入到 MySQL
    # 3. 生成新 Excel
    # 4. 上传到 OSS

    # 返回最终结果
    return {"status": "success", "oss_url": "https://..."}
```

**Redis 中的状态更新：**
```
db3:
├── celery-task-meta-abc123-def456 (string)
│   └── '{"status": "PROGRESS", "result": {"current_stage": "下载文件", "progress": 10}}'
└── celery-task-meta-abc123-def456 (string) [更新]
    └── '{"status": "SUCCESS", "result": {"oss_url": "https://...", "status": "success"}}'
```

### 步骤 7: 客户端轮询任务状态
```http
GET /importer/import-status/abc123-def456-ghi789
```

```python
# FastAPI 查询任务状态
from celery.result import AsyncResult

task_result = AsyncResult(task_id, app=celery_app)
status = task_result.state  # PENDING, PROGRESS, SUCCESS, FAILURE
result = task_result.info   # 任务结果或进度信息
```

**Redis 查询操作：**
```python
# Celery 在 Redis 中查询
result_key = f"celery-task-meta-{task_id}"
task_data = redis.get(result_key)
```

### 步骤 8: 返回最终结果
```json
{
    "code": 200,
    "data": {
        "task_id": "abc123-def456-ghi789",
        "status": "SUCCESS",
        "oss_url": "https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/...",
        "message": "任务成功完成。"
    }
}
```

## 🔧 Redis 数据结构详解

### 队列数据结构 (List)
```bash
# 查看队列长度
redis-cli -n 3 LLEN excel_import_queue

# 查看队列内容（不移除）
redis-cli -n 3 LRANGE excel_import_queue 0 -1

# 模拟 Worker 取任务
redis-cli -n 3 BRPOP excel_import_queue 1
```

### 任务结果数据结构 (String)
```bash
# 查看任务结果
redis-cli -n 3 GET celery-task-meta-abc123-def456

# 设置结果过期时间
redis-cli -n 3 TTL celery-task-meta-abc123-def456
```

### 队列绑定信息 (Set)
```bash
# 查看队列绑定
redis-cli -n 3 SMEMBERS _kombu.binding.excel_import_queue
```

## ⚡ 并发处理机制

### 多 Worker 协作
```
Redis Queue: [任务1] [任务2] [任务3] [任务4] [任务5]
                ↓       ↓       ↓       ↓
Worker1: ←─────┘       │       │       │
Worker2: ←─────────────┘       │       │
Worker3: ←─────────────────────┘       │
Worker4: ←─────────────────────────────┘
```

**特点：**
- 多个 Worker 可以同时从同一个队列取任务
- Redis 的 `BRPOP` 操作是原子性的，确保任务不重复
- 任务按 FIFO (先进先出) 顺序处理

### 任务状态流转
```
PENDING → PROGRESS → SUCCESS
   ↓         ↓         ↓
 等待执行   正在执行   执行成功
   ↓
FAILURE (执行失败)
```

## 🔍 实时监控示例

### 监控队列状态
```python
import redis
from config.settings import settings

r = redis.Redis(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
    db=settings.REDIS_DB,
    password=settings.REDIS_PASSWORD
)

# 实时监控
while True:
    queue_length = r.llen('excel_import_queue')
    print(f"队列中等待的任务数: {queue_length}")

    # 查看最新的任务
    if queue_length > 0:
        latest_task = r.lindex('excel_import_queue', 0)
        print(f"最新任务: {latest_task}")

    time.sleep(5)
```

### 监控任务执行
```python
from celery_task.celery import celery_app

# 查看活跃任务
active_tasks = celery_app.control.inspect().active()
print(f"正在执行的任务: {active_tasks}")

# 查看 Worker 统计
stats = celery_app.control.inspect().stats()
print(f"Worker 统计: {stats}")
```

## 🚨 异常处理流程

### 任务执行失败
```python
@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3})
def import_excel_task(self, excel_url, sheets_to_import):
    try:
        # 执行任务逻辑
        pass
    except Exception as exc:
        # 更新失败状态
        self.update_state(
            state="FAILURE",
            meta={"error": str(exc)}
        )
        raise
```

### Redis 连接断开
```python
# Celery 自动重连机制
broker_connection_retry_on_startup = True
broker_connection_retry = True
broker_connection_max_retries = 10
```

## 📊 性能优化要点

### 1. 队列长度控制
```python
# 限制队列最大长度
task_queue_max_priority = 10
worker_prefetch_multiplier = 1  # 每次只取一个任务
```

### 2. 结果存储优化
```python
# 及时清理结果
result_expires = 3600  # 1小时后自动删除
task_ignore_result = False  # 是否忽略结果存储
```

### 3. 连接池优化
```python
# Redis 连接池配置
broker_transport_options = {
    'socket_connect_timeout': 30,
    'socket_timeout': 10.0,
    'socket_keepalive': True,
}
```

这就是你的 Excel 导入系统中 Celery + Redis 的完整工作流程！每个环节都有明确的职责分工，确保系统的可靠性和可扩展性。
