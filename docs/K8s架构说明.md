# 🚀 yiya-ai-bot 项目 Kubernetes 架构说明

## 📋 概述

本文档详细说明了 yiya-ai-bot 项目在 Kubernetes 集群中的完整架构，包括网络路由、服务关系和资源配置。

## 🏗️ K8s 核心概念关系

### **📦 K8s 资源层次结构**

```mermaid
graph TB
    subgraph "外部访问层"
        A1[域名: dev.bot.yiya-ai.com]
        A2[域名: dev-gw.yiya-ai.com]
    end
    
    subgraph "Ingress 层 (路由规则)"
        B1[Ingress: dev.bot.yiya-ai.com]
        B2[Ingress: dev-gw.yiya-ai.com]
    end
    
    subgraph "Service 层 (服务发现)"
        C1[Service: yiya-ai-bot-dev<br/>ClusterIP: **************]
        C2[Service: yiya-gateway-dev<br/>ClusterIP: **************]
    end
    
    subgraph "Deployment 层 (应用管理)"
        D1[Deployment: yiya-ai-bot-dev]
        D2[Deployment: yiya-gateway-dev]
    end
    
    subgraph "Pod 层 (运行实例)"
        E1[Pod: yiya-ai-bot-dev<br/>IP: *********:7860]
        E2[Pod: yiya-gateway-dev<br/>IP: *********:7001]
    end
    
    A1 --> B1
    A2 --> B2
    B1 --> C1
    B2 --> C2
    C1 --> D1
    C2 --> D2
    D1 --> E1
    D2 --> E2
    
    style A1 fill:#e1f5fe
    style A2 fill:#fff3e0
    style E1 fill:#e8f5e8
    style E2 fill:#f3e5f5
```

### **🔗 各层级的作用**

| 层级 | 作用 | 特点 | 示例 |
|------|------|------|------|
| **域名** | 外部访问入口 | 人类可读，DNS解析 | `dev.bot.yiya-ai.com` |
| **Ingress** | HTTP路由规则 | 域名到Service的映射 | `dev.bot.yiya-ai.com` → `yiya-ai-bot-dev:7860` |
| **Service** | 服务发现和负载均衡 | 稳定的虚拟IP，不会因Pod重启而变化 | `**************:7860` |
| **Deployment** | 应用生命周期管理 | 管理Pod的创建、更新、扩缩容 | 确保1个Pod副本运行 |
| **Pod** | 实际运行的容器 | 真实的应用实例，IP会变化 | `*********:7860` |

## 🌐 网络访问路径

### **路径1: 直接访问 yiya-ai-bot-dev**

```mermaid
sequenceDiagram
    participant User as 外部用户
    participant DNS as DNS解析
    participant Ingress as Nginx Ingress
    participant Service as yiya-ai-bot-dev Service
    participant Pod as yiya-ai-bot-dev Pod
    
    User->>DNS: dev.bot.yiya-ai.com
    DNS->>User: *************
    User->>Ingress: HTTP请求到 *************
    Ingress->>Service: 转发到 **************:7860
    Service->>Pod: 负载均衡到 *********:7860
    Pod->>Service: 响应
    Service->>Ingress: 响应
    Ingress->>User: 响应
```

**访问示例：**
```bash
curl http://dev.bot.yiya-ai.com/importer/refresh-from-url-async
```

### **路径2: 通过网关访问**

```mermaid
sequenceDiagram
    participant User as 外部用户
    participant DNS as DNS解析
    participant Ingress as Nginx Ingress
    participant GwService as yiya-gateway-dev Service
    participant GwPod as yiya-gateway-dev Pod
    participant BotService as yiya-ai-bot-dev Service
    participant BotPod as yiya-ai-bot-dev Pod
    
    User->>DNS: dev-gw.yiya-ai.com
    DNS->>User: *************
    User->>Ingress: HTTPS请求 + Authorization
    Ingress->>GwService: 转发到 **************:8080
    GwService->>GwPod: 负载均衡到 *********:7001
    GwPod->>GwPod: 验证Authorization
    GwPod->>BotService: 内部调用 yiya-ai-bot-dev:7860
    BotService->>BotPod: 转发到 *********:7860
    BotPod->>BotService: 响应
    BotService->>GwPod: 响应
    GwPod->>GwService: 响应
    GwService->>Ingress: 响应
    Ingress->>User: 响应
```

**访问示例：**
```bash
curl -H "Authorization: Bearer <token>" https://dev-gw.yiya-ai.com/bot/health
```

## 📊 服务配置详解

### **yiya-ai-bot-dev 服务配置**

```yaml
# Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yiya-ai-bot-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: yiya-ai-bot-dev
  template:
    spec:
      containers:
      - name: yiya-ai-bot-dev
        image: yiya-acr-registry-vpc.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot:YYYY-MM-DD-HH-MM-SS
        ports:
        - containerPort: 7860
        resources:
          limits:
            cpu: "6"
            memory: 8000Mi
          requests:
            cpu: "4"
            memory: 6000Mi

---
# Service
apiVersion: v1
kind: Service
metadata:
  name: yiya-ai-bot-dev
spec:
  type: NodePort
  ports:
  - port: 7860          # Service端口
    targetPort: 7860    # Pod端口
    nodePort: 30222     # 外部访问端口
  selector:
    app: yiya-ai-bot-dev

---
# Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dev.bot.yiya-ai.com
spec:
  rules:
  - host: dev.bot.yiya-ai.com
    http:
      paths:
      - path: /
        backend:
          service:
            name: yiya-ai-bot-dev
            port:
              number: 7860
```

### **yiya-gateway-dev 服务配置**

```yaml
# Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yiya-gateway-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: yiya-gateway-dev
  template:
    spec:
      containers:
      - name: yiya-gateway-dev
        image: yiya-acr-registry-vpc.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-gateway:2025-03-10-22-25-13
        env:
        - name: NACOS_NAMESPACE
          value: yiya-local
        # 注意：容器内监听7001端口

---
# Service
apiVersion: v1
kind: Service
metadata:
  name: yiya-gateway-dev
spec:
  type: ClusterIP
  ports:
  - port: 8080          # Service端口
    targetPort: 7001    # Pod端口（网关应用监听7001）
  selector:
    app: yiya-gateway-dev

---
# Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dev-gw.yiya-ai.com
spec:
  rules:
  - host: dev-gw.yiya-ai.com
    http:
      paths:
      - path: /
        backend:
          service:
            name: yiya-gateway-dev
            port:
              number: 8080
```

## 🔍 关键理解点

### **1. 为什么 Service IP 不会变，但 Pod IP 会变？**

```mermaid
graph TB
    subgraph "Service 层 (稳定)"
        A[yiya-ai-bot-dev Service<br/>IP: **************<br/>🔒 永远不变]
    end

    subgraph "Pod 层 (动态)"
        B1[Pod 实例1<br/>IP: *********<br/>⚡ 重启后可能变为 *********]
        B2[Pod 实例2<br/>IP: *********<br/>⚡ 扩容时新增]
    end

    A --> B1
    A --> B2

    style A fill:#e8f5e8
    style B1 fill:#fff3e0
    style B2 fill:#fff3e0
```

**原因：**
- **Service** 是 K8s 的虚拟资源，IP 由集群分配且固定
- **Pod** 是实际的容器实例，重启、扩缩容时 IP 会重新分配
- **Service** 通过标签选择器自动发现和负载均衡到后端 Pod

### **2. 端口映射关系**

#### **yiya-ai-bot-dev 端口链路：**
```
外部访问:30222 → Service:7860 → Pod:7860 → 应用进程:7860
```

#### **yiya-gateway-dev 端口链路：**
```
Ingress:80 → Service:8080 → Pod:7001 → 网关应用:7001
```

### **3. 网关服务的作用**

**yiya-gateway-dev 是一个独立的应用服务（很可能是 Java Spring Gateway），负责：**

- ✅ **认证授权**：验证 Authorization 头
- ✅ **路由转发**：根据路径转发到不同的后端服务
- ✅ **统一入口**：提供统一的 API 网关
- ✅ **服务发现**：通过 Nacos 发现后端服务

**网关内部可能的路由配置：**
```yaml
# 网关应用内部配置（推测）
spring:
  cloud:
    gateway:
      routes:
      - id: bot-service
        uri: http://yiya-ai-bot-dev:7860
        predicates:
        - Path=/bot/**
      - id: admin-service
        uri: http://yiya-admin-dev:8084
        predicates:
        - Path=/admin/**
```

## 🎯 访问方式对比

### **开发调试场景**

| 访问方式 | URL | 特点 | 适用场景 |
|---------|-----|------|---------|
| **直接访问** | `http://dev.bot.yiya-ai.com/api` | 无需认证，直达服务 | 开发调试、单元测试 |
| **NodePort访问** | `http://节点IP:30222/api` | 绕过域名，直接访问 | 网络调试、本地测试 |
| **集群内访问** | `http://yiya-ai-bot-dev:7860/api` | 服务名访问，最稳定 | 服务间调用 |

### **生产模拟场景**

| 访问方式 | URL | 特点 | 适用场景 |
|---------|-----|------|---------|
| **网关访问** | `https://dev-gw.yiya-ai.com/bot/api` | 需要认证，完整链路 | 集成测试、生产模拟 |

## 🛠️ 实用命令

### **查看服务状态**
```bash
# 查看所有服务
kubectl get services -o wide

# 查看特定服务详情
kubectl describe service yiya-ai-bot-dev
kubectl describe service yiya-gateway-dev

# 查看服务的端点（实际Pod IP）
kubectl get endpoints yiya-ai-bot-dev
kubectl get endpoints yiya-gateway-dev
```

### **查看网络路由**
```bash
# 查看所有 Ingress
kubectl get ingress -o wide

# 查看特定 Ingress 详情
kubectl describe ingress dev.bot.yiya-ai.com
kubectl describe ingress dev-gw.yiya-ai.com
```

### **调试网络连接**
```bash
# 在集群内测试服务连通性
kubectl run debug --image=busybox -it --rm -- sh
# 在 debug pod 中执行：
wget -qO- http://yiya-ai-bot-dev:7860/health
wget -qO- http://yiya-gateway-dev:8080/health

# 测试外部访问
curl http://dev.bot.yiya-ai.com/health
curl -H "Authorization: Bearer <token>" https://dev-gw.yiya-ai.com/health
```

### **查看Pod和容器信息**
```bash
# 查看Pod状态
kubectl get pods -l app=yiya-ai-bot-dev
kubectl get pods -l app=yiya-gateway-dev

# 进入容器调试
kubectl exec -it <pod-name> -- bash

# 查看容器日志
kubectl logs -f deployment/yiya-ai-bot-dev
kubectl logs -f deployment/yiya-gateway-dev
```

## 🔧 故障排查

### **常见问题和解决方法**

#### **1. 外部访问不通**
```bash
# 检查 Ingress 状态
kubectl describe ingress dev.bot.yiya-ai.com

# 检查 Service 端点
kubectl get endpoints yiya-ai-bot-dev

# 检查 Pod 状态
kubectl get pods -l app=yiya-ai-bot-dev
```

#### **2. 服务间调用失败**
```bash
# 检查 Service 是否存在
kubectl get service yiya-ai-bot-dev

# 检查 DNS 解析
kubectl run debug --image=busybox -it --rm -- nslookup yiya-ai-bot-dev

# 检查端口是否正确
kubectl describe service yiya-ai-bot-dev
```

#### **3. 网关认证失败**
```bash
# 检查网关Pod日志
kubectl logs -f deployment/yiya-gateway-dev

# 检查网关配置
kubectl describe deployment yiya-gateway-dev
```

## 📋 总结

### **架构优势**
1. **灵活访问**：提供直接访问和网关访问两种方式
2. **服务隔离**：每个服务独立部署和管理
3. **负载均衡**：Service 自动负载均衡到后端 Pod
4. **高可用**：Pod 故障时自动重启和恢复

### **设计原则**
1. **基础设施稳定**：Service、Ingress 手动管理
2. **应用层灵活**：Deployment、Pod 自动管理
3. **网络分层**：外部→Ingress→Service→Pod 清晰分层
4. **服务发现**：通过 Service 名称进行稳定的服务间调用

这个架构设计既满足了开发调试的便利性，又保证了生产环境的安全性和可扩展性！🚀
