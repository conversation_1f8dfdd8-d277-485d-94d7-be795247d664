# 🚀 Yiya AI Bot 项目启动流程说明

## 📋 项目概述

Yiya AI Bot 是一个基于 FastAPI + Celery 的 AI 服务应用，支持 Excel 导入、文档处理、OCR 等功能。项目采用 Docker 容器化部署，使用 Circus 进程管理器管理多个服务进程。

## ⚠️ **重要说明 - 避免常见疑惑**

**本项目采用阿里云 Codeup CI/CD 流水线进行自动化构建和部署，不依赖本地构建脚本！**

### **🤔 常见疑惑解答：**

#### **Q1: 为什么本地的 `k8s/manifest2.yaml` 不起作用？**
**A:** 因为 K8s 配置由流水线的 "Kubernetes 分批发布" 步骤自动管理，该文件是历史遗留或参考模板。

#### **Q2: 为什么 `build.sh` 脚本配置与实际运行不符？**
**A:** 流水线有自己的构建逻辑，不使用本地 `build.sh`。该脚本是历史遗留文件。

#### **Q3: 为什么删除 Deployment 后重新运行流水线就能恢复？**
**A:** 流水线会自动检测并创建缺失的 K8s 资源，使用内置的 Deployment 模板。

#### **Q4: `APP-META/k8s/` 目录下的 YAML 文件是做什么的？**
**A:** 这些是 **OCR 服务** 的配置文件（`yiya-ai-ocr`、`paddle-ocr`），不是主应用的配置。

#### **Q5: 镜像版本号为什么是 `YYYY-MM-DD-HH-MM-SS` 格式？**
**A:** 这是流水线自动生成的时间戳版本号，每次构建都会生成新的版本。

## 🏗️ 整体架构

```mermaid
graph TB
    subgraph "基础镜像构建 (CI/CD - yiya-ai-bot_base 仓库)"
        A1[Dockerfile_base] --> A2[基础镜像构建]
        A2 --> A3[yiya-ai-bot:latest]
        A3 --> A4[包含: Python环境 + 系统依赖 + 启动脚本]
    end

    subgraph "应用镜像构建 (CI/CD - yiya-ai-bot-dev 仓库)"
        B1[代码提交] --> B2[流水线触发]
        B2 --> B3[Python构建: 打包源码为tgz]
        B3 --> B4[镜像构建: 使用Dockerfile]
        A3 --> B4
        B4 --> B5[安装应用依赖 + 复制代码包]
        B5 --> B6[推送镜像: yiya-ai-bot:YYYY-MM-DD-HH-MM-SS]
    end

    subgraph "自动部署 (K8s)"
        B6 --> C1[Kubernetes分批发布]
        C1 --> C2[自动创建/更新 Deployment]
        C2 --> C3[滚动更新 Pod]
    end

    subgraph "容器运行时"
        C3 --> D1[容器启动]
        D1 --> D2[解压应用代码到target/]
        D2 --> D3[/root/start.sh]
        D3 --> D4[appctl.sh start]
        D4 --> D5[circusd 进程管理器]

        D5 --> D6[gunicorn: FastAPI服务]
        D5 --> D7[celery: 异步任务]

        D4 --> D8[tail -f error.log]
        D8 --> D9[容器日志输出]
    end

    style A4 fill:#e1f5fe
    style B6 fill:#fff3e0
    style C2 fill:#e8f5e8
    style D9 fill:#ffebee
```

## 🔄 详细启动流程

### 1️⃣ 构建阶段

#### 🏭 阶段1: 基础镜像构建 (CI/CD 流水线 - yiya-ai-bot_base 仓库)

**流水线配置 (阿里云 Codeup)：**
- **仓库：** `yiya-ai-bot_base`
- **Dockerfile：** `APP-META/docker-config/Dockerfile_base`
- **Context：** `APP-META/docker-config`
- **目标镜像：** `yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot:latest`

```mermaid
graph LR
    A[python:3.11.0<br/>基础镜像] --> B[安装系统依赖<br/>OpenGL, unar等]
    B --> C[安装 Python 包<br/>circus, opencv等]
    C --> D[复制启动脚本<br/>appctl.sh, circus.ini]
    D --> E[设置启动命令<br/>/root/start.sh]
    E --> F[基础镜像<br/>yiya-ai-bot:latest]

    style A fill:#e3f2fd
    style F fill:#fff3e0
```

**基础镜像包含：**
- 系统依赖：OpenGL、unar、poppler-utils 等
- Python 包：circus、opencv、torch 等核心依赖
- 启动脚本：appctl.sh、circus.ini 等运行时配置
- 目录结构：/root/yiya-ai-bot/ 完整目录

#### 📦 阶段2: 应用镜像构建 (CI/CD 流水线 - yiya-ai-bot-dev 仓库)

**⚠️ 重要：不使用本地 `build.sh`，而是流水线自动执行！**

**流水线步骤：**

1. **Python 构建步骤**：
   ```bash
   APP_NAME=yiya-ai-bot
   build_dir=$(cd APP-META/docker-config; pwd)

   # 在项目根目录打包源码，保存到 docker-config 目录
   tar -zcvf ${build_dir}/${APP_NAME}.tgz \
     --exclude='APP-META' \
     --exclude='logs' \
     --exclude='.venv' \
     --exclude='model' \
     --exclude='nacos-data' \
     --exclude='.git' \
     --exclude='.idea' \
     --exclude='.env' \
     *
   ```

2. **镜像构建步骤**：
   ```yaml
   镜像仓库: yiya-acr
   命名空间: yiya
   仓库: yiya-ai-bot
   标签: ${DATETIME}  # 格式: YYYY-MM-DD-HH-MM-SS
   Dockerfile路径: APP-META/docker-config/Dockerfile
   ContextPath: APP-META/docker-config
   ```

3. **Kubernetes 分批发布步骤**：
   - 自动创建或更新 Deployment
   - 使用内置模板，不依赖项目中的 YAML 文件
   - 自动设置资源限制（CPU: 4-6核，内存: 6-8GB）

```mermaid
graph LR
    A[基础镜像<br/>yiya-ai-bot:latest] --> B[安装应用依赖<br/>pdf2image, celery等]
    B --> C[复制应用代码<br/>yiya-ai-bot.tgz]
    C --> D[最终应用镜像<br/>YYYY-MM-DD-HH-MM-SS]

    style A fill:#fff3e0
    style D fill:#e8f5e8
```

**Dockerfile 关键操作：**
```dockerfile
# 继承基础镜像
FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot:latest

# 安装应用特定依赖
RUN pip install pdf2image==1.17.0 orjson==3.10.18 pymysql==1.1.1 celery==5.5.3

# 复制应用代码包（由流水线生成）
COPY yiya-ai-bot.tgz $APP_HOME/$APP_NAME/target/

# 启动命令已在基础镜像中设置
CMD ["/bin/bash", "-c", "/root/start.sh"]
```

#### 🎯 两阶段构建的优势

**为什么采用两阶段构建？**

1. **基础镜像稳定性**：
   - 系统依赖和核心 Python 包变化频率低
   - 基础镜像可以长期复用，减少构建时间

2. **应用镜像灵活性**：
   - 应用代码变化频繁，只需重新构建轻量级的应用层
   - 快速迭代，提高开发效率

3. **CI/CD 分离**：
   - 基础镜像通过流水线自动构建和更新
   - 应用镜像可以本地构建或独立的 CI 流程

4. **资源优化**：
   - 基础镜像一次构建，多次使用
   - 减少重复的依赖安装时间

```mermaid
graph TB
    subgraph "基础镜像层 (变化少)"
        A1[系统依赖] --> A2[Python 环境]
        A2 --> A3[核心包: circus, opencv]
        A3 --> A4[启动脚本]
    end

    subgraph "应用镜像层 (变化频繁)"
        B1[应用特定依赖] --> B2[应用代码]
        B2 --> B3[配置文件]
    end

    A4 --> B1

    style A1 fill:#e3f2fd
    style B3 fill:#e8f5e8
```

### 2️⃣ 部署阶段

#### 🚀 Kubernetes 自动部署

**⚠️ 重要：K8s 配置由流水线自动管理，不使用项目中的 YAML 文件！**

**实际部署流程：**

1. **流水线的 "Kubernetes 分批发布" 步骤**：
   - 检测 Deployment 是否存在
   - 不存在则使用内置模板创建
   - 存在则更新镜像版本
   - 自动触发滚动更新

2. **自动生成的 Deployment 配置**：
   ```yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: yiya-ai-bot-dev
     labels:
       app: yiya-ai-bot-dev
   spec:
     replicas: 1
     selector:
       matchLabels:
         app: yiya-ai-bot-dev
     template:
       spec:
         containers:
         - name: yiya-ai-bot-dev
           image: yiya-acr-registry-vpc.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot:YYYY-MM-DD-HH-MM-SS
           resources:
             limits:
               cpu: "6"
               memory: 8000Mi
             requests:
               cpu: "4"
               memory: 6000Mi
         imagePullSecrets:
         - name: yiya-acr-key
   ```

3. **自动恢复机制**：
   - 即使删除 Deployment，重新运行流水线也会自动重建
   - 流水线会检测并创建缺失的 K8s 资源

**关键配置说明：**
- **镜像地址**：使用阿里云 ACR VPC 内网地址
- **版本号**：流水线自动生成的时间戳（YYYY-MM-DD-HH-MM-SS）
- **资源限制**：CPU 6核，内存 8GB
- **资源请求**：CPU 4核，内存 6GB
- **镜像拉取密钥**：`yiya-acr-key`

### 3️⃣ 运行时启动

#### 🎯 启动链路

```mermaid
sequenceDiagram
    participant K8s
    participant Container
    participant StartScript as /root/start.sh
    participant AppCtl as appctl.sh
    participant Circus as circusd
    participant Gunicorn
    participant Celery
    
    K8s->>Container: 启动容器
    Container->>StartScript: 执行启动脚本
    StartScript->>AppCtl: 调用 appctl.sh start
    AppCtl->>AppCtl: 解压应用代码
    AppCtl->>AppCtl: 替换配置模板
    AppCtl->>Circus: 启动 circusd --daemon circus.ini
    
    Circus->>Gunicorn: 启动主应用进程
    Circus->>Celery: 启动 Celery Worker
    
    Gunicorn->>Gunicorn: FastAPI 服务就绪
    Celery->>Celery: 异步任务处理就绪
    
    AppCtl->>AppCtl: tail -f error.log
    AppCtl->>Container: 输出日志到容器 stdout
```

#### 📁 关键文件说明

| 文件路径 | 作用 | 说明 |
|---------|------|------|
| `/root/start.sh` | 容器启动入口 | Docker CMD 执行的脚本 |
| `/root/yiya-ai-bot/bin/appctl.sh` | 应用控制脚本 | 启动/停止应用的主要脚本 |
| `/root/yiya-ai-bot/conf/circus.ini` | 进程管理配置 | 定义启动的服务进程 |
| `/root/yiya-ai-bot/target/yiya-ai-bot.tgz` | 应用代码包 | 打包的应用源代码 |

### 4️⃣ 进程管理 (Circus)

#### 🎪 Circus 配置详解

```ini
[watcher:yiya-ai-bot]
# 主应用进程 - FastAPI 服务
cmd=gunicorn app:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:7860
stdout_stream.filename=/root/yiya-ai-bot/logs/application.log
stderr_stream.filename=/root/yiya-ai-bot/logs/error.log

[watcher:yiya-ai-bot-celery]
# Celery Worker 进程 - 异步任务处理
cmd=celery -A celery_task worker --loglevel=INFO --pool=threads --concurrency=4
stdout_stream.filename=/root/yiya-ai-bot/logs/celery.log
stderr_stream.filename=/root/yiya-ai-bot/logs/celery_error.log
```

#### 🔄 进程启动顺序

```mermaid
graph TD
    A[circusd 启动] --> B[读取 circus.ini]
    B --> C[启动 gunicorn 主应用]
    B --> D[启动 celery worker]
    
    C --> E[FastAPI 应用就绪<br/>监听 7860 端口]
    D --> F[Celery Worker 就绪<br/>处理异步任务]
    
    E --> G[健康检查通过]
    F --> G
    G --> H[服务可用]
    
    style A fill:#e1f5fe
    style H fill:#e8f5e8
```

## 📊 日志系统

### 🗂️ 日志文件分布

```mermaid
graph LR
    subgraph "应用日志"
        A[app.log<br/>应用业务日志]
        B[biz.log<br/>业务操作日志]
    end
    
    subgraph "Circus 管理的进程日志"
        C[application.log<br/>主应用 stdout]
        D[error.log<br/>主应用 stderr]
        E[celery.log<br/>Celery stdout]
        F[celery_error.log<br/>Celery stderr]
    end
    
    subgraph "系统日志"
        G[gunicorn_access.log<br/>访问日志]
        H[gunicorn_error.log<br/>Gunicorn 错误]
    end
    
    subgraph "容器日志"
        I[kubectl logs<br/>K8s 容器日志]
    end
    
    D --> I
    
    style D fill:#ffebee
    style I fill:#e8f5e8
```

### 📋 日志查看方法

#### 本地开发环境
```bash
# 查看应用主日志
tail -f logs/application.log

# 查看 Celery 日志
tail -f logs/celery.log

# 查看错误日志
tail -f logs/error.log

# 同时查看多个日志
tail -f logs/application.log logs/celery.log
```

#### K8s 环境
```bash
# 查看容器日志（主要是 error.log 内容）
kubectl logs -f deployment/yiya-ai-bot

# 进入容器查看具体日志文件
kubectl exec -it deployment/yiya-ai-bot -- bash
tail -f /root/yiya-ai-bot/logs/celery.log
```

### ⚠️ 重要说明：为什么 K8s 看不到 Celery 日志

**问题原因：**
1. `appctl.sh` 只执行了 `tail -f error.log`
2. Celery 日志写入 `celery.log` 文件，没有输出到容器 stdout
3. K8s 只能看到容器的 stdout/stderr 输出

**解决方案：**
修改 `appctl.sh` 同时 tail 多个日志文件：
```bash
# 原来
tail -f ${APP_HOME}/logs/error.log

# 修改后
tail -f ${APP_HOME}/logs/error.log ${APP_HOME}/logs/celery.log ${APP_HOME}/logs/celery_error.log
```

## 🛠️ 开发和部署指南

### 本地开发
```bash
# 启动本地环境
circusd circus_local.ini

# 查看日志
tail -f logs/application.log logs/celery.log
```

### 构建和部署

**⚠️ 重要：本项目使用阿里云 Codeup CI/CD 流水线，不需要手动构建和部署！**

#### 🔄 实际的发布流程

1. **代码提交**：
   ```bash
   git add .
   git commit -m "feat: 新功能"
   git push origin main
   ```

2. **流水线自动触发**：
   - 阿里云 Codeup 检测到代码变更
   - 自动执行构建和部署流水线

3. **查看部署状态**：
   ```bash
   # 查看 Pod 状态
   kubectl get pods -l app=yiya-ai-bot-dev

   # 查看部署历史
   kubectl rollout history deployment/yiya-ai-bot-dev

   # 查看实时日志
   kubectl logs -f deployment/yiya-ai-bot-dev
   ```

#### 📋 流水线步骤详解

1. **Python 构建**：打包源码为 `yiya-ai-bot.tgz`
2. **镜像构建**：使用 `Dockerfile` 构建应用镜像
3. **Kubernetes 分批发布**：自动部署到 K8s 集群

#### 🚫 不再使用的方式

以下方式是**历史遗留**，现在不再使用：
```bash
# ❌ 不再使用本地构建
cd APP-META/docker-config
./build.sh

# ❌ 不再手动更新 YAML
kubectl apply -f k8s/manifest2.yaml
```

#### 🔄 完整的 CI/CD 流程
```mermaid
sequenceDiagram
    participant Dev as 开发者
    participant Git as Git 仓库
    participant Pipeline as 阿里云 Codeup 流水线
    participant ACR as 阿里云镜像仓库
    participant K8s as K8s集群

    Note over Dev,K8s: 应用代码更新发布流程 (全自动)

    Dev->>Git: 1. git push (提交代码)
    Git->>Pipeline: 2. 触发流水线
    Pipeline->>Pipeline: 3. Python构建 (打包源码)
    Pipeline->>Pipeline: 4. 镜像构建 (Dockerfile)
    Pipeline->>ACR: 5. 推送镜像 (YYYY-MM-DD-HH-MM-SS)
    Pipeline->>K8s: 6. Kubernetes分批发布
    K8s->>ACR: 7. 拉取新镜像
    K8s->>K8s: 8. 滚动更新 Pod

    Note over Dev,K8s: 基础镜像更新流程 (较少发生)

    Dev->>Dev: 1. 修改 Dockerfile_base
    Dev->>Git: 2. 提交到 yiya-ai-bot_base 仓库
    Git->>Pipeline: 3. 触发基础镜像流水线
    Pipeline->>ACR: 4. 构建并推送基础镜像 :latest

    Note over Dev,K8s: 开发者只需要关心代码提交，其他全部自动化！
```

#### 🎯 关键优势

1. **全自动化**：代码提交后无需任何手动操作
2. **版本管理**：每次构建都有唯一的时间戳版本
3. **自动恢复**：即使删除 K8s 资源，流水线也能重建
4. **分离关注点**：开发者专注代码，运维由流水线处理

## 🤔 常见疑惑详细解答

### **Q1: 为什么项目中有两个 Dockerfile？**

**A:** 采用两阶段构建策略：
- **`Dockerfile_base`**：构建基础镜像，包含系统依赖和 Python 环境（由 CI/CD 流水线管理）
- **`Dockerfile`**：构建应用镜像，继承基础镜像并添加应用代码（由应用流水线使用）

### **Q2: `yiya-ai-bot.tgz` 文件是什么时候生成的？**

**A:** 这个压缩包是在流水线的 "Python 构建" 步骤中动态生成的：
```bash
# 流水线执行：
tar -zcvf APP-META/docker-config/yiya-ai-bot.tgz \
  --exclude='APP-META' --exclude='logs' ... *
```
- **不会提交到 Git 仓库**
- **每次构建都重新生成**
- **包含项目根目录的所有源码**

### **Q3: 为什么删除 Deployment 后流水线能自动恢复？**

**A:** 流水线的 "Kubernetes 分批发布" 步骤会：
1. 检测 Deployment 是否存在
2. 不存在则使用内置模板创建
3. 存在则更新镜像版本
4. 自动配置资源限制和其他参数

### **Q4: 本地的 `k8s/manifest2.yaml` 为什么不起作用？**

**A:** 因为：
- 流水线使用**内置的 Deployment 模板**
- **不读取项目中的 YAML 文件**
- 该文件可能是历史遗留或参考模板

### **Q5: `build.sh` 脚本为什么配置不对？**

**A:** 因为：
- 流水线有**自己的构建逻辑**
- **不使用本地的 `build.sh` 脚本**
- 该脚本是历史遗留文件

### **Q6: 如何查看真实的 K8s 配置？**

**A:** 使用以下命令查看实际运行的配置：
```bash
# 导出当前运行的 Deployment 配置
kubectl get deployment yiya-ai-bot-dev -o yaml > actual-config.yaml

# 查看 Pod 详细信息
kubectl describe pod <pod-name>

# 进入容器查看实际环境
kubectl exec -it <pod-name> -- bash
```

### **Q7: 如何触发重新部署？**

**A:** 只需要提交代码：
```bash
git add .
git commit -m "trigger redeploy"
git push origin main
```
流水线会自动构建新镜像并部署。

### **Q8: 版本号格式为什么是时间戳？**

**A:** 流水线使用 `${DATETIME}` 变量生成版本号：
- **格式**：`YYYY-MM-DD-HH-MM-SS`
- **优势**：每次构建都有唯一版本，便于追踪和回滚

### **Q9: 为什么流水线需要 "Python 构建" 步骤？镜像不是已经有 Python 了吗？**

**A:** 这是一个**命名误导**！"Python 构建" 步骤的实际作用是**打包应用源码**，不是构建 Python 环境。

#### **脚本详细解析：**

```bash
APP_NAME=yiya-ai-bot
build_dir=$(cd APP-META/docker-config; pwd)  # 获取绝对路径

tar -zcvf ${build_dir}/${APP_NAME}.tgz \
  --exclude='APP-META' \
  --exclude='logs' \
  --exclude='.venv' \
  --exclude='model' \
  --exclude='nacos-data' \
  --exclude='.git' \
  --exclude='.idea' \
  --exclude='.env' \
  *
```

#### **执行逻辑详解：**

1. **获取绝对路径**：
   ```bash
   build_dir=$(cd APP-META/docker-config; pwd)
   # 结果：build_dir="/workspace/yiya-ai-bot/APP-META/docker-config"
   ```

2. **构造完整输出路径**：
   ```bash
   ${build_dir}/${APP_NAME}.tgz
   # 展开为：/workspace/yiya-ai-bot/APP-META/docker-config/yiya-ai-bot.tgz
   ```

3. **打包执行**：
   ```bash
   # 在项目根目录执行 tar 命令
   # 打包当前目录（*）的所有文件
   # 输出到 docker-config 目录
   ```

#### **关键理解：**
- **工作目录**：`/workspace/yiya-ai-bot/`（项目根目录）
- **打包内容**：根目录的所有文件（`*`）
- **输出位置**：`APP-META/docker-config/yiya-ai-bot.tgz`
- **文件名**：只是 `yiya-ai-bot.tgz`（不包含路径）

#### **为什么需要这个步骤：**
1. **文件传递**：将源码从流水线环境传递到 Docker 构建环境
2. **上下文限制**：Dockerfile 只能访问 `APP-META/docker-config/` 目录内的文件
3. **文件过滤**：排除不需要的大文件（如 `.git`、`model/` 等）
4. **分离关注点**：打包内容（根目录源码）vs 输出位置（构建目录）

**更准确的命名应该是 "源码打包" 而不是 "Python 构建"。**

### **Q10: Kubernetes 分批发布的 YAML 配置到底是怎么来的？**

**A:** 根据阿里云云效官方文档，**Kubernetes 分批发布不需要完整的 YAML 文件**！

#### **工作原理：**

1. **流水线只需要关键参数**：
   ```yaml
   集群连接: yiya-aliyun01的容器镜像服务(ACR)服务连接
   命名空间: default
   服务Service: yiya-ai-bot-dev
   关联Workloads类型: Deployment
   容器名称: yiya-ai-bot-dev
   镜像: yiya-acr-registry-vpc.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot:YYYY-MM-DD-HH-MM-SS
   ```

2. **自动查找现有资源**：
   - 通过 Service 名称找到关联的 Deployment
   - 读取现有 Deployment 的完整配置
   - 基于现有配置创建新版本

3. **动态生成配置**：
   - 如果 Deployment 存在：只更新镜像版本（滚动更新）
   - 如果 Deployment 不存在：基于参数 + 默认值创建新的

#### **为什么删除后能自动恢复？**

```mermaid
graph TB
    A[流水线执行] --> B{Deployment存在?}
    B -->|是| C[读取现有配置]
    B -->|否| D[使用默认模板]
    C --> E[更新镜像版本]
    D --> F[创建新Deployment]
    E --> G[滚动更新]
    F --> G

    style D fill:#fff3e0
    style F fill:#e8f5e8
```

当 Deployment 被删除后：
1. 流水线检测到资源不存在
2. 使用内置默认模板 + 流水线参数
3. 自动创建新的 Deployment
4. 资源限制等配置来自流水线默认值

#### **关键理解：**
- **Service 需要手动创建**：作为流水线的前提条件
- **Deployment 由流水线管理**：自动创建、更新、恢复
- **通过标签关联**：Service 的 `selector` 与 Deployment 的 `labels` 匹配
- **配置来源**：现有 Service + 流水线参数 + 默认值

#### **实际的资源创建顺序：**
```mermaid
timeline
    title 真实的部署时间线

    2024-12-19 : Service 手动创建
               : NodePort 30222
               : selector: app=yiya-ai-bot-dev

    2025-07-08 : Deployment 流水线创建
               : labels: app=yiya-ai-bot-dev
               : 通过标签与 Service 关联

    持续更新   : 流水线自动更新
               : 已更新 21+ 次
               : 只更新镜像版本
```

**这就是为什么删除 Deployment 后能自动恢复：**
1. Service 依然存在（手动创建，不会被删除）
2. 流水线通过 Service 名称找到标签选择器
3. 基于标签要求重新创建匹配的 Deployment

这就是现代 CI/CD 的强大之处：**手动基础设施 + 自动化应用管理**！

### **Q11: 为什么 Service 需要手动创建，而不是由流水线自动管理？**

**A:** 这是基于 **基础设施与应用分离** 的最佳实践设计：

#### **🌐 网络访问的稳定性**
- Service 是外部访问的入口点（NodePort: 30222）
- 如果由流水线管理，每次部署可能改变端口
- 外部系统、负载均衡器、DNS 配置都需要频繁更新

#### **🔒 基础设施 vs 应用的分离**
```mermaid
graph TB
    subgraph "基础设施层 (手动管理)"
        A[Service - 网络入口]
        B[PVC - 存储卷]
        C[Secret - 密钥]
        D[ConfigMap - 基础配置]
    end

    subgraph "应用层 (自动管理)"
        E[Deployment - 应用实例]
        F[Pod - 运行容器]
        G[ReplicaSet - 副本管理]
    end

    A --> E

    style A fill:#fff3e0
    style E fill:#e8f5e8
```

#### **🎯 多环境管理**
- 开发环境：NodePort 30222
- 测试环境：NodePort 30223
- 生产环境：LoadBalancer
- 每个环境的网络配置不同，需要独立管理

#### **🛡️ 安全和权限控制**
- Service 涉及端口暴露和网络安全
- 需要网络管理员审批和安全团队评估
- 不适合频繁自动化变更

#### **🔗 外部系统集成**
- API 网关、前端应用、其他微服务都依赖 Service
- Service 地址变更会影响整个系统架构
- 需要保持长期稳定

**类比理解：**
```
Service = 房子的地址和门牌号 (稳定，不能随便改)
Deployment = 房子里的家具和装修 (可以随时更换升级)
```

这就是为什么采用 **手动基础设施 + 自动化应用** 的混合管理模式！

### **Q12: Service 的端口配置是如何与应用代码对应的？**

**A:** 端口 7860 贯穿了整个应用栈，形成完整的配置链路：

#### **🔗 端口配置链路：**
```mermaid
graph TB
    subgraph "应用代码层"
        A1[app.py: uvicorn port=7860]
        A2[gunicorn.py: bind=0.0.0.0:7860]
    end

    subgraph "进程管理层"
        B1[circus.ini: --bind 0.0.0.0:7860]
    end

    subgraph "K8s服务层"
        C1[Service: targetPort=7860]
        C2[Service: nodePort=30222]
        C3[Deployment: containerPort=7860]
    end

    A1 --> B1
    A2 --> B1
    B1 --> C3
    C3 --> C1
    C1 --> C2

    style A1 fill:#e1f5fe
    style C1 fill:#e8f5e8
    style C2 fill:#fff3e0
```

#### **📋 配置一致性验证：**
- ✅ **应用代码**: `uvicorn.run(port=7860)` 和 `bind='0.0.0.0:7860'`
- ✅ **Circus 配置**: `--bind 0.0.0.0:7860`
- ✅ **Service**: `targetPort: 7860`
- ✅ **Deployment**: `containerPort: 7860`

#### **🌐 网络流量路径：**
```
外部访问:30222 → Service:7860 → Pod:7860 → 应用进程:7860
```

#### **📅 Service 演进历史：**
- **2024-12-19**: 创建 ClusterIP 类型（仅内部访问）
- **2025-05-21**: 升级为 NodePort（支持外部访问:30222）

这确保了从外部请求到应用进程的完整网络链路畅通！

## 🛠️ 故障排查

### 查看部署状态
```bash
# 查看 Pod 状态
kubectl get pods -l app=yiya-ai-bot-dev
kubectl describe pod <pod-name>

# 查看部署历史
kubectl rollout history deployment/yiya-ai-bot-dev

# 查看实时日志
kubectl logs -f deployment/yiya-ai-bot-dev
```

### 进入容器调试
```bash
# 进入容器
kubectl exec -it <pod-name> -- bash

# 查看进程状态
circusctl status

# 查看应用代码
ls -la /root/yiya-ai-bot/target/

# 查看配置文件
cat /root/yiya-ai-bot/conf/circus.ini
```

### 重启服务
```bash
# 重启整个 Deployment
kubectl rollout restart deployment/yiya-ai-bot-dev

# 在容器内重启特定进程
circusctl restart yiya-ai-bot-celery
```

---

## 📚 相关文档

- [Excel导入系统使用说明](./Excel导入系统使用说明.md)
- [K8s部署说明](./K8s部署说明.md)
- [快速开始指南](./快速开始指南.md)
- [Redis_Celery_隔离机制详解](./Redis_Celery_隔离机制详解.md)
