可参考我公司的代码风格：

## 代码

```python
from pydantic import BaseModel, Field

from models.file_info import FileInfo

class RtfFileRequest(BaseModel):
    rtf_file: FileInfo = Field(None, title="rtf file info")

```

oss.py

```python
# -*- coding: utf-8 -*-

import oss2
import os

LASER = "/data/LASER_yiya"

# 从配置文件中获取Access Key ID和Access Key Secret
access_key_id = "LTAI5tSMtzYQ5GVPCm8njDYp"
access_key_secret = "******************************"

# 使用获取的RAM用户的访问密钥配置访问凭证
auth = oss2.AuthV4(access_key_id, access_key_secret)

# 使用环境变量中获取的RAM用户访问密钥配置访问凭证
bucket = 'yiya-dev'
bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', bucket, region="cn-hangzhou")


def download_file(key, save_path):
    folder = os.path.dirname(save_path)
    if not os.path.exists(folder):
        os.makedirs(folder)
    bucket.get_object_to_file(key, save_path)


def write_file(local_path, key):
    bucket.put_object_from_file(key, local_path)


def get_file_url(key: str):
    expiration = 24 * 60 * 60
    return bucket.sign_url('GET', key, expiration)


if __name__ == '__main__':
    download_file(
        "2124_3161_20241020215131.json",
        "./2124_3161_20241020215131.json")

    # download_file(
    #     "cos/20/Niraparib PPK Modeling Report_Zai_Draft1_UPDATED_20190110_unQCd (1)_parse3524935558657725957.json",
    #     "./1_tgt.json")
    # src_data_2 = json.load(open("2_src.json", "r"))
    # segmentList = src_data_2['segmentList']
    # tgt_data = {"charCount": 11058, "language": "en-US", "rowCount": 1408 }
    # tgt_data_segmentList = []
    # for d in segmentList:
    #     d_tmp = copy.deepcopy(d)
    #     d_tmp["srcText"], d_tmp[]

```

Rtf_api.py

```python
#!/usr/bin/env python
import logging
import os
import time
from fastapi import APIRouter
from models.result import make_fail, make_success
from models.tfl_vo import RtfFileRequest
from utils.oss_utils import download_file
from utils.tfl_utils import extact_tfl_from_rtf

router = APIRouter(include_in_schema=False)
logger = logging.getLogger(name=__name__)

## rtf 文件存储路径
RTF_FILES_FOLDER = 'rtf'
if not os.path.exists(RTF_FILES_FOLDER):
    os.makedirs(RTF_FILES_FOLDER)

@router.post('/tfls/extract-by-rtf')
async def extract_tfls_from_rtf(request: RtfFileRequest):
    """
    解析RTF文件中的tfl数据


    """
    start = time.time()

    try:
        # 1. 下载rtf 文件
        file_name = request.rtf_file.file_name
        file_key = request.rtf_file.file_key
        rtf_file_path = os.path.join(RTF_FILES_FOLDER, file_name)
        download_file(file_key, rtf_file_path)

        # 2. 解析rtf文件
        tfl_item = extact_tfl_from_rtf(rtf_file_path)
        logger.info(f"tfl is extracted successfully")

        # 3. 返回 tfl_item
        return make_success([tfl_item], int(time.time() - start))
    except Exception as e:
        logger.error(f"Error processing file: ", e)
        return make_fail(500, "Internal Server Error")
```

result.py

```python

def make_success(data: Any, time_cost: int) -> Union[ResultDTO]:
    return ResultDTO(
        success=True,
        code=0,
        result=data,
        time_cost=time_cost
    )

def make_fail(code: int, message: str) -> Union[ResultDTO]:
    update_biz_log_context(success=False, err_msg=message)
    return ResultDTO(success=False, code=code, message=message)

```
rtf_utils.py

```python
from striprtf.striprtf import rtf_to_text
import re

def extract_text_from_rtf(rtf_file_path):
    with open(rtf_file_path, 'r', encoding='utf-8') as file:
        rtf_content = file.read()
    plain_text = rtf_to_text(rtf_content)
    return plain_text

def extract_tbl_code(text):
    matches = re.findall(r"\d+(?:\.\d+)+", text)
    print(matches)
    return matches


def extact_tfl_from_rtf(rtf_path: str):
    print(f'rtf_path:${rtf_path}')
    text = extract_text_from_rtf(rtf_path)

    print(text)

    parts = text.split('\n|\n')  # 假设表头和表体之间有空行

    header = parts[0]  # 表头（第一段）
    footnote = parts[-1]  ## 脚注
    code = extract_tbl_code(header)
    body = '\n'.join(parts[1:])  # 表体（剩余部分）
    lines = body.split('|\n')
    cells = []
    csv_rows = []
    for line in lines:
        cells.append(line.split('|'))
        csv_rows.append(line.replace('|', ','))

    return {
        "code": code,
        "caption": header,
        "footnote": footnote,
        "cells": cells,
        "rowCount": len(cells),
        "colCount": len(cells[0]) if len(cells) > 0 else 0
    }
```



请求体：

```python
curl --location 'http://dev.bot.yiya-ai.com/tfls/extract-by-rtf' \
--header 'Content-Type: application/json' \
--data '{
    "rtf_file": {
        "file_name": "tfl/t14_3_4_1.rtf",
        "file_key": "tfl/t14_3_4_1.rtf"
    }}'
```





## 我的代码

```python

import json
import os
import uuid
from ast import literal_eval
from typing import List, Tuple, Dict, Any
from datetime import datetime
from random import randint
import pandas as pd
from utils.oss_utils import write_file, get_file_url


# --------------------------------------------------------------------------- #
#                            ★ 核心计分逻辑 ★                                 #
# --------------------------------------------------------------------------- #

def process_one_category(
        original_criteria: List[str],
        decomposed_criteria: List[str],
        all_evaluations: List[str],
        start_eval_index: int,
        base_score_per_item: float,
        id_prefix: str
) -> Tuple[Dict[str, Any], int]:
    """
    处理单一类别（“入选”或“排除”）的所有标准，并计算其总分。

    这个函数是计分的核心引擎。它会遍历给定类别的每一条原始标准，
    处理其由 LLM 拆分后的子标准，并根据 LLM 的评估结果和计分逻辑
    （入选/排除逻辑相反）来计算得分。

    Args:
        original_criteria (List[str]): 原始一级标准的列表 (e.g., ["年龄18-75岁", ...])。
        decomposed_criteria (List[str]): LLM 对每条一级标准拆分后的结果列表，
                                         每个元素是一个代表列表的字符串 (e.g., ["['年龄>=18岁', '年龄<=75岁']"])。
        all_evaluations (List[str]): 所有子项的 LLM 评估结果JSON字符串列表。
        start_eval_index (int): 在 `all_evaluations` 列表中开始处理的索引位置。
        base_score_per_item (float): 分配给每一条一级标准的基础分数。
        id_prefix (str): ID前缀，用于区分是 "inclusion" 还是 "exclusion"。

    Returns:
        Tuple[Dict[str, Any], int]:
            - 第一个元素是一个字典，包含了该类别下所有项目的详细计分明细和该类别的总分。
            - 第二个元素是处理完后，在 `all_evaluations` 列表中的新索引位置，用于下一个类别处理。
    """
    category_items = []
    category_total_final_score = 0.0
    current_eval_index = start_eval_index

    # 遍历每一条一级标准
    for i, original_criterion in enumerate(original_criteria):
        # ---- 步骤1: 解析 LLM 拆分出的子标准 ----
        decomposed_str = decomposed_criteria[i]
        try:
            # 使用 literal_eval 安全地将字符串 "['item1', 'item2']" 转为 Python 列表
            sub_criteria_list = literal_eval(decomposed_str)
            if not isinstance(sub_criteria_list, list):
                # 如果转换结果不是列表（例如，就是一个普通字符串），则将其包装成单元素列表
                sub_criteria_list = [str(decomposed_str)]
        except (ValueError, SyntaxError):
            # 如果解析失败，说明它可能不是一个合法的列表字符串，直接当作单元素列表处理
            sub_criteria_list = [decomposed_str]

        # ---- 步骤2: 将一级标准的分数，均分给其下的所有子标准 ----
        num_sub_items = len(sub_criteria_list)
        score_per_sub_item = (
            base_score_per_item / num_sub_items if num_sub_items else 0
        )

        sub_items_details = []
        main_item_final_score = 0.0

        # 遍历所有子标准，进行计分
        for j, sub_criterion_text in enumerate(sub_criteria_list):
            if current_eval_index >= len(all_evaluations):
                print(f"警告: 评估结果数量不足，标准 '{sub_criterion_text}' 无法计分。")
                break

            # ---- 步骤3: 解析 LLM 对当前子标准的评估结果 ----
            eval_str = all_evaluations[current_eval_index]
            try:
                eval_json = json.loads(eval_str)
                result_value = eval_json.get("result")
                reason_text = eval_json.get("reason", "理由缺失")
            except json.JSONDecodeError:
                result_value = "unknown"
                reason_text = "错误：无法解析评估结果的JSON字符串"

            # ---- 步骤4: 根据“正负计分”逻辑计算得分影响 ----
            score_impact = 0.0
            if result_value is True:
                # 如果是入选标准，符合(True)则加分；如果是排除标准，符合(True)则扣分。
                score_impact = score_per_sub_item if id_prefix == "inclusion" else -score_per_sub_item
            elif result_value is False:
                # 如果是入选标准，不符合(False)则扣分；如果是排除标准，不符合(False)则加分。
                score_impact = -score_per_sub_item if id_prefix == "inclusion" else score_per_sub_item
            # 如果 result_value 是 'unknown' 或其他值, score_impact 保持 0.0

            # 累加子项的得分到一级标准的总分上
            main_item_final_score += score_impact

            # 收集子项的详细信息，用于最终报告
            sub_items_details.append(
                {
                    "id": f"{id_prefix}_{i + 1}_sub_{j + 1}",
                    "description": sub_criterion_text,
                    "distributed_score": round(score_per_sub_item, 4),  # 该子项满分值
                    "evaluation_result": result_value,
                    "evaluation_reason": reason_text,  # LLM给出的判断理由
                    "score_impact": round(score_impact, 4),  # 实际得分/扣分
                }
            )
            current_eval_index += 1

        # 收集一级标准的汇总信息
        category_items.append(
            {
                "id": f"{id_prefix}_{i + 1}",
                "description": original_criterion,
                "original_score": round(base_score_per_item, 4),  # 该一级标准满分值
                "calculated_score": round(main_item_final_score, 4),  # 该一级标准最终得分
                "sub_items": sub_items_details,  # 包含其下所有子项的明细
            }
        )
        # 累加一级标准的得分到整个类别的总分上
        category_total_final_score += main_item_final_score

    # 构建并返回该类别的最终结果字典
    category_dict = {
        "items": category_items,
        "total_final_score": round(category_total_final_score, 4),
    }
    return category_dict, current_eval_index


def main(
        patient_id: str,
        all_criteria_list: Dict[str, List[str]],
        llm_decomposition_output: List[str],
        llm_evaluation_results: List[str],
) -> Dict[str, Any]:
    """
    为单个患者执行完整的计分流程。

    这是计分总入口函数。它接收一个患者的所有相关数据（原始标准、LLM拆分结果、
    LLM评估结果），然后调用 `process_one_category` 分别处理入选和排除标准，
    最终将所有结果汇总成一个代表该患者评估详情的 JSON 对象。

    Args:
        patient_id (str): 患者的唯一标识符。
        all_criteria_list (Dict[str, List[str]]): 包含 "入选标准" 和 "排除标准" 列表的字典。
        llm_decomposition_output (List[str]): LLM 对所有标准（先入选后排除）的拆分结果列表。
        llm_evaluation_results (List[str]): LLM 对所有子标准的评估结果列表。

    Returns:
        Dict[str, Any]: 一个包含患者ID、总分和详细评估分解的字典。
                        如果输入数据有误，则返回一个包含 "error" 信息的字典。
    """
    inclusion = all_criteria_list.get("入选标准", [])
    exclusion = all_criteria_list.get("排除标准", [])
    total_original = len(inclusion) + len(exclusion)

    # ---- 数据校验 ----
    if total_original == 0:
        return {"error": "未提供任何临床试验标准"}

    if len(llm_decomposition_output) != total_original:
        return {"error": "一级标准的数量与LLM拆分结果的数量不符"}

    # ---- 计分准备 ----
    # 计算每条一级标准的基础分值
    base_score = 100 / total_original
    # 将 LLM 的拆分结果也相应地分为入选和排除两部分
    inc_dec = llm_decomposition_output[: len(inclusion)]
    exc_dec = llm_decomposition_output[len(inclusion):]

    # ---- 执行计分 ----
    eval_idx = 0
    # 1. 处理入选标准
    inc_proc, eval_idx = process_one_category(
        inclusion, inc_dec, llm_evaluation_results, eval_idx, base_score, "inclusion"
    )
    # 2. 处理排除标准 (注意 eval_idx 已经更新，会从上一步结束的位置继续)
    exc_proc, _ = process_one_category(
        exclusion, exc_dec, llm_evaluation_results, eval_idx, base_score, "exclusion"
    )

    # ---- 汇总结果 ----
    # 返回一个结构化的字典，包含了该患者的所有评估信息
    return {
        "patient_id": patient_id,
        "final_score": round(inc_proc["total_final_score"] + exc_proc["total_final_score"], 4),
        "criteria_breakdown": {"inclusion": inc_proc, "exclusion": exc_proc},
    }


# --------------------------------------------------------------------------- #
#                      ★ 排名与Excel报告导出 ★                               #
# --------------------------------------------------------------------------- #
def rank_and_export(
        patient_json_strings: List[str],
        business_id: str = "patient-rank"  # 新增：允许传入业务标识，并设置默认值
) -> str:
    """
    对多位患者的计分结果进行排名，并导出为一份详细的 Excel 报告。
    报告会上传到OSS，并返回一个可供下载的URL。

    报告包含:
    1.  一个 "总榜" (Summary) Sheet，列出所有患者的排名、ID和总分。
    2.  为每一位患者创建一个单独的 Sheet，详细展示其每一条标准的得分明细。

    Args:
        patient_json_strings (List[str]): `main()` 函数生成的患者JSON字符串列表。
        business_id (str): 业务标识，用于生成文件名。

    Returns:
        str: 上传到OSS后的可下载的Excel文件URL。
    """
    if not patient_json_strings:
        raise ValueError("输入的患者JSON字符串列表为空，无法生成报告。")

    patients = [json.loads(s) for s in patient_json_strings]
    patients_sorted = sorted(patients, key=lambda d: d["final_score"], reverse=True)

    summary_rows = [
        {"排名": i + 1, "patient_id": p["patient_id"], "总分": p["final_score"]}
        for i, p in enumerate(patients_sorted)
    ]
    summary_df = pd.DataFrame(summary_rows)

    # --- 生成本地临时文件 ---
    temp_dir = "temp_reports"
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)

    # ==================== 代码修改核心区域 (START) ====================
    # 1. 生成 YYYYMMDDHHMMSS 格式的时间戳
    timestamp_str = datetime.now().strftime('%Y%m%d%H%M%S')

    # 2. 生成一个6位随机数
    random_num = randint(100000, 999999)

    # 3. 按照新规则拼接文件名
    # 格式: 业务标识-时间戳-随机数.文件后缀
    new_filename = f"{business_id}-{timestamp_str}-{random_num}.xlsx"

    # 4. 定义本地文件的完整路径
    local_filepath = os.path.join(temp_dir, new_filename)

    # 5. 定义OSS Key
    # 格式: 路径/新文件名
    oss_path_prefix = "patient_rank_reports"
    oss_key = f"{oss_path_prefix}/{new_filename}"
    # ==================== 在这里添加 print 语句 ====================
    print(f"[*] Generated Filename: {new_filename}")
    print(f"[*] Generated OSS Key: {oss_key}")
    # =============================================================
    # ==================== 代码修改核心区域 (END) ======================

    # --- 核心: 使用 pd.ExcelWriter 写入多个 Sheet ---
    try:
        with pd.ExcelWriter(local_filepath, engine="xlsxwriter") as writer:
            # (后续代码与原来保持一致，无需修改)
            # ...
            summary_df.to_excel(writer, sheet_name="总榜", index=False)

            workbook = writer.book
            bold_fmt = workbook.add_format({"bold": True})

            for p in patients_sorted:
                detail_rows = []
                for cat_name, cat in p["criteria_breakdown"].items():
                    for item in cat["items"]:
                        detail_rows.append(
                            {
                                "层级": "一级标准",
                                "类别": "入选" if "inclusion" in cat_name else "排除",
                                "标准ID": item["id"],
                                "描述": item["description"],
                                "得分": item["calculated_score"],
                                "判断理由": "",
                            }
                        )
                        for sub in item["sub_items"]:
                            detail_rows.append(
                                {
                                    "层级": "子项",
                                    "类别": "入选" if "inclusion" in cat_name else "排除",
                                    "标准ID": sub["id"],
                                    "描述": sub["description"],
                                    "得分": sub["score_impact"],
                                    "判断理由": sub["evaluation_reason"],
                                }
                            )

                df_detail = pd.DataFrame(detail_rows)
                sheet_name = p["patient_id"][:31] or "Patient_Detail"
                df_detail.to_excel(writer, sheet_name=sheet_name, index=False)

                worksheet = writer.sheets[sheet_name]
                for row_idx, level in enumerate(df_detail["层级"], start=1):
                    if level == "一级标准":
                        worksheet.set_row(row_idx, None, bold_fmt)

                for col_idx, col in enumerate(df_detail.columns):
                    max_len = max(df_detail[col].astype(str).map(len).max(), len(col)) + 2
                    worksheet.set_column(col_idx, col_idx, min(max_len, 60))

        # --- 上传到OSS ---
        # 使用新生成的 oss_key
        write_file(local_filepath, oss_key)

        # --- 获取下载链接 ---
        file_url = get_file_url(oss_key)

        return file_url
    finally:
        # --- 清理本地临时文件 ---
        if os.path.exists(local_filepath):
            os.remove(local_filepath)

```

帮我的代码仿照公司代码改造成 api 的形式，我已经想好名字了叫patient_rank_api.py

