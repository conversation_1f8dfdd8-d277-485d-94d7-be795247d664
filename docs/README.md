# 📚 yiya-ai-bot 项目文档

## 📋 文档概览

本目录包含 yiya-ai-bot 项目的完整技术文档，涵盖架构设计、部署指南、功能说明等各个方面。

## 📖 文档分类

### 🚀 快速开始
- [📖 快速开始指南](快速开始指南.md)
  - 本地开发环境搭建
  - 基本功能使用
  - 常见问题解决

### 🏗️ 架构和部署
- [🔧 项目启动流程说明](项目启动流程说明.md) **⭐ 核心文档**
  - 完整的技术架构解析
  - CI/CD 流水线详解
  - 常见疑惑解答（11个重要问题）
  - 故障排查指南

- [☸️ K8s架构说明](K8s架构说明.md)
  - Kubernetes 集群架构
  - 网络路由详解
  - Service、Ingress、Pod 关系
  - 访问路径分析

- [🚀 K8s部署说明](K8s部署说明.md)
  - 部署配置和操作指南
  - 资源配置说明
  - 部署最佳实践

### 📊 功能模块
- [📈 Excel导入系统使用说明](Excel导入系统使用说明.md)
  - Excel 文件处理系统
  - 异步任务处理
  - 进度跟踪机制

- [🔄 Redis_Celery_隔离机制详解](Redis_Celery_隔离机制详解.md)
  - 异步任务处理机制
  - Redis 队列管理
  - 任务隔离策略

### 🔧 开发指南
- [🌿 Git分支管理](new-branch-commands.md)
  - 分支创建和管理命令
  - 开发流程规范

## 🎯 推荐阅读顺序

### 新同事入门
1. **[项目启动流程说明](项目启动流程说明.md)** - 必读！理解整个项目架构
2. **[快速开始指南](快速开始指南.md)** - 搭建本地开发环境
3. **[K8s架构说明](K8s架构说明.md)** - 理解生产环境架构

### 运维人员
1. **[K8s架构说明](K8s架构说明.md)** - 网络架构和服务关系
2. **[K8s部署说明](K8s部署说明.md)** - 部署操作指南
3. **[项目启动流程说明](项目启动流程说明.md)** - CI/CD 流程和故障排查

### 开发人员
1. **[快速开始指南](快速开始指南.md)** - 本地开发环境
2. **[Excel导入系统使用说明](Excel导入系统使用说明.md)** - 核心功能模块
3. **[Redis_Celery_隔离机制详解](Redis_Celery_隔离机制详解.md)** - 异步任务机制

## 🔍 重要提示

### ⚠️ 常见疑惑解答
**[项目启动流程说明](项目启动流程说明.md)** 文档中包含了11个常见疑惑的详细解答：

1. 为什么本地的 `k8s/manifest2.yaml` 不起作用？
2. 为什么 `build.sh` 脚本配置与实际运行不符？
3. 为什么删除 Deployment 后重新运行流水线就能恢复？
4. `yiya-ai-bot.tgz` 文件是什么时候生成的？
5. 为什么流水线需要 "Python 构建" 步骤？
6. Kubernetes 分批发布的 YAML 配置到底是怎么来的？
7. 为什么 Service 需要手动创建？
8. Service 的端口配置是如何与应用代码对应的？
9. NodePort 和 ClusterIP 的区别是什么？
10. 网关服务的作用和工作原理？
11. 完整的网络路由链路是怎样的？

### 🎯 核心架构理解
- **基础设施与应用分离**：Service、Ingress 手动管理，Deployment 自动管理
- **CI/CD 全自动化**：代码提交后无需任何手动操作
- **网络路由分层**：外部域名 → Ingress → Service → Pod 的清晰分层
- **服务发现机制**：通过 Service 名称进行稳定的服务间调用

## 📞 文档维护

如果发现文档有误或需要补充，请：
1. 直接修改相应的 Markdown 文件
2. 提交 Pull Request
3. 联系文档维护人员

---

**💡 提示：建议先阅读 [项目启动流程说明](项目启动流程说明.md)，这是理解整个项目的基础！**
