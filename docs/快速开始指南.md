# Excel 导入系统 - 快速开始指南

## 🚀 快速部署

### 1. 安装依赖

```bash
# 安装新增的依赖包
pip install openpyxl>=3.1.0

# 或者安装所有依赖
pip install -r requirements_excel_import.txt
```

### 2. 检查系统健康状态

```bash
python health_check.py
```

确保所有检查都通过。

### 3. 启动 Celery Worker

```bash
# 方式一：使用启动脚本（推荐）
python start_celery.py

# 方式二：直接命令
celery -A celery_task worker --loglevel=INFO --pool=threads --concurrency=4
```

### 4. 启动 FastAPI 应用

```bash
# 根据你的项目启动方式
python main.py
```

### 5. 运行测试

```bash
python test_excel_import.py
```

## 📋 系统改造总结

### 主要变更

1. **配置文件更新**
   - `config/settings.py` - 添加 Excel 导入配置，使用 Redis db1
   - `config/redis_config.py` - 更新为使用 db1

2. **新增文件**
   - `celery_setup/` - Celery 设置目录
   - `celery_setup/celery_app.py` - Celery 应用实例
   - `celery_setup/excel_import_task.py` - Excel 导入任务
   - `utils/excel_to_oss_utils.py` - Excel 生成和 OSS 上传工具

3. **API 更新**
   - `api/monitor_excel2sql_api.py` - 改为轮询模式，返回 OSS 链接

4. **工具脚本**
   - `start_celery.py` - Celery 启动脚本
   - `health_check.py` - 系统健康检查
   - `test_excel_import.py` - 功能测试脚本

### 核心功能

✅ **异步处理** - 使用 Celery + Redis 实现异步任务处理  
✅ **轮询查询** - 客户端可轮询查询任务状态和进度  
✅ **OSS 存储** - 生成的 Excel 文件自动上传到 OSS  
✅ **进度跟踪** - 实时跟踪任务执行进度  
✅ **错误处理** - 完善的错误处理和日志记录  
✅ **资源清理** - 自动清理临时文件  

## 🔧 配置说明

### Redis 配置
- **Broker**: Redis db1
- **Result Backend**: Redis db1
- **连接**: `redis://:wtg2024@47.98.45.40:6379/1`

### OSS 配置
- **上传路径**: `excel_exports/`
- **文件命名**: `{task_id}_{timestamp}_{filename}`
- **访问权限**: 公开读取

### 数据库配置
- **来源**: Nacos 配置 `mysql-monitor-config`
- **操作**: 清空所有表 → 导入指定 Sheets

## 📊 API 接口

### 1. 提交任务
```http
POST /importer/refresh-from-url-async
Content-Type: application/json

{
    "excel_url": "https://example.com/file.xlsx",
    "sheets_to_import": ["Sheet1", "Sheet2"]
}
```

### 2. 查询状态
```http
GET /importer/import-status/{task_id}
```

### 3. 获取下载链接
```http
GET /importer/download-excel/{task_id}
```

## 🔍 监控和调试

### Celery 命令

```bash
# 查看 Worker 状态
celery -A celery_task inspect active

# 查看注册的任务
celery -A celery_task inspect registered

# 查看统计信息
celery -A celery_task inspect stats

# 清空队列（谨慎使用）
celery -A celery_task purge
```

### 日志查看

- **Celery Worker**: 控制台输出
- **FastAPI**: 应用日志
- **任务详情**: 包含完整的执行流程日志

## ⚠️ 注意事项

1. **Redis 连接**: 确保 Redis 服务运行在 db1
2. **并发控制**: 默认 4 个并发，可根据需要调整
3. **文件大小**: 大文件可能需要调整超时设置
4. **网络访问**: 确保能访问 Excel URL 和 OSS
5. **权限配置**: 确保数据库和 OSS 权限正确

## 🆘 故障排除

### 常见问题

**Q: 任务一直是 PENDING 状态**  
A: 检查 Celery Worker 是否启动，Redis 连接是否正常

**Q: 下载 Excel 失败**  
A: 检查 URL 是否可访问，网络连接是否正常

**Q: 数据库导入失败**  
A: 检查 Nacos 配置，数据库连接权限

**Q: OSS 上传失败**  
A: 检查 OSS 配置和上传权限

### 获取帮助

1. 运行 `python health_check.py` 检查系统状态
2. 查看 Celery Worker 日志
3. 检查 FastAPI 应用日志
4. 验证 Redis 和数据库连接

## 🎯 使用示例

```python
import requests
import time

# 1. 提交任务
response = requests.post('http://localhost:8000/importer/refresh-from-url-async', json={
    "excel_url": "https://example.com/data.xlsx",
    "sheets_to_import": ["用户数据", "产品信息"]
})

task_id = response.json()['data']['task_id']

# 2. 轮询状态
while True:
    status_response = requests.get(f'http://localhost:8000/importer/import-status/{task_id}')
    data = status_response.json()['data']
    
    if data['status'] == 'SUCCESS':
        print(f"任务完成！下载链接: {data['oss_url']}")
        break
    elif data['status'] == 'FAILURE':
        print(f"任务失败: {data['error']}")
        break
    else:
        print(f"进度: {data.get('progress', 0)}% - {data.get('current_stage', '')}")
        time.sleep(2)
```

---

🎉 **恭喜！你的 Excel 导入系统已经成功改造为 Celery + Redis 轮询方案！**
