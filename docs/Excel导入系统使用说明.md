# Excel 导入系统使用说明

## 概述

本系统已改造为基于 **Celery + Redis** 的异步轮询方案，支持：
- 异步 Excel 文件导入到 MySQL 数据库
- 从数据库生成新的 Excel 文件
- 自动上传到 OSS 并返回下载链接
- 轮询查询任务状态和进度

## 系统架构

```
客户端 → FastAPI → Celery Task → Redis Queue → Celery Worker
                                                      ↓
                                              1. 下载 Excel
                                              2. 导入 MySQL
                                              3. 生成新 Excel
                                              4. 上传到 OSS
                                              5. 返回链接
```

## 配置说明

### Redis 配置
- 使用 Redis **db1** 作为消息队列和结果存储
- 配置文件：`config/settings.py`

```python
REDIS_DB: int = 1  # 使用 db1
REDIS_TASK_DB: int = 1  # 结果存储也使用 db1
```

### OSS 配置
- 文件上传到 `excel_exports/` 目录
- 自动生成带时间戳的文件名
- 返回可访问的 OSS 链接

## 启动方式

### 1. 启动 Celery Worker

```bash
# 方式一：使用启动脚本（推荐）
python start_celery.py

# 方式二：直接使用 celery 命令
celery -A celery_task worker --loglevel=INFO --pool=threads --concurrency=4
```

### 2. 启动 FastAPI 应用

```bash
# 根据你的项目启动方式
python main.py
# 或
uvicorn main:app --reload
```

## API 接口

### 1. 提交异步导入任务

**POST** `/importer/refresh-from-url-async`

```json
{
    "excel_url": "https://example.com/data/file.xlsx",
    "sheets_to_import": ["用户信息", "产品列表"]
}
```

**响应：**
```json
{
    "code": 200,
    "data": {
        "task_id": "abc123-def456-ghi789",
        "message": "数据库刷新任务已成功提交，正在后台处理中。"
    }
}
```

### 2. 查询任务状态

**GET** `/importer/import-status/{task_id}`

**响应示例：**

**进行中：**
```json
{
    "code": 200,
    "data": {
        "task_id": "abc123-def456-ghi789",
        "status": "PROGRESS",
        "message": "任务正在进行中...",
        "current_stage": "导入数据到数据库",
        "progress": 30
    }
}
```

**完成：**
```json
{
    "code": 200,
    "data": {
        "task_id": "abc123-def456-ghi789",
        "status": "SUCCESS",
        "message": "任务成功完成。",
        "oss_url": "https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/excel_exports/...",
        "successful_imports": 2,
        "failed_imports": 0,
        "total_time_seconds": 45.67,
        "completed_at": "2024-01-15 14:30:25"
    }
}
```

### 3. 获取 Excel 下载链接

**GET** `/importer/download-excel/{task_id}`

**响应：**
```json
{
    "code": 200,
    "data": {
        "task_id": "abc123-def456-ghi789",
        "status": "success",
        "oss_url": "https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/excel_exports/...",
        "message": "Excel文件已生成，可以下载。"
    }
}
```

## 任务执行流程

1. **提交任务** - 客户端调用 `/refresh-from-url-async` 接口
2. **任务排队** - 任务进入 Redis 队列等待执行
3. **开始执行** - Celery Worker 接收任务并开始处理
4. **下载文件** - 从提供的 URL 下载 Excel 文件
5. **导入数据库** - 清空目标数据库并导入指定的 Sheets
6. **生成 Excel** - 从数据库重新生成 Excel 文件
7. **上传 OSS** - 将生成的文件上传到阿里云 OSS
8. **返回结果** - 任务完成，返回 OSS 下载链接

## 轮询建议

客户端应该定期轮询任务状态：

```javascript
async function pollTaskStatus(taskId) {
    const maxAttempts = 120; // 最多轮询 2 分钟
    const interval = 1000; // 每秒轮询一次
    
    for (let i = 0; i < maxAttempts; i++) {
        const response = await fetch(`/importer/import-status/${taskId}`);
        const result = await response.json();
        
        if (result.data.status === 'SUCCESS') {
            console.log('任务完成，OSS链接:', result.data.oss_url);
            return result.data.oss_url;
        } else if (result.data.status === 'FAILURE') {
            console.error('任务失败:', result.data.error);
            throw new Error(result.data.error);
        }
        
        console.log(`进度: ${result.data.progress}% - ${result.data.current_stage}`);
        await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    throw new Error('任务超时');
}
```

## 监控和调试

### 查看 Celery Worker 状态

```bash
# 查看活跃的 worker
celery -A celery_task inspect active

# 查看注册的任务
celery -A celery_task inspect registered

# 查看统计信息
celery -A celery_task inspect stats
```

### 查看任务队列

```bash
# 查看队列状态
celery -A celery_task inspect active_queues

# 清空队列（谨慎使用）
celery -A celery_task purge
```

### 日志查看

Celery Worker 的日志会直接输出到控制台，包含详细的任务执行信息。

## 注意事项

1. **Redis 连接** - 确保 Redis 服务正常运行且可访问
2. **OSS 配置** - 确保 OSS 配置正确，有上传权限
3. **MySQL 配置** - 确保 Nacos 中的数据库配置正确
4. **文件清理** - 系统会自动清理临时文件，无需手动处理
5. **并发控制** - 默认 4 个并发 worker，可根据需要调整
6. **任务超时** - 长时间运行的任务可能需要调整超时设置

## 故障排除

### 常见问题

1. **任务一直处于 PENDING 状态**
   - 检查 Celery Worker 是否正常运行
   - 检查 Redis 连接是否正常

2. **下载 Excel 文件失败**
   - 检查提供的 URL 是否可访问
   - 检查网络连接

3. **数据库导入失败**
   - 检查 Nacos 配置是否正确
   - 检查数据库连接权限

4. **OSS 上传失败**
   - 检查 OSS 配置和权限
   - 检查网络连接

### 获取帮助

如有问题，请查看：
1. Celery Worker 控制台日志
2. FastAPI 应用日志
3. Redis 连接状态
4. 数据库连接状态
