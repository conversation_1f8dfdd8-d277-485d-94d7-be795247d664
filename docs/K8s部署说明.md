# K8s 部署说明 - Excel 导入系统

## 📋 部署配置总览

### 1. Circus 配置更新

已在 `APP-META/docker-config/environment/common/app/conf/circus.ini` 中添加了 Celery Worker 配置：

```ini
[watcher:celery-excel-import]
working_dir=/root/{{app_name}}/target/{{app_name}}
cmd=celery -A celery_task worker --loglevel=INFO --pool=threads --concurrency=4
numprocesses=1
stop_signal=QUIT
stdout_stream.class=FileStream
stdout_stream.filename=/root/{{app_name}}/logs/celery_excel_import.log
stdout_stream.max_bytes=20971520
stdout_stream.backup_count=30
stderr_stream.class=FileStream
stderr_stream.filename=/root/{{app_name}}/logs/celery_excel_import_error.log
stderr_stream.max_bytes=20971520
stderr_stream.backup_count=30
```

### 2. 依赖包更新

已在 `requirements.txt` 中添加了必要的依赖：

```txt
# Excel 导入系统依赖
celery==5.5.3
redis==5.2.0
openpyxl==3.1.5
pymysql==1.1.1
```

## 🚀 部署后的进程结构

部署后，K8s Pod 中会运行以下进程：

1. **FastAPI 应用** (`{{app_name}}`)
   - 端口: 7860
   - 日志: `/root/{{app_name}}/logs/application.log`
   - 处理 HTTP 请求，提交 Celery 任务

2. **Celery Worker** (`celery-excel-import`)
   - 并发: 4 threads
   - 日志: `/root/{{app_name}}/logs/celery_excel_import.log`
   - 处理 Excel 导入任务

## 📊 监控和日志

### 日志文件位置

```bash
# FastAPI 应用日志
/root/{{app_name}}/logs/application.log
/root/{{app_name}}/logs/error.log

# Celery Worker 日志
/root/{{app_name}}/logs/celery_excel_import.log
/root/{{app_name}}/logs/celery_excel_import_error.log

# Circus 进程管理日志
/root/logs/circus.pid
```

### 查看日志命令

```bash
# 查看 Celery Worker 日志
kubectl logs <pod-name> -c <container-name> --tail=100 -f

# 或者进入 Pod 查看
kubectl exec -it <pod-name> -- tail -f /root/{{app_name}}/logs/celery_excel_import.log
```

## 🔧 环境变量配置

确保以下环境变量正确设置：

```yaml
env:
  - name: ENV
    value: "production"  # 或 "local"
  - name: NACOS_SERVER
    value: "mse-450596b0-p.nacos-ans.mse.aliyuncs.com:8848"
  - name: NACOS_NAMESPACE
    value: "yy-prod"  # 或其他环境
  - name: APP_LOG_DIR
    value: "/root/{{app_name}}/logs"
  - name: APP_STAGE
    value: "production"
```

## 🔍 健康检查

### 检查 Celery Worker 状态

```bash
# 进入 Pod
kubectl exec -it <pod-name> -- bash

# 检查 Celery Worker 状态
cd /root/{{app_name}}/target/{{app_name}}
celery -A celery_task inspect active
celery -A celery_task inspect stats
```

### 检查进程状态

```bash
# 查看 Circus 管理的进程
kubectl exec -it <pod-name> -- circusctl status

# 重启 Celery Worker
kubectl exec -it <pod-name> -- circusctl restart celery-excel-import

# 重启 FastAPI 应用
kubectl exec -it <pod-name> -- circusctl restart {{app_name}}
```

## 📋 部署检查清单

### 部署前检查

- [ ] `circus.ini` 配置已更新
- [ ] `requirements.txt` 包含所有依赖
- [ ] Nacos 中 `mysql-monitor-config` 配置正确
- [ ] Redis 服务可访问（db1）
- [ ] OSS 配置正确

### 部署后验证

- [ ] FastAPI 应用正常启动（端口 7860）
- [ ] Celery Worker 正常启动并连接到 Redis
- [ ] 日志文件正常生成
- [ ] API 接口可以正常访问

```bash
# 测试 API 接口
curl -X POST "http://<service-url>/importer/refresh-from-url-async" \
  -H "Content-Type: application/json" \
  -d '{
    "excel_url": "https://example.com/test.xlsx",
    "sheets_to_import": ["Sheet1"]
  }'
```

## ⚠️ 注意事项

### 1. 资源配置

建议为 Pod 配置适当的资源限制：

```yaml
resources:
  requests:
    memory: "1Gi"
    cpu: "500m"
  limits:
    memory: "2Gi"
    cpu: "1000m"
```

### 2. 存储配置

确保日志目录有足够的存储空间：

```yaml
volumeMounts:
  - name: logs-volume
    mountPath: /root/{{app_name}}/logs
```

### 3. 网络配置

确保 Pod 可以访问：
- Redis 服务（端口 6379）
- MySQL 数据库
- 阿里云 OSS
- Nacos 配置中心

### 4. 故障排除

如果 Celery Worker 启动失败：

1. 检查 Redis 连接
2. 检查 Nacos 配置
3. 检查依赖包是否安装完整
4. 查看详细错误日志

```bash
# 查看详细错误
kubectl exec -it <pod-name> -- cat /root/{{app_name}}/logs/celery_excel_import_error.log
```

## 🎯 性能调优

### Celery Worker 并发调整

根据实际负载调整并发数：

```ini
# 高负载环境
cmd=celery -A celery_task worker --loglevel=INFO --pool=threads --concurrency=8

# 低负载环境
cmd=celery -A celery_task worker --loglevel=INFO --pool=threads --concurrency=2
```

### 日志轮转配置

当前配置每个日志文件最大 20MB，保留 30 个备份文件：

```ini
stdout_stream.max_bytes=20971520    # 20MB
stdout_stream.backup_count=30       # 30 个备份
```

可根据存储空间调整这些参数。
