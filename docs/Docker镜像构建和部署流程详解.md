# Docker镜像构建和部署流程详解

## 概述

本文档详细说明了 yiya-ai-bot 项目的 Docker 镜像构建流程、配置文件管理机制以及部署过程中的关键环节。

## 镜像构建架构

### 双层镜像结构

项目采用**双层镜像结构**：

1. **基础镜像** (`yiya-ai-bot:latest`) - 包含环境、依赖和基础配置
2. **应用镜像** (`yiya-ai-bot:YYYYMMDDHHMM`) - 包含应用代码和最新配置

```
基础镜像 (yiya-ai-bot:latest)
├── Python 环境
├── 系统依赖
├── Python 包依赖
├── 基础配置文件 (可能过时)
└── 目录结构

应用镜像 (继承基础镜像)
├── 应用代码包 (yiya-ai-bot.tgz)
├── 最新配置文件 (覆盖基础镜像)
└── 启动脚本
```

## 构建流程详解

### 1. 基础镜像构建

**文件**: `APP-META/docker-config/Dockerfile_base`

**关键步骤**:
```dockerfile
# 创建目录结构
RUN mkdir -p $APP_HOME/$APP_NAME/bin && \
    mkdir -p $APP_HOME/$APP_NAME/conf && \
    mkdir -p $APP_HOME/$APP_NAME/target && \
    mkdir -p $APP_HOME/logs/app

# 复制应用框架文件
COPY environment/common/app/ $APP_HOME/$APP_NAME/

# 复制配置文件 (可能是旧版本)
COPY environment/common/app/conf/circus.ini $APP_HOME/$APP_NAME/conf/circus.ini

# 生成启动脚本
RUN echo "$APP_HOME/$APP_NAME/bin/appctl.sh start" >> $APP_HOME/start.sh && \
    echo "$APP_HOME/$APP_NAME/bin/preload.sh" >> $APP_HOME/health.sh
```

**生成的文件结构**:
```
/root/
├── start.sh                    # 应用启动脚本
├── health.sh                   # 健康检查脚本
└── yiya-ai-bot/
    ├── bin/                    # 应用控制脚本
    │   ├── appctl.sh          # 主启动脚本
    │   ├── preload.sh         # 预加载脚本
    │   └── setenv.sh          # 环境变量设置
    ├── conf/                   # 配置文件目录
    │   ├── circus.ini         # Circus进程管理配置
    │   └── requirements.txt   # Python依赖
    └── target/                 # 应用代码目录
```

### 2. 应用镜像构建

**文件**: `APP-META/docker-config/Dockerfile`

**关键步骤**:
```dockerfile
# 继承基础镜像
FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot:latest

# 安装额外依赖
RUN pip install celery==5.5.3 redis==6.2.0 ...

# 复制应用代码包
COPY $APP_NAME.tgz $APP_HOME/$APP_NAME/target/

# 覆盖配置文件 (确保使用最新配置)
COPY environment/common/app/conf/circus.ini $APP_HOME/$APP_NAME/conf/circus.ini
```

## 配置文件管理机制

### 配置文件来源优先级

1. **最高优先级**: 应用镜像中的配置文件
2. **中等优先级**: 基础镜像中的配置文件
3. **最低优先级**: 默认配置

### 关键配置文件

#### circus.ini - 进程管理配置

**位置**: `/root/yiya-ai-bot/conf/circus.ini`

**作用**: 定义需要启动的进程和服务

**关键配置**:
```ini
[watcher:yiya-ai-bot]
# FastAPI 应用进程
cmd=gunicorn app:app --workers 4 --timeout 180 ...

[watcher:yiya-ai-bot-celery]
# Celery Worker 进程
cmd=celery -A celery_task worker --loglevel=INFO --pool=threads --concurrency=4
```

## 启动流程

### 1. 容器启动

```bash
# Docker CMD 执行
/bin/bash -c /root/start.sh
```

### 2. start.sh 执行

```bash
# start.sh 内容 (由基础镜像生成)
/root/yiya-ai-bot/bin/appctl.sh start
```

### 3. appctl.sh 启动 Circus

```bash
# appctl.sh 启动 Circus 进程管理器
circusd --daemon /root/yiya-ai-bot/conf/circus.ini
```

### 4. Circus 启动服务

根据 `circus.ini` 配置启动：
- **FastAPI 应用** (端口 7860)
- **Celery Worker** (连接 Redis 队列)

## 部署流程

### 1. 代码提交

```bash
git push origin feature/medical_monitor
```

### 2. CI/CD 流水线

1. **代码拉取**: 从 Git 仓库拉取最新代码
2. **代码打包**: 创建 `yiya-ai-bot.tgz` (排除 APP-META 等目录)
3. **镜像构建**: 使用 `Dockerfile` 构建应用镜像
4. **镜像推送**: 推送到 ACR 镜像仓库
5. **K8s 部署**: 更新 K8s Deployment

### 3. K8s 部署

```yaml
# deployment.yaml
spec:
  containers:
  - name: yiya-ai-bot
    image: yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot:YYYYMMDDHHMM
    command: ["/bin/bash", "-c", "/root/start.sh"]
```

## 常见问题和解决方案

### 问题1: Celery Worker 未启动

**现象**: 任务一直处于 pending 状态

**原因**: `circus.ini` 中缺少 Celery Worker 配置

**解决方案**:
1. 检查 `APP-META/docker-config/environment/common/app/conf/circus.ini` 是否包含 Celery 配置
2. 确保 `Dockerfile` 中有覆盖配置文件的步骤
3. 重新构建和部署镜像

### 问题2: 配置文件未更新

**现象**: 线上配置与代码仓库不一致

**原因**: 基础镜像中的配置文件过时，应用镜像未覆盖

**解决方案**:
1. 在 `Dockerfile` 中添加配置文件复制步骤
2. 或重新构建基础镜像

### 问题3: 多环境 Worker 冲突

**现象**: 本地和线上 Worker 同时处理任务

**原因**: 连接同一个 Redis 实例和队列

**解决方案**:
1. 使用不同的 Redis 数据库 (db0, db1, db2...)
2. 使用不同的队列名称
3. 配置环境隔离

## 监控和调试

### 查看进程状态

```bash
# 进入容器
kubectl exec -it <pod-name> -- bash

# 查看 Circus 状态
circusctl status

# 查看所有进程
ps aux

# 查看日志
tail -f /root/yiya-ai-bot/logs/celery.log
tail -f /root/yiya-ai-bot/logs/application.log
```

### 检查配置文件

```bash
# 查看当前配置
cat /root/yiya-ai-bot/conf/circus.ini

# 检查配置文件行数 (判断是否为最新版本)
wc -l /root/yiya-ai-bot/conf/circus.ini
```

## 最佳实践

1. **配置文件管理**: 始终在应用镜像中覆盖基础镜像的配置文件
2. **环境隔离**: 不同环境使用不同的 Redis 数据库和队列
3. **版本控制**: 配置文件变更要与代码变更同步提交
4. **监控告警**: 设置 Celery Worker 健康检查和告警
5. **日志管理**: 确保日志输出到标准输出，便于 K8s 日志收集

## 相关文件

- `APP-META/docker-config/Dockerfile_base` - 基础镜像构建文件
- `APP-META/docker-config/Dockerfile` - 应用镜像构建文件
- `APP-META/docker-config/environment/common/app/conf/circus.ini` - Circus 配置
- `APP-META/docker-config/build.sh` - 镜像构建脚本
- `start_celery.py` - 本地 Celery 启动脚本
