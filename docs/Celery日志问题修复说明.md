# Celery 日志问题修复说明

## 🔍 问题描述

在K8s容器环境中使用circusd启动应用后，只能看到FastAPI的日志，而Celery Worker的日志不显示在容器日志中。

## 🔧 问题原因分析

### 1. 启动流程问题
- **容器启动**: `CMD ["/bin/bash", "-c", "/root/start.sh"]`
- **start.sh内容**: 由基础镜像生成，内容是 `/root/yiya-ai-bot/bin/appctl.sh start`
- **appctl.sh阻塞**: `appctl.sh start` 最后执行 `tail -f error.log`，只监控错误日志

### 2. 日志输出机制问题
- **K8s日志收集**: K8s只能收集容器主进程（PID 1）的标准输出和标准错误
- **Circus子进程**: Celery作为Circus管理的子进程，其日志默认只写入文件，不输出到标准输出
- **日志重定向**: 原始配置中的`copy_stdout=true`可能没有正确生效

### 3. 配置文件问题
- **circus.ini配置**: 原始配置使用`FileStream`只将日志写入文件
- **日志监控**: `appctl.sh`中的`tail -f`只监控error.log，不包含celery.log

## 🛠️ 解决方案

### 核心解决思路

你说得对！问题的关键是：
1. **基础镜像生成的start.sh**: 内容是 `/root/yiya-ai-bot/bin/appctl.sh start`
2. **appctl.sh的问题**: 最后只执行 `tail -f error.log`，不包含celery日志
3. **需要覆盖start.sh**: 在应用镜像中覆盖基础镜像的 `/root/start.sh`

### 方案1: 覆盖start.sh（推荐）

**新增文件**: `APP-META/docker-config/environment/common/app/start_enhanced.sh`

这个脚本会：
1. 检测容器环境
2. 启动应用服务（不阻塞在日志监控）
3. 同时监控所有日志文件并输出到容器标准输出

**修改Dockerfile**:
```dockerfile
# 复制增强版启动脚本，覆盖基础镜像中的 /root/start.sh
COPY environment/common/app/start_enhanced.sh $APP_HOME/start.sh
RUN chmod +x $APP_HOME/start.sh
```

### 方案2: 修改Circus配置（辅助）

**修改文件**: `APP-META/docker-config/environment/common/app/conf/circus.ini`

```ini
[watcher:{{app_name}}-celery]
working_dir=/root/{{app_name}}/target/{{app_name}}
cmd=bash -c "celery -A celery_task worker --loglevel=INFO --pool=threads --concurrency=4 --logfile=- 2>&1 | tee /root/{{app_name}}/logs/celery.log"
numprocesses=1
stop_signal=QUIT
# 直接输出到标准输出，同时保存到文件
stdout_stream.class=StdoutStream
stderr_stream.class=StdoutStream
```

### 方案2: 改进启动脚本

**修改文件**: `APP-META/docker-config/environment/common/app/bin/appctl.sh`

```bash
# 检查是否在容器环境中
if [ -f /.dockerenv ]; then
    echo "检测到容器环境，启动增强日志监控..."
    # 同时监控所有日志文件并输出到标准输出
    tail -f \
        ${APP_HOME}/logs/application.log \
        ${APP_HOME}/logs/error.log \
        ${APP_HOME}/logs/celery.log \
        ${APP_HOME}/logs/celery_error.log \
        2>/dev/null | while IFS= read -r line; do
            echo "[$(date '+%Y-%m-%d %H:%M:%S')] $line"
        done
else
    # 本地环境只监控错误日志
    tail -f ${APP_HOME}/logs/error.log
fi
```

### 方案3: 专用日志转发脚本

**新增文件**: `APP-META/docker-config/environment/common/app/bin/log_forwarder.sh`

```bash
#!/bin/bash
# 日志转发脚本，将所有应用日志转发到容器标准输出

LOG_DIR="${APP_HOME}/logs"
{
    tail -f "$LOG_DIR/application.log" | sed 's/^/[APP] /' &
    tail -f "$LOG_DIR/error.log" | sed 's/^/[ERROR] /' &
    tail -f "$LOG_DIR/celery.log" | sed 's/^/[CELERY] /' &
    tail -f "$LOG_DIR/celery_error.log" | sed 's/^/[CELERY-ERROR] /' &
    wait
}
```

## 📋 部署步骤

### 1. 重新构建镜像

```bash
# 在项目根目录执行
cd APP-META/docker-config
./build.sh
```

### 2. 更新K8s部署

```bash
# 更新部署配置
kubectl apply -f APP-META/k8s/deploy-paddle.yaml

# 或者重启现有Pod
kubectl rollout restart deployment/yiya-ai-ocr -n yiya-app
```

### 3. 验证修复效果

```bash
# 查看Pod日志
kubectl logs -f deployment/yiya-ai-ocr -n yiya-app

# 应该能看到类似以下的日志输出：
# [APP] INFO: Application startup complete.
# [CELERY] [2025-07-25 10:30:00,123: INFO/MainProcess] Connected to redis://...
# [CELERY] [2025-07-25 10:30:00,456: INFO/MainProcess] mingle: searching for neighbor nodes...
```

## 🔍 故障排查

### 1. 检查Circus进程状态

```bash
# 进入容器
kubectl exec -it <pod-name> -n yiya-app -- bash

# 查看Circus状态
circusctl status

# 应该看到两个进程都在运行：
# yiya-ai-bot: active
# yiya-ai-bot-celery: active
```

### 2. 检查日志文件

```bash
# 检查日志文件是否存在
ls -la /root/yiya-ai-bot/logs/

# 查看Celery日志内容
tail -f /root/yiya-ai-bot/logs/celery.log
```

### 3. 检查Celery连接

```bash
# 测试Celery连接
cd /root/yiya-ai-bot/target/yiya-ai-bot
python -c "from celery_task.celery import celery_app; print(celery_app.control.inspect().stats())"
```

## 📝 注意事项

1. **环境变量**: 确保Redis连接配置正确
2. **权限问题**: 确保日志目录有写权限
3. **资源限制**: 监控容器资源使用情况
4. **日志轮转**: 配置适当的日志轮转策略避免磁盘空间不足

## 🔄 回滚方案

如果修改后出现问题，可以快速回滚：

```bash
# 回滚到上一个版本
kubectl rollout undo deployment/yiya-ai-ocr -n yiya-app

# 或者使用之前的镜像版本
kubectl set image deployment/yiya-ai-ocr yiya-ai-ocr=<previous-image> -n yiya-app
```

## 📊 监控建议

1. **日志监控**: 设置日志告警，监控Celery Worker健康状态
2. **任务监控**: 监控Celery任务执行情况和队列长度
3. **资源监控**: 监控容器CPU和内存使用情况
