#!/usr/bin/env python3
"""
Redis连接测试脚本
用于测试Redis连接的稳定性和超时配置
"""

import redis
import time
import threading
from config.settings import settings

def test_redis_connection():
    """测试Redis连接"""
    print("=== Redis连接测试 ===")
    
    # 创建Redis连接池
    pool = redis.ConnectionPool(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        password=settings.REDIS_PASSWORD,
        db=settings.REDIS_DB,
        socket_connect_timeout=30,
        socket_timeout=60,
        socket_keepalive=True,
        # socket_keepalive_options={
        #     'TCP_KEEPIDLE': 600,
        #     'TCP_KEEPINTVL': 30,
        #     'TCP_KEEPCNT': 3,
        # },
        max_connections=50,
        retry_on_timeout=True,
        decode_responses=True
    )
    
    client = redis.Redis(connection_pool=pool)
    
    try:
        # 测试基本连接
        print("1. 测试基本连接...")
        pong = client.ping()
        print(f"   Ping响应: {pong}")
        
        # 测试写入和读取
        print("2. 测试写入和读取...")
        test_key = "test_connection_key"
        test_value = f"test_value_{int(time.time())}"
        client.set(test_key, test_value, ex=60)  # 60秒过期
        retrieved_value = client.get(test_key)
        print(f"   写入值: {test_value}")
        print(f"   读取值: {retrieved_value}")
        
        # 测试连接信息
        print("3. 测试连接信息...")
        info = client.info('clients')
        print(f"   当前连接数: {info.get('connected_clients', 'N/A')}")
        print(f"   阻塞连接数: {info.get('blocked_clients', 'N/A')}")
        
        # 测试长时间操作
        print("4. 测试长时间操作...")
        start_time = time.time()
        for i in range(10):
            client.set(f"test_long_op_{i}", f"value_{i}", ex=60)
            time.sleep(1)  # 模拟长时间操作
        end_time = time.time()
        print(f"   长时间操作完成，耗时: {end_time - start_time:.2f}秒")
        
        # 清理测试数据
        client.delete(test_key)
        for i in range(10):
            client.delete(f"test_long_op_{i}")
        
        print("✅ Redis连接测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ Redis连接测试失败: {e}")
        return False
    finally:
        pool.disconnect()

def test_concurrent_connections():
    """测试并发连接"""
    print("\n=== 并发连接测试 ===")
    
    def worker(worker_id):
        """工作线程"""
        try:
            client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                password=settings.REDIS_PASSWORD,
                db=settings.REDIS_DB,
                socket_connect_timeout=30,
                socket_timeout=60,
                socket_keepalive=True,
                decode_responses=True
            )
            
            # 执行一些操作
            for i in range(5):
                key = f"worker_{worker_id}_key_{i}"
                value = f"worker_{worker_id}_value_{i}"
                client.set(key, value, ex=60)
                retrieved = client.get(key)
                assert retrieved == value, f"值不匹配: {retrieved} != {value}"
                client.delete(key)
                time.sleep(0.1)
            
            print(f"   Worker {worker_id}: 完成")
            return True
            
        except Exception as e:
            print(f"   Worker {worker_id}: 失败 - {e}")
            return False
    
    # 创建多个线程
    threads = []
    for i in range(5):
        thread = threading.Thread(target=worker, args=(i,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print("✅ 并发连接测试完成！")

def test_celery_redis_config():
    """测试Celery Redis配置"""
    print("\n=== Celery Redis配置测试 ===")
    
    try:
        from celery_task.celeryconfig import broker_url, result_backend
        print(f"Broker URL: {broker_url}")
        print(f"Result Backend: {result_backend}")
        
        # 测试broker连接
        import redis
        from urllib.parse import urlparse
        
        parsed_broker = urlparse(broker_url)
        broker_client = redis.Redis(
            host=parsed_broker.hostname,
            port=parsed_broker.port,
            password=parsed_broker.password,
            db=int(parsed_broker.path.split('?')[0].lstrip('/')),
            socket_connect_timeout=30,
            socket_timeout=60,
            decode_responses=True
        )
        
        broker_ping = broker_client.ping()
        print(f"Broker连接测试: {broker_ping}")
        
        # 测试result backend连接
        parsed_result = urlparse(result_backend)
        result_client = redis.Redis(
            host=parsed_result.hostname,
            port=parsed_result.port,
            password=parsed_result.password,
            db=int(parsed_result.path.split('?')[0].lstrip('/')),
            socket_connect_timeout=30,
            socket_timeout=60,
            decode_responses=True
        )
        
        result_ping = result_client.ping()
        print(f"Result Backend连接测试: {result_ping}")
        
        print("✅ Celery Redis配置测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ Celery Redis配置测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始Redis连接测试...\n")
    
    # 运行所有测试
    test1 = test_redis_connection()
    test_concurrent_connections()
    test2 = test_celery_redis_config()
    
    print(f"\n=== 测试结果 ===")
    print(f"基本连接测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"Celery配置测试: {'✅ 通过' if test2 else '❌ 失败'}")
    
    if test1 and test2:
        print("\n🎉 所有测试通过！Redis连接配置正常。")
    else:
        print("\n⚠️  部分测试失败，请检查Redis配置。")
