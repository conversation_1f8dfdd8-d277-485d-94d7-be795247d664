from docx import Document
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
from lxml import etree

def create_doc_with_toc(path: str):
    doc = Document()
    # 添加示例标题
    doc.add_heading('一级标题示例', level=1)
    doc.add_heading('二级标题示例', level=2)
    # 插入目录
    p = doc.add_paragraph()
    fld = OxmlElement('w:fldSimple')
    fld.set(qn('w:instr'), 'TOC \\o "1-2" \\h \\z \\u')
    p._p.append(fld)
    # 启用打开时自动更新目录
    namespace = "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}"
    update_el = etree.SubElement(doc.settings.element, f"{namespace}updateFields")
    update_el.set(f"{namespace}val", "true")
    # 保存文档
    doc.save(path)

if __name__ == '__main__':
    create_doc_with_toc('output_with_toc.docx')

