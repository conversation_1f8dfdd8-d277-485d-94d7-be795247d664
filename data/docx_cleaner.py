"""
docx_cleaner.py

功能模块：
- 将 docx 文档中的软换行（\n）转换为硬换行（新段落）
- 保留原段落和字体格式
- 去除多余的空格和换行符
- 中英字符自动识别分段并设置字体

使用方法：
    from docx_cleaner import clean_docx_breaks

    clean_docx_breaks("input.docx", "output.docx")
"""
from docx import Document
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
import re


def clean_docx_breaks(input_file: str, output_file: str):
    doc = Document(input_file)
    paragraphs_to_process = []

    for i, paragraph in enumerate(doc.paragraphs):
        p_xml = paragraph._element
        breaks = p_xml.xpath('.//w:br')

        if breaks or '\n' in paragraph.text:
            paragraphs_to_process.append((i, paragraph))

    for para_index, paragraph in reversed(paragraphs_to_process):
        process_paragraph_with_breaks(paragraph, doc, para_index)

    doc.save(output_file)


def save_paragraph_format(paragraph):
    pf = paragraph.paragraph_format
    return {
        'style': paragraph.style,
        'alignment': pf.alignment,
        'left_indent': pf.left_indent,
        'right_indent': pf.right_indent,
        'first_line_indent': pf.first_line_indent,
        'space_before': pf.space_before,
        'space_after': pf.space_after,
        'line_spacing': pf.line_spacing,
        'line_spacing_rule': pf.line_spacing_rule,
        'keep_together': pf.keep_together,
        'keep_with_next': pf.keep_with_next,
        'page_break_before': pf.page_break_before,
        'widow_control': pf.widow_control,
    }


def apply_paragraph_format(paragraph, format_info):
    pf = paragraph.paragraph_format
    try:
        paragraph.style = format_info.get('style')
        pf.alignment = format_info.get('alignment')
        pf.left_indent = format_info.get('left_indent')
        pf.right_indent = format_info.get('right_indent')
        pf.first_line_indent = format_info.get('first_line_indent')
        pf.space_before = format_info.get('space_before')
        pf.space_after = format_info.get('space_after')
        pf.line_spacing = format_info.get('line_spacing')
        pf.line_spacing_rule = format_info.get('line_spacing_rule')
        pf.keep_together = format_info.get('keep_together')
        pf.keep_with_next = format_info.get('keep_with_next')
        pf.page_break_before = format_info.get('page_break_before')
        pf.widow_control = format_info.get('widow_control')
    except Exception as e:
        print(f"应用段落格式出错: {e}")


def is_chinese_char(char): return '\u4e00' <= char <= '\u9fff'
def is_english_or_number(char): return char.isalnum() and char.isascii()


def split_text_by_language(text):
    if not text:
        return []

    parts, current_part, current_type = [], "", None
    for char in text:
        char_type = "chinese" if is_chinese_char(char) else "english" if is_english_or_number(char) else "other"
        if current_type == char_type:
            current_part += char
        else:
            if current_part:
                parts.append({'text': current_part, 'type': current_type})
            current_part = char
            current_type = char_type
    if current_part:
        parts.append({'text': current_part, 'type': current_type})
    return parts


def process_paragraph_with_breaks(paragraph, doc, para_index):
    original_format = save_paragraph_format(paragraph)
    content_parts = []

    for run in paragraph.runs:
        text = run.text.replace('\r\n', '\n').replace('\r', '\n')
        parts = text.split('\n')
        for i, part in enumerate(parts):
            if part.strip():
                for seg in split_text_by_language(part.strip()):
                    content_parts.append({
                        'type': 'text',
                        'content': seg['text'],
                        'text_type': seg['type'],
                        'font_size': run.font.size,
                        'bold': run.font.bold,
                        'italic': run.font.italic,
                        'underline': run.font.underline,
                        'color': run.font.color.rgb if run.font.color.rgb else None
                    })
            if i < len(parts) - 1:
                content_parts.append({'type': 'break'})

    if not content_parts:
        return

    paragraph.clear()
    current_para = paragraph

    for part in content_parts:
        if part['type'] == 'break':
            current_para = insert_paragraph_after(doc, current_para)
            apply_paragraph_format(current_para, original_format)
        else:
            run = current_para.add_run(part['content'])
            apply_font_format(run, part)

    apply_paragraph_format(paragraph, original_format)


def insert_paragraph_after(doc, reference_paragraph):
    new_p = parse_xml(f'<w:p {nsdecls("w")}></w:p>')
    reference_paragraph._element.addnext(new_p)

    for i, para in enumerate(doc.paragraphs):
        if para._element == reference_paragraph._element:
            if i + 1 < len(doc.paragraphs):
                return doc.paragraphs[i + 1]
            break
    return doc.add_paragraph()


def apply_font_format(run, info):
    try:
        run.font.name = '宋体' if info.get('text_type') in ['chinese', 'other'] else 'Times New Roman'
        run.font.size = info.get('font_size')
        run.font.bold = info.get('bold')
        run.font.italic = info.get('italic')
        run.font.underline = info.get('underline')
        if info.get('color'):
            run.font.color.rgb = info['color']
    except Exception as e:
        print(f"应用字体格式出错: {e}")


# 测试入口
if __name__ == "__main__":
    clean_docx_breaks("input.docx", "output.docx")