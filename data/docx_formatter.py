"""
docx_formatter.py - Word文档格式化工具

主要功能：
1. 软换行转硬换行：将文档中的软回车（换行符）转换为硬回车（段落分隔）
2. 去除多余空格：清理文档中的冗余空白字符
3. 智能字体设置：根据文本类型（中文/英文）自动设置合适的字体
4. 格式保持：在处理过程中保持原有的段落和字体格式
"""

from docx import Document
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls, qn
from docx.shared import Pt, Inches
import copy
import re


class DocxFormatter:
    """Word文档格式化器"""

    def __init__(self):
        self.chinese_font = '宋体'
        self.english_font = 'Times New Roman'
        self.default_font = '宋体'

    def format_document(self, input_file, output_file,
                       convert_soft_breaks=True,
                       remove_extra_spaces=True,
                       chinese_font='宋体',
                       english_font='Times New Roman'):
        """
        格式化Word文档

        Args:
            input_file (str): 输入文件路径
            output_file (str): 输出文件路径
            convert_soft_breaks (bool): 是否转换软换行为硬换行
            remove_extra_spaces (bool): 是否去除多余空格
            chinese_font (str): 中文字体名称
            english_font (str): 英文字体名称
        """
        self.chinese_font = chinese_font
        self.english_font = english_font
        self.remove_extra_spaces = remove_extra_spaces  # 保存设置

        doc = Document(input_file)

        if convert_soft_breaks:
            self._convert_soft_breaks(doc)
        elif remove_extra_spaces:
            # 如果只需要去除空格而不转换软换行，单独处理
            self._remove_extra_spaces(doc)

        doc.save(output_file)
        print(f"文档格式化完成，已保存到: {output_file}")

    def _convert_soft_breaks(self, doc):
        """转换软换行为硬换行"""
        # 收集需要处理的段落（从后往前处理避免索引问题）
        paragraphs_to_process = []

        for i, paragraph in enumerate(doc.paragraphs):
            p_xml = paragraph._element
            # 查找所有的break元素
            breaks = p_xml.xpath('.//w:br')

            if breaks or '\n' in paragraph.text:
                paragraphs_to_process.append((i, paragraph))

        # 从后往前处理段落
        for para_index, paragraph in reversed(paragraphs_to_process):
            self._process_paragraph_with_breaks(paragraph, doc, para_index)

    def _remove_extra_spaces(self, doc):
        """去除多余空格"""
        for paragraph in doc.paragraphs:
            # 处理段落级别的空格
            if paragraph.text.strip():  # 非空段落
                self._clean_paragraph_spaces(paragraph)

    def _clean_paragraph_spaces(self, paragraph):
        """清理段落中的多余空格"""
        # 收集所有run的内容和格式
        runs_info = []
        full_text = ""

        for run in paragraph.runs:
            if run.text:
                runs_info.append({
                    'text': run.text,
                    'font_size': run.font.size,
                    'bold': run.font.bold,
                    'italic': run.font.italic,
                    'underline': run.font.underline,
                    'color': run.font.color.rgb if run.font.color.rgb else None
                })
                full_text += run.text

        if not full_text.strip():
            return

        # 清理空格：多个连续空格替换为单个空格，去除行首行尾空格
        cleaned_text = re.sub(r'\s+', ' ', full_text).strip()

        if cleaned_text != full_text:
            # 保存段落格式
            original_format = self._save_paragraph_format(paragraph)

            # 清空段落
            paragraph.clear()

            # 按语言类型重新添加文本
            text_parts = self._split_text_by_language(cleaned_text)
            for text_part in text_parts:
                run = paragraph.add_run(text_part['text'])
                # 应用第一个run的格式（简化处理）
                if runs_info:
                    self._apply_font_format(run, {
                        'text_type': text_part['type'],
                        **runs_info[0]
                    })

            # 恢复段落格式
            self._apply_paragraph_format(paragraph, original_format)

    def _clean_text_spaces(self, text):
        """清理文本中的多余空格"""
        if not hasattr(self, 'remove_extra_spaces') or not self.remove_extra_spaces:
            return text

        # 清理空格：多个连续空格替换为单个空格，去除行首行尾空格
        cleaned_text = re.sub(r'\s+', ' ', text).strip()
        return cleaned_text

    def _save_paragraph_format(self, paragraph):
        """保存段落的完整格式信息"""
        pf = paragraph.paragraph_format

        format_info = {
            'style': paragraph.style,
            'alignment': pf.alignment,
            'left_indent': pf.left_indent,
            'right_indent': pf.right_indent,
            'first_line_indent': pf.first_line_indent,
            'space_before': pf.space_before,
            'space_after': pf.space_after,
            'line_spacing': pf.line_spacing,
            'line_spacing_rule': pf.line_spacing_rule,
            'keep_together': pf.keep_together,
            'keep_with_next': pf.keep_with_next,
            'page_break_before': pf.page_break_before,
            'widow_control': pf.widow_control,
        }

        return format_info

    def _apply_paragraph_format(self, paragraph, format_info):
        """应用段落格式"""
        try:
            pf = paragraph.paragraph_format

            if format_info.get('style'):
                paragraph.style = format_info['style']

            if format_info.get('alignment') is not None:
                pf.alignment = format_info['alignment']

            if format_info.get('left_indent') is not None:
                pf.left_indent = format_info['left_indent']

            if format_info.get('right_indent') is not None:
                pf.right_indent = format_info['right_indent']

            if format_info.get('first_line_indent') is not None:
                pf.first_line_indent = format_info['first_line_indent']

            if format_info.get('space_before') is not None:
                pf.space_before = format_info['space_before']

            if format_info.get('space_after') is not None:
                pf.space_after = format_info['space_after']

            if format_info.get('line_spacing') is not None:
                pf.line_spacing = format_info['line_spacing']

            if format_info.get('line_spacing_rule') is not None:
                pf.line_spacing_rule = format_info['line_spacing_rule']

            if format_info.get('keep_together') is not None:
                pf.keep_together = format_info['keep_together']

            if format_info.get('keep_with_next') is not None:
                pf.keep_with_next = format_info['keep_with_next']

            if format_info.get('page_break_before') is not None:
                pf.page_break_before = format_info['page_break_before']

            if format_info.get('widow_control') is not None:
                pf.widow_control = format_info['widow_control']

        except Exception as e:
            print(f"应用段落格式时出错: {e}")

    def _is_chinese_char(self, char):
        """判断字符是否为中文字符"""
        return '\u4e00' <= char <= '\u9fff'

    def _is_english_or_number(self, char):
        """判断字符是否为英文字母或数字"""
        return char.isalnum() and char.isascii()

    def _split_text_by_language(self, text):
        """按照语言类型分割文本"""
        if not text:
            return []

        parts = []
        current_part = ""
        current_type = None

        for char in text:
            if self._is_chinese_char(char):
                char_type = "chinese"
            elif self._is_english_or_number(char):
                char_type = "english"
            else:
                char_type = "other"

            if current_type is None:
                current_type = char_type
                current_part = char
            elif current_type == char_type:
                current_part += char
            else:
                # 类型改变，保存当前部分
                parts.append({
                    'text': current_part,
                    'type': current_type
                })
                current_part = char
                current_type = char_type

        # 添加最后一部分
        if current_part:
            parts.append({
                'text': current_part,
                'type': current_type
            })

        return parts

    def _process_paragraph_with_breaks(self, paragraph, doc, para_index):
        """处理包含break的段落"""
        # 收集段落中的所有内容和格式信息
        content_parts = []

        # 保存原始段落的完整格式
        original_format = self._save_paragraph_format(paragraph)

        # 遍历所有runs，处理软回车
        for run in paragraph.runs:
            if '\n' in run.text or '\r' in run.text:
                # 分割包含换行的文本
                parts = run.text.replace('\r\n', '\n').replace('\r', '\n').split('\n')
                for i, part in enumerate(parts):
                    if part:  # 非空文本
                        # 清理空格（如果启用）
                        cleaned_part = self._clean_text_spaces(part)
                        if cleaned_part:  # 清理后仍有内容
                            # 按语言类型分割文本
                            text_parts = self._split_text_by_language(cleaned_part)
                            for text_part in text_parts:
                                content_parts.append({
                                    'type': 'text',
                                    'content': text_part['text'],
                                    'text_type': text_part['type'],
                                    'font_size': run.font.size,
                                    'bold': run.font.bold,
                                    'italic': run.font.italic,
                                    'underline': run.font.underline,
                                    'color': run.font.color.rgb if run.font.color.rgb else None
                                })
                    if i < len(parts) - 1:  # 不是最后一部分，添加换行标记
                        content_parts.append({'type': 'break'})
            else:
                # 普通文本，无换行
                if run.text:
                    # 清理空格（如果启用）
                    cleaned_text = self._clean_text_spaces(run.text)
                    if cleaned_text:  # 清理后仍有内容
                        # 按语言类型分割文本
                        text_parts = self._split_text_by_language(cleaned_text)
                        for text_part in text_parts:
                            content_parts.append({
                                'type': 'text',
                                'content': text_part['text'],
                                'text_type': text_part['type'],
                                'font_size': run.font.size,
                                'bold': run.font.bold,
                                'italic': run.font.italic,
                                'underline': run.font.underline,
                                'color': run.font.color.rgb if run.font.color.rgb else None
                            })

        if not content_parts:
            return

        # 清空原段落
        paragraph.clear()

        # 重建段落内容
        current_paragraph = paragraph

        for part in content_parts:
            if part['type'] == 'break':
                # 创建新段落
                current_paragraph = self._insert_paragraph_after(doc, current_paragraph)
                # 应用完整格式到新段落
                self._apply_paragraph_format(current_paragraph, original_format)
            else:
                # 添加文本到当前段落
                run = current_paragraph.add_run(part['content'])
                self._apply_font_format(run, part)

        # 确保第一个段落也应用了格式
        self._apply_paragraph_format(paragraph, original_format)

    def _insert_paragraph_after(self, doc, reference_paragraph):
        """在指定段落后插入新段落"""
        # 创建新的段落元素
        new_p = parse_xml(f'<w:p {nsdecls("w")}></w:p>')

        # 在参考段落后插入
        reference_paragraph._element.addnext(new_p)

        # 重新获取文档段落列表中的新段落对象
        # 找到参考段落的索引
        ref_index = None
        for i, para in enumerate(doc.paragraphs):
            if para._element == reference_paragraph._element:
                ref_index = i
                break

        if ref_index is not None and ref_index + 1 < len(doc.paragraphs):
            return doc.paragraphs[ref_index + 1]
        else:
            # 如果找不到，就添加到文档末尾
            return doc.add_paragraph()

    def _apply_font_format(self, run, format_info):
        """应用字体格式，根据文本类型设置不同字体"""
        try:
            # 根据文本类型设置字体
            text_type = format_info.get('text_type', 'other')

            if text_type == 'chinese':
                run.font.name = self.chinese_font
            elif text_type == 'english':
                run.font.name = self.english_font
            else:
                # 其他字符（标点符号等）使用默认字体
                run.font.name = self.default_font

            # 应用其他格式
            if format_info.get('font_size'):
                run.font.size = format_info['font_size']
            if format_info.get('bold') is not None:
                run.font.bold = format_info['bold']
            if format_info.get('italic') is not None:
                run.font.italic = format_info['italic']
            if format_info.get('underline') is not None:
                run.font.underline = format_info['underline']
            if format_info.get('color'):
                run.font.color.rgb = format_info['color']

        except Exception as e:
            print(f"应用字体格式时出错: {e}")


# 便捷函数
def format_docx(input_file, output_file,
                convert_soft_breaks=True,
                remove_extra_spaces=True,
                chinese_font='宋体',
                english_font='Times New Roman'):
    """
    格式化Word文档的便捷函数

    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径
        convert_soft_breaks (bool): 是否转换软换行为硬换行
        remove_extra_spaces (bool): 是否去除多余空格
        chinese_font (str): 中文字体名称
        english_font (str): 英文字体名称
    """
    formatter = DocxFormatter()
    formatter.format_document(
        input_file, output_file,
        convert_soft_breaks, remove_extra_spaces,
        chinese_font, english_font
    )


# 使用示例
if __name__ == "__main__":
    # 测试你的文件
    format_docx(
        input_file="oss-20250527014835-91df9859-c544-4a1c-b662-ae565cafaf2d (1).docx",
        output_file="0527-1.docx",
        convert_soft_breaks=True,
        remove_extra_spaces=True
    )
