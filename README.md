# 🚀 yiya-ai-bot

## 📋 项目概述

yiya-ai-bot 是一个基于 FastAPI 和 Celery 的 AI 服务应用，主要提供文档处理、OCR识别、翻译等智能服务。

### 🎯 主要功能
- 📊 Excel 导入和处理
- 📄 PDF 文档解析和翻译
- 🔍 OCR 文字识别
- 📝 文档对比和差异分析
- 🖼️ 图片转 Markdown
- 🏥 医疗报告生成
- 📋 协议文档处理

### 🛠️ 技术栈
- **Web 框架**: FastAPI
- **异步任务**: Celery + Redis
- **容器化**: Docker
- **进程管理**: Circus
- **部署**: Kubernetes
- **CI/CD**: 阿里云 Codeup

## 📚 文档导航

### 🚀 快速开始
- [📖 快速开始指南](docs/快速开始指南.md) - 本地开发环境搭建和基本使用

### 🏗️ 架构和部署
- [🔧 项目启动流程说明](docs/项目启动流程说明.md) - **⭐ 核心文档** - 完整的技术架构和CI/CD流程
- [☸️ K8s架构说明](docs/K8s架构说明.md) - Kubernetes集群架构和网络路由详解
- [🚀 K8s部署说明](docs/K8s部署说明.md) - 部署配置和操作指南

### 📊 功能模块
- [📈 Excel导入系统使用说明](docs/Excel导入系统使用说明.md) - Excel文件处理系统详解
- [🔄 Redis_Celery_隔离机制详解](docs/Redis_Celery_隔离机制详解.md) - 异步任务处理机制

### 🔧 开发指南
- [🌿 Git分支管理](docs/new-branch-commands.md) - 分支创建和管理命令

## 🌐 访问地址

### 开发环境
- **直接访问**: http://dev.bot.yiya-ai.com
- **网关访问**: https://dev-gw.yiya-ai.com (需要Authorization)
- **API文档**: http://dev.bot.yiya-ai.com/docs

### 本地开发
- **应用地址**: http://localhost:7860
- **Swagger UI**: http://localhost:7860/docs
- **ReDoc**: http://localhost:7860/redoc

## ⚡ 快速启动

### 本地开发
```bash
# 1. 安装依赖
uv add fastapi uvicorn celery redis

# 2. 启动Redis
redis-server

# 3. 启动应用
uvicorn app:app --host 127.0.0.1 --port 7860

# 4. 启动Celery Worker
celery -A celery_task.celery worker --loglevel=info
```

### 使用Circus管理进程
```bash
# 启动所有服务
circusd circus_local.ini

# 查看状态
circusctl status

# 重启服务
circusctl restart yiya-ai-bot
```

## 🔍 健康检查

```bash
# 本地检查
curl http://localhost:7860/health

# 开发环境检查
curl http://dev.bot.yiya-ai.com/health
```

## 🏗️ 项目结构

```
yiya-ai-bot/
├── docs/                    # 📚 项目文档
├── api/                     # 🌐 API路由
├── celery_task/            # ⚡ 异步任务
├── config/                 # ⚙️ 配置文件
├── models/                 # 📊 数据模型
├── utils/                  # 🛠️ 工具函数
├── APP-META/               # 🐳 Docker配置
│   ├── docker-config/      # 构建配置
│   └── k8s/               # K8s配置
├── logs/                   # 📝 日志文件
└── app.py                  # 🚀 应用入口
```

## 🤝 贡献指南

1. 📖 阅读 [项目启动流程说明](docs/项目启动流程说明.md) 了解架构
2. 🔧 按照 [快速开始指南](docs/快速开始指南.md) 搭建环境
3. 🌿 使用 [Git分支管理](docs/new-branch-commands.md) 创建功能分支
4. 💻 开发完成后提交代码，CI/CD会自动构建部署

## 📞 支持

如有问题请查看相关文档或联系开发团队。

---

**⭐ 推荐首先阅读 [项目启动流程说明](docs/项目启动流程说明.md)，这是理解整个项目的核心文档！**