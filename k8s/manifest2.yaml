apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: yiya-ai-bot
  name: yiya-ai-bot
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: yiya-ai-bot
  template:
    metadata:
      labels:
        app: yiya-ai-bot
    spec:
      containers:
      - image: yiya-acr-registry-vpc.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot:2024-12-23-15-38-07
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 120
          periodSeconds: 3
          successThreshold: 1
          tcpSocket:
            port: 7860
          timeoutSeconds: 1
        name: yiya-ai-bot
        ports:
        - containerPort: 7860
          protocol: TCP
        readinessProbe:
          failureThreshold: 10
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          tcpSocket:
            port: 7860
          timeoutSeconds: 5
        resources:
          limits:
            cpu: "2"
            memory: 8000Mi
            aliyun.com/gpu-mem: "6"
          requests:
            cpu: "1"
            memory: 4000Mi
        volumeMounts:
        - mountPath: /root/.paddlehub
          name: volume-1734617308831
        - mountPath: /root/.cache
          name: volume-1734617318026
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: yiya-acr-key
      restartPolicy: Always
      schedulerName: default-scheduler
      volumes:
      - name: volume-1734617308831
        persistentVolumeClaim:
          claimName: yiya-ai-bot-paddlehub-pvc
      - name: volume-1734617318026
        persistentVolumeClaim:
          claimName: yiya-ai-bot-pvc
