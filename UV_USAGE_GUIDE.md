# UV 使用指南

UV是一个极快的Python包管理器，提供了完整的Python项目管理解决方案。

## 📋 目录

- [UV命令分类](#uv命令分类)
- [核心概念](#核心概念)
- [常用工作流程](#常用工作流程)
- [实用技巧](#实用技巧)
- [常见问题](#常见问题)

## 🎯 UV命令分类

### 🌍 全局管理命令

#### Python版本管理
```bash
uv python list            # 列出所有可用Python版本
uv python install 3.12    # 全局安装Python版本
uv python uninstall 3.11  # 卸载Python版本
uv python find 3.12       # 查找Python安装
uv python pin 3.12        # 固定项目Python版本
uv python dir             # 显示Python安装目录
```

#### 全局工具管理
```bash
uv tool install black     # 全局安装工具
uv tool list              # 列出已安装的全局工具
uv tool upgrade black     # 升级全局工具
uv tool uninstall black   # 卸载全局工具
uv tool dir               # 显示工具安装目录
uv tool update-shell      # 更新PATH环境变量
```

#### 缓存和自身管理
```bash
uv cache clean           # 清理全局缓存
uv cache dir             # 显示缓存目录
uv self update           # 更新uv本身
uv version               # 显示uv版本
```

### 📁 项目管理命令

#### 项目初始化和依赖
```bash
uv init                  # 创建新项目
uv add requests          # 添加项目依赖
uv add --group dev ruff  # 添加开发依赖
uv remove requests       # 移除项目依赖
uv sync                  # 同步项目环境
uv sync --group dev      # 同步包含开发依赖
uv lock                  # 更新项目锁文件
uv lock --upgrade        # 升级到最新兼容版本
```

#### 项目环境和运行
```bash
uv venv                  # 创建项目虚拟环境
uv run python script.py # 在项目环境中运行Python
uv run pytest           # 在项目环境中运行测试
```

#### 项目信息
```bash
uv tree                  # 显示项目依赖树
uv export                # 导出依赖到其他格式
uv build                 # 构建Python包
uv publish               # 发布包到PyPI
```

### ⚡ 一次性用完即弃命令

#### 临时运行工具（不安装）
```bash
uv tool run black .              # 临时运行代码格式化
uv tool run ruff check .         # 临时运行代码检查
uv tool run httpie GET api.com   # 临时发HTTP请求
uv tool run cowsay -t "Hello"    # 临时运行有趣工具
uv tool run youtube-dl "url"     # 临时下载视频
```

#### 临时环境运行
```bash
# 在临时环境中运行，不影响项目
uv run --with requests python -c "import requests; print(requests.get('https://api.github.com').status_code)"
uv run --with pandas,numpy python script.py
```

## 🔑 核心概念

### 文件说明

#### `pyproject.toml` - 项目配置文件
```toml
[project]
name = "my-project"
version = "0.1.0"
requires-python = ">=3.11"
dependencies = [
    "requests>=2.28.0",
    "fastapi>=0.100.0",
]

[dependency-groups]
dev = [
    "ruff>=0.11.11",
    "pytest>=7.0.0",
]
```

#### `uv.lock` - 精确版本锁定文件
- 机器生成，包含所有传递依赖的精确版本
- 确保团队环境完全一致
- **必须提交到版本控制**

### 依赖管理层次

1. **`uv lock`** - 计算依赖关系，更新"购物清单"（uv.lock）
2. **`uv sync`** - 根据"购物清单"实际安装包到环境
3. **`uv add/remove`** - 会自动执行lock和sync

## 🚀 常用工作流程

### 新项目开始
```bash
# 创建新项目
uv init my-project
cd my-project

# 添加依赖
uv add fastapi uvicorn

# 添加开发依赖
uv add --group dev pytest ruff black

# 运行项目
uv run python app.py
```

### 克隆现有项目
```bash
git clone project-repo
cd project-repo

# 同步环境（安装所有依赖）
uv sync

# 包含开发依赖
uv sync --group dev

# 运行项目
uv run python app.py
```

### 团队协作
```bash
# 开发者A：添加新依赖
uv add requests
git add pyproject.toml uv.lock
git commit -m "Add requests dependency"
git push

# 开发者B：同步环境
git pull
uv sync  # 自动安装新依赖
```

### 代码质量检查
```bash
# 方式1：使用项目依赖
uv run ruff check .
uv run ruff format .
uv run pytest

# 方式2：使用全局工具
uv tool install ruff black pytest
ruff check .
black .
pytest

# 方式3：临时使用
uv tool run ruff check .
uv tool run black .
```

## 💡 实用技巧

### Python环境问题解决

如果遇到 `python: command not found`：

```bash
# 查看可用Python版本
uv python list

# 安装Python版本
uv python install 3.12

# 在项目中使用
uv run python --version

# 或激活虚拟环境
source .venv/bin/activate
python --version
```

### 依赖管理最佳实践

1. **项目依赖 vs 全局工具**：
   ```bash
   # 项目依赖（推荐用于库和框架）
   uv add requests fastapi pandas
   
   # 全局工具（推荐用于开发工具）
   uv tool install ruff black mypy
   ```

2. **开发依赖分组**：
   ```bash
   # 添加到开发组
   uv add --group dev pytest ruff black
   
   # 生产环境只安装主要依赖
   uv sync
   
   # 开发环境安装所有依赖
   uv sync --group dev
   ```

### 常用全局工具推荐

```bash
# 代码质量工具
uv tool install ruff black mypy

# 实用工具
uv tool install httpie rich-cli cookiecutter

# 开发工具
uv tool install jupyter pre-commit poetry
```

## ❓ 常见问题

### Q: `uv sync` 和 `uv lock` 什么时候用？

**A:** 
- `uv lock` - 更新依赖关系计算，生成uv.lock文件
- `uv sync` - 根据uv.lock实际安装包到环境
- 大多数时候用 `uv add/remove`（会自动lock和sync）

### Q: 给同事代码时需要提供哪些文件？

**A:** 两个文件都要：
- `pyproject.toml` - 项目配置和依赖声明
- `uv.lock` - 精确版本锁定

### Q: 项目依赖 vs 全局工具如何选择？

**A:**
- **项目依赖**：确保团队版本一致，项目自包含
- **全局工具**：个人开发便利，任何地方都能用
- **建议**：开发工具可以两种都装

### Q: 如何在不同环境运行代码？

**A:**
```bash
# 项目环境
uv run python script.py

# 激活虚拟环境
source .venv/bin/activate
python script.py

# 临时环境
uv run --with requests python script.py
```

---

## 📚 更多信息

- [UV官方文档](https://docs.astral.sh/uv/)
- [UV GitHub仓库](https://github.com/astral-sh/uv)

---

*最后更新：2025-07-27*
