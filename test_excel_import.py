#!/usr/bin/env python3
# test_excel_import.py
"""
Excel 导入系统测试脚本
用于测试 Celery + Redis 的 Excel 导入功能
"""

import time
import requests
import json
from typing import Optional


class ExcelImportTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        
    def submit_task(self, excel_url: str, sheets_to_import: list) -> Optional[str]:
        """提交 Excel 导入任务"""
        url = f"{self.base_url}/importer/refresh-from-url-async"
        
        payload = {
            "excel_url": excel_url,
            "sheets_to_import": sheets_to_import
        }
        
        try:
            print(f"提交任务到: {url}")
            print(f"请求数据: {json.dumps(payload, indent=2, ensure_ascii=False)}")
            
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('code') == 200:
                task_id = result['data']['task_id']
                print(f"✅ 任务提交成功，Task ID: {task_id}")
                return task_id
            else:
                print(f"❌ 任务提交失败: {result}")
                return None
                
        except Exception as e:
            print(f"❌ 提交任务时发生错误: {e}")
            return None
    
    def check_status(self, task_id: str) -> dict:
        """查询任务状态"""
        url = f"{self.base_url}/importer/import-status/{task_id}"
        
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            return result
            
        except Exception as e:
            print(f"❌ 查询状态时发生错误: {e}")
            return {"error": str(e)}
    
    def poll_until_complete(self, task_id: str, max_wait_seconds: int = 300) -> dict:
        """轮询直到任务完成"""
        print(f"开始轮询任务状态，最大等待时间: {max_wait_seconds} 秒")
        
        start_time = time.time()
        last_progress = -1
        
        while time.time() - start_time < max_wait_seconds:
            result = self.check_status(task_id)
            
            if "error" in result:
                return result
            
            data = result.get('data', {})
            status = data.get('status', 'UNKNOWN')
            progress = data.get('progress', 0)
            current_stage = data.get('current_stage', '')
            
            # 只在进度变化时打印
            if progress != last_progress:
                elapsed = time.time() - start_time
                print(f"[{elapsed:.1f}s] 状态: {status}, 进度: {progress}%, 阶段: {current_stage}")
                last_progress = progress
            
            if status == 'SUCCESS':
                print("✅ 任务成功完成！")
                oss_url = data.get('oss_url')
                if oss_url:
                    print(f"📁 Excel 文件下载链接: {oss_url}")
                return result
            elif status == 'FAILURE':
                print("❌ 任务执行失败！")
                error = data.get('error', '未知错误')
                print(f"错误信息: {error}")
                return result
            
            time.sleep(2)  # 每 2 秒轮询一次
        
        print("⏰ 轮询超时")
        return {"error": "轮询超时"}
    
    def get_download_link(self, task_id: str) -> Optional[str]:
        """获取下载链接"""
        url = f"{self.base_url}/importer/download-excel/{task_id}"
        
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('code') == 200:
                oss_url = result['data']['oss_url']
                print(f"✅ 获取下载链接成功: {oss_url}")
                return oss_url
            else:
                print(f"❌ 获取下载链接失败: {result}")
                return None
                
        except Exception as e:
            print(f"❌ 获取下载链接时发生错误: {e}")
            return None
    
    def run_test(self, excel_url: str, sheets_to_import: list):
        """运行完整测试"""
        print("=" * 60)
        print("Excel 导入系统测试")
        print("=" * 60)
        
        # 1. 提交任务
        task_id = self.submit_task(excel_url, sheets_to_import)
        if not task_id:
            return
        
        print("\n" + "-" * 40)
        
        # 2. 轮询直到完成
        result = self.poll_until_complete(task_id)
        
        print("\n" + "-" * 40)
        
        # 3. 获取下载链接（如果任务成功）
        if result.get('data', {}).get('status') == 'SUCCESS':
            download_link = self.get_download_link(task_id)
            
            if download_link:
                print(f"\n🎉 测试完成！Excel 文件已生成并上传到 OSS")
                print(f"下载链接: {download_link}")
            else:
                print(f"\n⚠️ 任务完成但获取下载链接失败")
        else:
            print(f"\n❌ 测试失败")
        
        print("=" * 60)


def main():
    """主函数"""
    # 测试配置
    tester = ExcelImportTester("http://localhost:8000")
    
    # 示例测试数据
    test_excel_url = "https://example.com/test.xlsx"  # 替换为实际的 Excel 文件 URL
    test_sheets = ["Sheet1", "Sheet2"]  # 替换为实际的 Sheet 名称
    
    print("请确保：")
    print("1. FastAPI 应用已启动")
    print("2. Celery Worker 已启动")
    print("3. Redis 服务正常运行")
    print("4. 数据库连接正常")
    print("5. OSS 配置正确")
    print()
    
    # 获取用户输入
    excel_url = input(f"请输入 Excel 文件 URL (默认: {test_excel_url}): ").strip()
    if not excel_url:
        excel_url = test_excel_url
    
    sheets_input = input(f"请输入要导入的 Sheet 名称，用逗号分隔 (默认: {','.join(test_sheets)}): ").strip()
    if sheets_input:
        sheets_to_import = [s.strip() for s in sheets_input.split(',')]
    else:
        sheets_to_import = test_sheets
    
    print()
    
    # 运行测试
    tester.run_test(excel_url, sheets_to_import)


if __name__ == "__main__":
    main()
