#!/usr/bin/env python3
"""
测试Celery Redis超时修复效果
"""

import time
import json
from celery_task.celery import celery_app as app
from celery_task.excel_import_task import import_excel_task

def test_task_submission():
    """测试任务提交和状态查询"""
    print("=== 测试Celery任务提交和状态查询 ===")
    
    # 提交一个测试任务
    test_url = "https://example.com/test.xlsx"
    test_sheets = ["Sheet1"]
    
    print(f"提交任务: {test_url}")
    result = import_excel_task.delay(test_url, test_sheets)
    task_id = result.id
    print(f"任务ID: {task_id}")
    
    # 监控任务状态
    print("\n监控任务状态:")
    max_wait_time = 60  # 最多等待60秒
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            # 获取任务状态
            status = result.status
            print(f"[{time.strftime('%H:%M:%S')}] 任务状态: {status}")
            
            if status in ['SUCCESS', 'FAILURE']:
                print(f"任务完成，最终状态: {status}")
                if status == 'FAILURE':
                    print(f"错误信息: {result.result}")
                break
            elif status == 'PENDING':
                print("任务等待中...")
            else:
                print(f"任务进行中: {status}")
            
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ 获取任务状态失败: {e}")
            break
    else:
        print("⚠️  任务超时")
    
    return task_id

def test_redis_connection_stability():
    """测试Redis连接稳定性"""
    print("\n=== 测试Redis连接稳定性 ===")
    
    import redis
    from config.settings import settings
    
    # 创建Redis客户端
    client = redis.Redis(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        password=settings.REDIS_PASSWORD,
        db=settings.REDIS_DB,
        socket_connect_timeout=30,
        socket_timeout=60,
        socket_keepalive=True,
        decode_responses=True
    )
    
    print("执行连续Redis操作测试...")
    
    try:
        for i in range(20):
            # 模拟Celery的操作
            test_key = f"test_stability_{i}"
            test_data = {
                "status": "PROGRESS",
                "result": {"progress": i * 5},
                "timestamp": time.time()
            }
            
            # 写入
            client.set(test_key, json.dumps(test_data), ex=60)
            
            # 读取
            retrieved = client.get(test_key)
            if retrieved:
                data = json.loads(retrieved)
                print(f"  操作 {i+1}/20: ✅ 成功 (进度: {data['result']['progress']}%)")
            else:
                print(f"  操作 {i+1}/20: ❌ 读取失败")
            
            # 删除
            client.delete(test_key)
            
            time.sleep(0.5)  # 模拟实际使用间隔
        
        print("✅ Redis连接稳定性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Redis连接稳定性测试失败: {e}")
        return False

def check_current_redis_status():
    """检查当前Redis状态"""
    print("\n=== 当前Redis状态 ===")
    
    import redis
    from config.settings import settings
    
    try:
        client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            password=settings.REDIS_PASSWORD,
            db=settings.REDIS_DB,
            decode_responses=True
        )
        
        info = client.info()
        
        print(f"连接的客户端数: {info.get('connected_clients', 'N/A')}")
        print(f"阻塞的客户端数: {info.get('blocked_clients', 'N/A')}")
        print(f"内存使用: {info.get('used_memory_human', 'N/A')}")
        print(f"QPS: {info.get('instantaneous_ops_per_sec', 'N/A')}")
        
        # 检查Celery任务
        task_keys = client.keys('celery-task-meta-*')
        print(f"当前Celery任务数: {len(task_keys)}")
        
        # 检查队列
        queue_length = client.llen('excel_import_queue')
        print(f"队列长度: {queue_length}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查Redis状态失败: {e}")
        return False

def main():
    """主函数"""
    print("Celery Redis超时修复测试")
    print("=" * 50)
    
    # 检查当前状态
    status_ok = check_current_redis_status()
    if not status_ok:
        print("Redis状态检查失败，退出测试")
        return
    
    # 测试Redis连接稳定性
    stability_ok = test_redis_connection_stability()
    if not stability_ok:
        print("Redis连接稳定性测试失败")
        return
    
    # 测试任务提交（可选）
    print("\n是否要测试任务提交? (这会提交一个实际的任务)")
    response = input("输入 'y' 继续，其他键跳过: ").strip().lower()
    
    if response == 'y':
        task_id = test_task_submission()
        print(f"\n测试任务ID: {task_id}")
    else:
        print("跳过任务提交测试")
    
    print("\n=== 测试总结 ===")
    print("✅ Redis状态检查: 通过" if status_ok else "❌ Redis状态检查: 失败")
    print("✅ 连接稳定性: 通过" if stability_ok else "❌ 连接稳定性: 失败")
    
    if status_ok and stability_ok:
        print("\n🎉 所有测试通过！Redis超时问题已修复。")
        print("\n修复要点:")
        print("1. 增加了socket_timeout到60秒")
        print("2. 启用了socket_keepalive")
        print("3. 配置了连接池参数")
        print("4. 添加了重试机制")
    else:
        print("\n⚠️  部分测试失败，可能仍存在问题。")

if __name__ == "__main__":
    main()
