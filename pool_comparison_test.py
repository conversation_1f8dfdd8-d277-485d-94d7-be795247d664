#!/usr/bin/env python3
"""
Celery 线程池 vs 进程池性能对比测试
"""

import time
import psutil
import subprocess
import threading
from datetime import datetime


class PoolPerformanceTest:
    def __init__(self):
        self.results = {}
    
    def measure_memory_usage(self, process_name="celery"):
        """测量内存使用情况"""
        total_memory = 0
        process_count = 0
        
        for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
            try:
                if process_name in proc.info['name'].lower():
                    total_memory += proc.info['memory_info'].rss
                    process_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        return {
            'total_memory_mb': total_memory / 1024 / 1024,
            'process_count': process_count,
            'avg_memory_mb': total_memory / 1024 / 1024 / max(process_count, 1)
        }
    
    def test_startup_time(self, pool_type):
        """测试启动时间"""
        print(f"\n测试 {pool_type} 启动时间...")
        
        start_time = time.time()
        
        # 启动Celery Worker
        cmd = [
            "celery", "-A", "celery_task", "worker",
            f"--pool={pool_type}",
            "--concurrency=4",
            "--loglevel=ERROR",  # 减少日志输出
            "--without-gossip",  # 禁用gossip
            "--without-mingle",  # 禁用mingle
            "--without-heartbeat"  # 禁用心跳
        ]
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待Worker准备就绪
        ready = False
        timeout = 30  # 30秒超时
        
        while not ready and time.time() - start_time < timeout:
            try:
                # 检查Worker是否响应
                check_cmd = ["celery", "-A", "celery_task", "inspect", "ping"]
                result = subprocess.run(
                    check_cmd, 
                    capture_output=True, 
                    text=True, 
                    timeout=5
                )
                
                if result.returncode == 0 and "pong" in result.stdout.lower():
                    ready = True
                    break
                    
            except subprocess.TimeoutExpired:
                pass
            
            time.sleep(0.5)
        
        startup_time = time.time() - start_time
        
        # 测量内存使用
        time.sleep(2)  # 等待稳定
        memory_info = self.measure_memory_usage()
        
        # 关闭Worker
        process.terminate()
        process.wait(timeout=10)
        
        return {
            'startup_time': startup_time,
            'ready': ready,
            'memory_info': memory_info
        }
    
    def test_task_throughput(self, pool_type, num_tasks=10):
        """测试任务吞吐量"""
        print(f"\n测试 {pool_type} 任务吞吐量 ({num_tasks} 个任务)...")
        
        # 这里应该启动Worker并提交任务
        # 由于需要实际的Celery环境，这里只是示例框架
        
        return {
            'tasks_per_second': 0,  # 实际测试中计算
            'avg_task_time': 0,
            'memory_peak': 0
        }
    
    def run_comparison(self):
        """运行完整对比测试"""
        print("Celery 线程池 vs 进程池性能对比测试")
        print("=" * 60)
        
        pools = ['threads', 'prefork']
        
        for pool_type in pools:
            print(f"\n{'='*20} 测试 {pool_type.upper()} 池 {'='*20}")
            
            try:
                # 测试启动性能
                startup_result = self.test_startup_time(pool_type)
                self.results[pool_type] = startup_result
                
                print(f"启动时间: {startup_result['startup_time']:.2f}秒")
                print(f"启动成功: {'是' if startup_result['ready'] else '否'}")
                print(f"内存使用: {startup_result['memory_info']['total_memory_mb']:.1f}MB")
                print(f"进程数量: {startup_result['memory_info']['process_count']}")
                
            except Exception as e:
                print(f"测试 {pool_type} 时出错: {e}")
                self.results[pool_type] = {'error': str(e)}
        
        # 输出对比结果
        self.print_comparison()
    
    def print_comparison(self):
        """打印对比结果"""
        print("\n" + "="*60)
        print("对比结果总结")
        print("="*60)
        
        if 'threads' in self.results and 'prefork' in self.results:
            threads = self.results['threads']
            prefork = self.results['prefork']
            
            if 'error' not in threads and 'error' not in prefork:
                print(f"启动时间对比:")
                print(f"  线程池: {threads['startup_time']:.2f}秒")
                print(f"  进程池: {prefork['startup_time']:.2f}秒")
                print(f"  差异: {abs(threads['startup_time'] - prefork['startup_time']):.2f}秒")
                
                print(f"\n内存使用对比:")
                print(f"  线程池: {threads['memory_info']['total_memory_mb']:.1f}MB")
                print(f"  进程池: {prefork['memory_info']['total_memory_mb']:.1f}MB")
                print(f"  差异: {abs(threads['memory_info']['total_memory_mb'] - prefork['memory_info']['total_memory_mb']):.1f}MB")
                
                # 推荐
                print(f"\n推荐分析:")
                if threads['startup_time'] < prefork['startup_time']:
                    print("✅ 线程池启动更快")
                else:
                    print("✅ 进程池启动更快")
                
                if threads['memory_info']['total_memory_mb'] < prefork['memory_info']['total_memory_mb']:
                    print("✅ 线程池内存使用更少")
                else:
                    print("✅ 进程池内存使用更少")


def analyze_your_workload():
    """分析你的工作负载特点"""
    print("\n" + "="*60)
    print("你的Excel导入任务特点分析")
    print("="*60)
    
    workload_analysis = {
        "任务类型": "I/O密集型",
        "主要操作": [
            "网络下载Excel文件 (I/O等待)",
            "读取Excel数据 (磁盘I/O)",
            "数据库批量写入 (数据库I/O)",
            "数据转换处理 (少量CPU)"
        ],
        "并发特点": [
            "大部分时间在等待I/O操作",
            "CPU使用率相对较低",
            "内存使用稳定",
            "网络和数据库是瓶颈"
        ],
        "最佳选择": "线程池 (threads)",
        "原因": [
            "I/O等待时线程会释放GIL",
            "多个线程可以并发等待不同I/O",
            "内存使用更少",
            "启动和切换开销更小"
        ]
    }
    
    for key, value in workload_analysis.items():
        print(f"\n{key}:")
        if isinstance(value, list):
            for item in value:
                print(f"  • {item}")
        else:
            print(f"  {value}")


def main():
    """主函数"""
    print("Celery Pool 类型选择分析工具")
    print("=" * 50)
    
    # 分析工作负载
    analyze_your_workload()
    
    # 询问是否运行性能测试
    print("\n" + "="*60)
    response = input("是否运行实际性能测试？(需要停止当前Celery Worker) [y/N]: ")
    
    if response.lower() in ['y', 'yes']:
        print("\n⚠️  请先停止当前运行的Celery Worker")
        input("停止后按回车继续...")
        
        tester = PoolPerformanceTest()
        tester.run_comparison()
    else:
        print("\n跳过性能测试。")
    
    # 总结建议
    print("\n" + "="*60)
    print("总结建议")
    print("="*60)
    print("✅ 对于你的Excel导入任务，使用线程池是正确的选择")
    print("✅ 线程池在I/O密集型任务中表现更好")
    print("✅ 如果需要更高并发，可以考虑增加 --concurrency 参数")
    print("✅ 如果是CPU密集型任务，才考虑使用进程池")


if __name__ == "__main__":
    main()
