#!/usr/bin/env python3
"""
Celery并发处理测试脚本
测试多个Excel导入任务的并行处理能力
"""

import time
import threading
import requests
import json
from datetime import datetime
from typing import List, Dict
import concurrent.futures


class ConcurrentTaskTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.results = []
        self.lock = threading.Lock()
    
    def submit_single_task(self, task_id: int, excel_url: str, sheets_to_import: List[str]) -> Dict:
        """提交单个任务并记录时间"""
        start_time = time.time()
        
        try:
            url = f"{self.base_url}/importer/refresh-from-url-async"
            payload = {
                "excel_url": excel_url,
                "sheets_to_import": sheets_to_import
            }
            
            print(f"[任务{task_id}] {datetime.now().strftime('%H:%M:%S.%f')[:-3]} - 开始提交")
            
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()
            
            submit_time = time.time()
            result = response.json()
            
            if result.get('code') == 200:
                celery_task_id = result['data']['task_id']
                print(f"[任务{task_id}] {datetime.now().strftime('%H:%M:%S.%f')[:-3]} - 提交成功，Celery Task ID: {celery_task_id}")
                
                # 记录结果
                task_result = {
                    'test_task_id': task_id,
                    'celery_task_id': celery_task_id,
                    'submit_time': submit_time - start_time,
                    'submitted_at': datetime.now().strftime('%H:%M:%S.%f')[:-3],
                    'status': 'submitted'
                }
                
                with self.lock:
                    self.results.append(task_result)
                
                return task_result
            else:
                print(f"[任务{task_id}] 提交失败: {result}")
                return None
                
        except Exception as e:
            print(f"[任务{task_id}] 提交异常: {e}")
            return None
    
    def check_task_status(self, celery_task_id: str) -> Dict:
        """检查任务状态"""
        try:
            url = f"{self.base_url}/importer/task-status/{celery_task_id}"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"检查任务状态失败: {e}")
            return None
    
    def monitor_tasks(self):
        """监控所有已提交的任务"""
        print("\n" + "="*60)
        print("开始监控任务执行状态...")
        print("="*60)
        
        completed_tasks = set()
        start_monitor_time = time.time()
        
        while len(completed_tasks) < len(self.results):
            for task_result in self.results:
                if task_result['celery_task_id'] in completed_tasks:
                    continue
                
                status_result = self.check_task_status(task_result['celery_task_id'])
                if status_result and status_result.get('code') == 200:
                    task_data = status_result['data']
                    current_status = task_data.get('status', 'UNKNOWN')
                    progress = task_data.get('progress', 0)
                    stage = task_data.get('current_stage', '未知')
                    
                    print(f"[任务{task_result['test_task_id']}] {datetime.now().strftime('%H:%M:%S')} - "
                          f"状态: {current_status}, 进度: {progress}%, 阶段: {stage}")
                    
                    if current_status in ['SUCCESS', 'FAILURE']:
                        completed_tasks.add(task_result['celery_task_id'])
                        task_result['completed_at'] = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        task_result['final_status'] = current_status
                        
                        if current_status == 'SUCCESS':
                            # 获取执行时间信息
                            result_data = task_data.get('result', {})
                            task_result['total_time'] = result_data.get('total_time_seconds', 0)
                            task_result['successful_imports'] = result_data.get('successful_imports', 0)
                            task_result['failed_imports'] = result_data.get('failed_imports', 0)
            
            if len(completed_tasks) < len(self.results):
                time.sleep(2)  # 每2秒检查一次
            
            # 超时保护（10分钟）
            if time.time() - start_monitor_time > 600:
                print("监控超时，停止监控")
                break
    
    def run_concurrent_test(self, num_tasks: int, excel_url: str, sheets_to_import: List[str]):
        """运行并发测试"""
        print("="*80)
        print(f"Celery 并发处理测试 - {num_tasks} 个并发任务")
        print("="*80)
        print(f"测试URL: {excel_url}")
        print(f"导入表: {sheets_to_import}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 1. 并发提交任务
        print("阶段1: 并发提交任务")
        print("-"*40)
        
        start_time = time.time()
        
        # 使用线程池并发提交
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_tasks) as executor:
            futures = []
            for i in range(num_tasks):
                future = executor.submit(
                    self.submit_single_task, 
                    i+1, 
                    excel_url, 
                    sheets_to_import
                )
                futures.append(future)
            
            # 等待所有提交完成
            concurrent.futures.wait(futures)
        
        submit_end_time = time.time()
        total_submit_time = submit_end_time - start_time
        
        print(f"\n所有任务提交完成，总耗时: {total_submit_time:.2f}秒")
        print(f"成功提交: {len([r for r in self.results if r])} 个任务")
        
        # 2. 监控任务执行
        if self.results:
            self.monitor_tasks()
        
        # 3. 分析结果
        self.analyze_results(total_submit_time)
    
    def analyze_results(self, total_submit_time: float):
        """分析测试结果"""
        print("\n" + "="*60)
        print("测试结果分析")
        print("="*60)
        
        successful_tasks = [r for r in self.results if r.get('final_status') == 'SUCCESS']
        failed_tasks = [r for r in self.results if r.get('final_status') == 'FAILURE']
        
        print(f"总提交任务数: {len(self.results)}")
        print(f"成功完成任务: {len(successful_tasks)}")
        print(f"失败任务数: {len(failed_tasks)}")
        print(f"任务提交总时间: {total_submit_time:.2f}秒")
        
        if successful_tasks:
            avg_task_time = sum(r.get('total_time', 0) for r in successful_tasks) / len(successful_tasks)
            print(f"平均任务执行时间: {avg_task_time:.2f}秒")
            
            # 分析并发效果
            print("\n并发执行分析:")
            print("-"*30)
            for task in successful_tasks:
                print(f"任务{task['test_task_id']}: "
                      f"提交于 {task['submitted_at']}, "
                      f"完成于 {task.get('completed_at', 'N/A')}, "
                      f"执行时间 {task.get('total_time', 0):.1f}秒")
        
        print("\n" + "="*60)


def main():
    """主函数"""
    tester = ConcurrentTaskTester("http://localhost:8000")
    
    # 测试配置
    test_excel_url = "https://f-dev.yiya-ai.com/files/53f5c73c-5b27-4592-ade4-7ee384ab37c2/file-preview"
    test_sheets = ["AE", "CM", "CP", "LAB", "VS", "PE", "ECG"]
    
    print("Celery 并发处理能力测试")
    print("="*50)
    print("请确保:")
    print("1. FastAPI 应用已启动")
    print("2. Celery Worker 已启动")
    print("3. Redis 服务正常运行")
    print()
    
    # 获取用户输入
    try:
        num_tasks = int(input("请输入并发任务数量 (建议2-8): ") or "3")
        if num_tasks < 1 or num_tasks > 20:
            print("任务数量应在1-20之间，使用默认值3")
            num_tasks = 3
    except ValueError:
        print("输入无效，使用默认值3")
        num_tasks = 3
    
    excel_url = input(f"Excel文件URL (回车使用默认): ").strip() or test_excel_url
    
    print(f"\n将提交 {num_tasks} 个并发任务进行测试...")
    input("按回车键开始测试...")
    
    # 运行测试
    tester.run_concurrent_test(num_tasks, excel_url, test_sheets)


if __name__ == "__main__":
    main()
