# 环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# 基本环境配置
ENV=local

# Nacos 配置
NACOS_SERVER=mse-450596b0-p.nacos-ans.mse.aliyuncs.com:8848
NACOS_NAMESPACE=yy-prod

# Nacos 连接配置
NACOS_TIMEOUT=30
NACOS_PULLING_TIMEOUT=30
NACOS_PULLING_CONFIG_SIZE=3000
NACOS_CALLBACK_THREAD_NUM=1

# Nacos 重试配置
NACOS_MAX_RETRIES=3
NACOS_RETRY_DELAY=2
NACOS_CONFIG_RETRY=2

# Nacos 故障转移配置
NACOS_ENABLE_FAILOVER=true
NACOS_FAILOVER_BASE=./nacos-failover
NACOS_SNAPSHOT_BASE=./nacos-snapshot
NACOS_NO_SNAPSHOT=false

# Nacos 功能开关
NACOS_ENABLE_WATCHER=true
NACOS_ENABLE_IN_CELERY=true

# 紧急情况下禁用 Nacos（设置为 true 可以完全禁用 Nacos）
DISABLE_NACOS=false

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=1
REDIS_TASK_DB=2

# RabbitMQ 配置（本地环境）
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
